// Backend ENUMs aligned with PostgreSQL schema
// These ENUMs must match exactly with the backend database ENUMs

/// Bubble request types from backend bubble_request_type ENUM
enum BubbleRequestType {
  invite,  // 'invite' - Invite user to bubble
  join,    // 'join' - Request to join bubble
  kick,    // 'kick' - Request to kick user from bubble
  start,   // 'start' - Request to start a new bubble
}

/// Request status from backend request_status ENUM
enum RequestStatus {
  pending,   // 'pending' - Request is waiting for response
  approved,  // 'approved' - Request was approved
  rejected,  // 'rejected' - Request was rejected
  expired,   // 'expired' - Request has expired
}

/// Vote type from backend vote_type ENUM
enum VoteType {
  approve,  // 'approve' - Vote to approve the request
  reject,   // 'reject' - Vote to reject the request
}

/// Friend request status from backend friend_request_status ENUM
enum FriendRequestStatus {
  pending,   // 'pending' - Friend request is waiting for response
  accepted,  // 'accepted' - Friend request was accepted
  declined,  // 'declined' - Friend request was declined
  expired,   // 'expired' - Friend request has expired
}

/// Contact request status from backend contact_request_status ENUM
enum ContactRequestStatus {
  pending,   // 'pending' - Contact request is waiting for response
  accepted,  // 'accepted' - Contact request was accepted
  declined,  // 'declined' - Contact request was declined
  expired,   // 'expired' - Contact request has expired
}

/// Notification types from backend notification_type ENUM
enum NotificationType {
  contactRequestReceived,
  contactRequestAccepted,
  contactRequestDeclined,
  friendRequestReceived,
  friendRequestAccepted,
  friendRequestDeclined,
  bubbleInviteReceived,
  bubbleInviteAccepted,
  bubbleInviteDeclined,
  bubbleJoinRequestReceived,
  bubbleJoinRequestAccepted,
  bubbleJoinRequestDeclined,
  bubbleKickoutRequestReceived,
  bubbleKickoutRequestAccepted,
  bubbleKickoutRequestDeclined,
  bubbleStartRequestReceived,
  bubbleStartRequestAccepted,
  bubbleStartRequestDeclined,
  bubbleExpired,
  bubbleDissolved,
  bubbleCallStarted,
  bubbleCallEnded,
  bubbleCallMissed,
  systemNotification,
}

/// User relationship types from backend user_relationships table
enum UserRelationshipType {
  none,        // 'none' - No relationship
  contact,     // 'contact' - Users are contacts
  bubbler,     // 'bubbler' - Users are in same bubble
  maybefriend, // 'maybefriend' - Potential friends from expired bubble
  friend,      // 'friend' - Users are friends
  block,       // 'block' - User is blocked
}

/// User relationship status from backend user_relationships table
enum UserRelationshipStatus {
  active,    // 'active' - Relationship is active
  inactive,  // 'inactive' - Relationship is inactive
  expired,   // 'expired' - Relationship has expired
}

// =============================================================================
// EXTENSION METHODS FOR BACKEND MAPPING
// =============================================================================

/// Extension for BubbleRequestType backend mapping
extension BubbleRequestTypeExtension on BubbleRequestType {
  String toBackendString() {
    switch (this) {
      case BubbleRequestType.invite: return 'invite';
      case BubbleRequestType.join: return 'join';
      case BubbleRequestType.kick: return 'kick';
      case BubbleRequestType.start: return 'start';
    }
  }

  static BubbleRequestType fromBackendString(String value) {
    switch (value.toLowerCase()) {
      case 'invite': return BubbleRequestType.invite;
      case 'join': return BubbleRequestType.join;
      case 'kick': return BubbleRequestType.kick;
      case 'start': return BubbleRequestType.start;
      default: throw ArgumentError('Invalid BubbleRequestType: $value');
    }
  }
}

/// Extension for RequestStatus backend mapping
extension RequestStatusExtension on RequestStatus {
  String toBackendString() {
    switch (this) {
      case RequestStatus.pending: return 'pending';
      case RequestStatus.approved: return 'approved';
      case RequestStatus.rejected: return 'rejected';
      case RequestStatus.expired: return 'expired';
    }
  }

  static RequestStatus fromBackendString(String value) {
    switch (value.toLowerCase()) {
      case 'pending': return RequestStatus.pending;
      case 'approved': return RequestStatus.approved;
      case 'rejected': return RequestStatus.rejected;
      case 'expired': return RequestStatus.expired;
      default: throw ArgumentError('Invalid RequestStatus: $value');
    }
  }
}

/// Extension for VoteType backend mapping
extension VoteTypeExtension on VoteType {
  String toBackendString() {
    switch (this) {
      case VoteType.approve: return 'approve';
      case VoteType.reject: return 'reject';
    }
  }

  static VoteType fromBackendString(String value) {
    switch (value.toLowerCase()) {
      case 'approve': return VoteType.approve;
      case 'reject': return VoteType.reject;
      default: throw ArgumentError('Invalid VoteType: $value');
    }
  }
}

/// Extension for FriendRequestStatus backend mapping
extension FriendRequestStatusExtension on FriendRequestStatus {
  String toBackendString() {
    switch (this) {
      case FriendRequestStatus.pending: return 'pending';
      case FriendRequestStatus.accepted: return 'accepted';
      case FriendRequestStatus.declined: return 'declined';
      case FriendRequestStatus.expired: return 'expired';
    }
  }

  static FriendRequestStatus fromBackendString(String value) {
    switch (value.toLowerCase()) {
      case 'pending': return FriendRequestStatus.pending;
      case 'accepted': return FriendRequestStatus.accepted;
      case 'declined': return FriendRequestStatus.declined;
      case 'expired': return FriendRequestStatus.expired;
      default: throw ArgumentError('Invalid FriendRequestStatus: $value');
    }
  }
}

/// Extension for ContactRequestStatus backend mapping
extension ContactRequestStatusExtension on ContactRequestStatus {
  String toBackendString() {
    switch (this) {
      case ContactRequestStatus.pending: return 'pending';
      case ContactRequestStatus.accepted: return 'accepted';
      case ContactRequestStatus.declined: return 'declined';
      case ContactRequestStatus.expired: return 'expired';
    }
  }

  static ContactRequestStatus fromBackendString(String value) {
    switch (value.toLowerCase()) {
      case 'pending': return ContactRequestStatus.pending;
      case 'accepted': return ContactRequestStatus.accepted;
      case 'declined': return ContactRequestStatus.declined;
      case 'expired': return ContactRequestStatus.expired;
      default: throw ArgumentError('Invalid ContactRequestStatus: $value');
    }
  }
}

/// Extension for NotificationType backend mapping
extension NotificationTypeExtension on NotificationType {
  String toBackendString() {
    return toString().split('.').last;
  }

  static NotificationType fromBackendString(String value) =>
    NotificationType.values.firstWhere(
      (type) => type.toBackendString() == value,
      orElse: () => throw ArgumentError('Invalid NotificationType: $value'),
    );
}

/// Extension for UserRelationshipType backend mapping
extension UserRelationshipTypeExtension on UserRelationshipType {
  String toBackendString() {
    switch (this) {
      case UserRelationshipType.none: return 'none';
      case UserRelationshipType.contact: return 'contact';
      case UserRelationshipType.bubbler: return 'bubbler';
      case UserRelationshipType.maybefriend: return 'maybefriend';
      case UserRelationshipType.friend: return 'friend';
      case UserRelationshipType.block: return 'block';
    }
  }

  static UserRelationshipType fromBackendString(String value) {
    switch (value.toLowerCase()) {
      case 'none': return UserRelationshipType.none;
      case 'contact': return UserRelationshipType.contact;
      case 'bubbler': return UserRelationshipType.bubbler;
      case 'maybefriend': return UserRelationshipType.maybefriend;
      case 'friend': return UserRelationshipType.friend;
      case 'block': return UserRelationshipType.block;
      default: throw ArgumentError('Invalid UserRelationshipType: $value');
    }
  }
}

/// Extension for UserRelationshipStatus backend mapping
extension UserRelationshipStatusExtension on UserRelationshipStatus {
  String toBackendString() {
    switch (this) {
      case UserRelationshipStatus.active: return 'active';
      case UserRelationshipStatus.inactive: return 'inactive';
      case UserRelationshipStatus.expired: return 'expired';
    }
  }

  static UserRelationshipStatus fromBackendString(String value) {
    switch (value.toLowerCase()) {
      case 'active': return UserRelationshipStatus.active;
      case 'inactive': return UserRelationshipStatus.inactive;
      case 'expired': return UserRelationshipStatus.expired;
      default: throw ArgumentError('Invalid UserRelationshipStatus: $value');
    }
  }
}

import 'package:equatable/equatable.dart';
import '../error/result.dart';
import 'value_objects.dart';

/// Bubble lifecycle status enum - matches backend bubble_status ENUM
enum BubbleLifecycleStatus {
  active,     // Operating normally, countdown active
  expired,    // Countdown reached zero, naturally concluded
  dissolved,  // Ended prematurely (< 2 members or explicit termination)
  archived,   // Historical data preserved
}

/// Member status within a bubble - ALIGNED WITH BACKEND bubble_member_status ENUM
enum BubbleMemberStatus {
  active,     // User is an active member ('active')
  left,       // User voluntarily left ('left')
  removed,    // User was removed/kicked out ('removed')
  pending,    // User has pending request ('pending')
  accepted,   // Request was accepted - transitional state ('accepted')
  declined,   // Request was declined - transitional state ('declined')
}

/// Reason for leaving a bubble
enum LeaveReason {
  voluntary,
  kickedOut,
}

/// Extension for backend-frontend status mapping
extension BubbleMemberStatusExtension on BubbleMemberStatus {
  String toBackendString() {
    switch (this) {
      case BubbleMemberStatus.active: return 'active';
      case BubbleMemberStatus.left: return 'left';
      case BubbleMemberStatus.removed: return 'removed';
      case BubbleMemberStatus.pending: return 'pending';
      case BubbleMemberStatus.accepted: return 'accepted';
      case BubbleMemberStatus.declined: return 'declined';
    }
  }

  static BubbleMemberStatus fromBackendString(String status) {
    switch (status.toLowerCase()) {
      case 'active': return BubbleMemberStatus.active;
      case 'left': return BubbleMemberStatus.left;
      case 'removed': return BubbleMemberStatus.removed;
      case 'pending': return BubbleMemberStatus.pending;
      case 'accepted': return BubbleMemberStatus.accepted;
      case 'declined': return BubbleMemberStatus.declined;
      default: return BubbleMemberStatus.active;
    }
  }
}

/// Vote for removing a member from bubble
class RemovalVote extends Equatable {

  const RemovalVote({
    required this.voterId,
    required this.targetMemberId,
    required this.voteTime,
  });
  final UserId voterId;
  final UserId targetMemberId;
  final DateTime voteTime;

  /// Create a removal vote with validation
  static Result<RemovalVote> create({
    required String voterId,
    required String targetMemberId,
    required DateTime voteTime,
  }) => UserId.create(voterId).flatMap((voterUserId) =>
      UserId.create(targetMemberId).flatMap((targetUserId) {
        // Validate vote time
        if (voteTime.isAfter(DateTime.now())) {
          return Result.failure(const ValidationError(
            field: 'voteTime',
            message: 'Vote time cannot be in the future',
          ),);
        }

        // Validate that voter and target are different
        if (voterUserId.value == targetUserId.value) {
          return Result.failure(const ValidationError(
            field: 'targetMemberId',
            message: 'User cannot vote to remove themselves',
          ),);
        }

        return Result.success(RemovalVote(
          voterId: voterUserId,
          targetMemberId: targetUserId,
          voteTime: voteTime,
        ),);
      }),
    );

  /// Check if vote is still valid (within 24 hours)
  bool isValid({Duration validityPeriod = const Duration(hours: 24)}) => DateTime.now().difference(voteTime) <= validityPeriod;

  @override
  List<Object?> get props => [voterId, targetMemberId, voteTime];
}

/// Member information within a bubble
class BubbleMemberEntity extends Equatable {

  const BubbleMemberEntity({
    required this.id,
    required this.name,
    required this.joinedAt, this.avatarUrl,
    this.isOnline = false,
    this.status = BubbleMemberStatus.active,
    this.unreadMessageCount = 0,
    this.color,
    this.leftAt,
    this.leaveReason,
  });
  final UserId id;
  final String name;
  final String? avatarUrl;
  final DateTime joinedAt;
  final bool isOnline;
  final BubbleMemberStatus status;
  final int unreadMessageCount;
  final String? color; // Member's assigned color in the bubble
  final DateTime? leftAt;
  final LeaveReason? leaveReason;

  /// Create a member with validation
  static Result<BubbleMemberEntity> create({
    required String id,
    required String name,
    required DateTime joinedAt, String? avatarUrl,
    bool isOnline = false,
    BubbleMemberStatus status = BubbleMemberStatus.active,
    int unreadMessageCount = 0,
    String? color,
    DateTime? leftAt,
    LeaveReason? leaveReason,
  }) => UserId.create(id).flatMap((userId) {
      // Validate name
      if (name.trim().isEmpty) {
        return Result.failure(const ValidationError(
          field: 'memberName',
          message: 'Member name cannot be empty',
        ),);
      }

      if (name.trim().length > 100) {
        return Result.failure(const ValidationError(
          field: 'memberName',
          message: 'Member name cannot exceed 100 characters',
        ),);
      }

      // Validate joined date
      if (joinedAt.isAfter(DateTime.now())) {
        return Result.failure(const ValidationError(
          field: 'joinedAt',
          message: 'Join date cannot be in the future',
        ),);
      }

      // Validate unread count
      if (unreadMessageCount < 0) {
        return Result.failure(const ValidationError(
          field: 'unreadMessageCount',
          message: 'Unread message count cannot be negative',
        ),);
      }

      // Validate that leftAt and leaveReason are consistent with status
      if (status == BubbleMemberStatus.left || status == BubbleMemberStatus.removed) {
        if (leftAt == null) {
          return Result.failure(const ValidationError(
            field: 'leftAt',
            message: 'leftAt is required when member has left or been kicked out',
          ),);
        }
        if (leaveReason == null) {
          return Result.failure(const ValidationError(
            field: 'leaveReason',
            message: 'leaveReason is required when member has left or been kicked out',
          ),);
        }
        // Validate that leave reason matches status
        if (status == BubbleMemberStatus.removed && leaveReason != LeaveReason.kickedOut) {
          return Result.failure(const ValidationError(
            field: 'leaveReason',
            message: 'leaveReason must be kickedOut when status is removed',
          ),);
        }
        if (status == BubbleMemberStatus.left && leaveReason != LeaveReason.voluntary) {
          return Result.failure(const ValidationError(
            field: 'leaveReason',
            message: 'leaveReason must be voluntary when status is left',
          ),);
        }
      } else {
        // For active, pending, accepted, declined members, leftAt and leaveReason should be null
        if (leftAt != null || leaveReason != null) {
          return Result.failure(const ValidationError(
            field: 'leftAt',
            message: 'leftAt and leaveReason must be null for active members',
          ),);
        }
      }

      return Result.success(BubbleMemberEntity(
        id: userId,
        name: name.trim(),
        avatarUrl: avatarUrl,
        joinedAt: joinedAt,
        isOnline: isOnline,
        status: status,
        unreadMessageCount: unreadMessageCount,
        color: color,
        leftAt: leftAt,
        leaveReason: leaveReason,
      ),);
    });

  /// Check if member is active - UPDATED FOR BACKEND ALIGNMENT
  bool get isActive => status == BubbleMemberStatus.active;

  /// Check if member has left the bubble (either voluntarily or kicked out)
  bool get hasLeft => status == BubbleMemberStatus.left || status == BubbleMemberStatus.removed;

  /// Check if member was kicked out/removed
  bool get wasKickedOut => status == BubbleMemberStatus.removed;

  /// Check if member left voluntarily
  bool get leftVoluntarily => status == BubbleMemberStatus.left && leaveReason == LeaveReason.voluntary;

  /// Get human-readable leave reason
  String? get leaveReasonDescription {
    switch (leaveReason) {
      case LeaveReason.voluntary:
        return 'Left voluntarily';
      case LeaveReason.kickedOut:
        return 'Removed by vote';
      case null:
        return null;
    }
  }

  /// Copy with updated values
  BubbleMemberEntity copyWith({
    String? name,
    String? avatarUrl,
    DateTime? joinedAt,
    bool? isOnline,
    BubbleMemberStatus? status,
    int? unreadMessageCount,
    String? color,
    bool clearAvatarUrl = false,
    bool clearColor = false,
    DateTime? leftAt,
    LeaveReason? leaveReason,
  }) => BubbleMemberEntity(
      id: id,
      name: name ?? this.name,
      avatarUrl: clearAvatarUrl ? null : (avatarUrl ?? this.avatarUrl),
      joinedAt: joinedAt ?? this.joinedAt,
      isOnline: isOnline ?? this.isOnline,
      status: status ?? this.status,
      unreadMessageCount: unreadMessageCount ?? this.unreadMessageCount,
      color: clearColor ? null : (color ?? this.color),
      leftAt: leftAt ?? this.leftAt,
      leaveReason: leaveReason ?? this.leaveReason,
    );

  @override
  List<Object?> get props => [
    id,
    name,
    avatarUrl,
    joinedAt,
    isOnline,
    status,
    unreadMessageCount,
    color,
    leftAt,
    leaveReason,
  ];
}

/// Bubble call information
class BubbleCallEntity extends Equatable {

  const BubbleCallEntity({
    required this.callId,
    required this.participants,
    required this.startedAt,
    this.isActive = true,
  });
  final String callId;
  final List<UserId> participants;
  final DateTime startedAt;
  final bool isActive;

  /// Create a call with validation
  static Result<BubbleCallEntity> create({
    required String callId,
    required List<String> participantIds,
    required DateTime startedAt,
    bool isActive = true,
  }) {
    // Validate call ID
    if (callId.trim().isEmpty) {
      return Result.failure(const ValidationError(
        field: 'callId',
        message: 'Call ID cannot be empty',
      ),);
    }

    // Validate participants
    if (participantIds.isEmpty) {
      return Result.failure(const ValidationError(
        field: 'participants',
        message: 'Call must have at least one participant',
      ),);
    }

    // Validate started date
    if (startedAt.isAfter(DateTime.now())) {
      return Result.failure(const ValidationError(
        field: 'startedAt',
        message: 'Call start time cannot be in the future',
      ),);
    }

    // Convert and validate participant IDs
    final userIdResults = participantIds.map(UserId.create).toList();
    final failedUserIds = userIdResults.where((result) => result.isFailure).toList();
    
    if (failedUserIds.isNotEmpty) {
      return Result.failure(failedUserIds.first.error);
    }

    final validUserIds = userIdResults.map((result) => result.data).toList();

    return Result.success(BubbleCallEntity(
      callId: callId.trim(),
      participants: validUserIds,
      startedAt: startedAt,
      isActive: isActive,
    ),);
  }

  /// Get call duration
  Duration get duration => DateTime.now().difference(startedAt);

  /// Get participant count
  int get participantCount => participants.length;

  @override
  List<Object?> get props => [callId, participants, startedAt, isActive];
}

/// Comprehensive bubble entity that consolidates all bubble-related functionality
class BubbleEntity extends Equatable {

  const BubbleEntity({
    required this.id,
    required this.name,
    required this.capacity,
    required this.members,
    required this.createdAt,
    this.endDate,
    this.activeCall,
    this.status = BubbleLifecycleStatus.active,
    this.activeVotes = const [],
  });
  static const int maxMembers = 5;
  static const int minActiveMembers = 2;

  final BubbleId id;
  final BubbleName name;
  final MemberCapacity capacity;
  final List<BubbleMemberEntity> members;
  final DateTime createdAt;
  final DateTime? endDate;
  final BubbleCallEntity? activeCall;
  final BubbleLifecycleStatus status;
  final List<RemovalVote> activeVotes;

  /// Create a bubble with comprehensive validation
  static Result<BubbleEntity> create({
    required String id,
    required String name,
    required DateTime createdAt, int? capacity,
    List<BubbleMemberEntity> members = const [],
    DateTime? endDate,
    BubbleCallEntity? activeCall,
    BubbleLifecycleStatus status = BubbleLifecycleStatus.active,
    List<RemovalVote> activeVotes = const [],
  }) {
    // Validate all value objects
    return BubbleId.create(id).flatMap((bubbleId) =>
      BubbleName.create(name).flatMap((bubbleName) =>
        MemberCapacity.create(capacity ?? maxMembers).flatMap((memberCapacity) {
          // Validate dates
          if (createdAt.isAfter(DateTime.now())) {
            return Result.failure(const ValidationError(
              field: 'createdAt',
              message: 'Creation date cannot be in the future',
            ),);
          }

          if (endDate != null && endDate.isBefore(createdAt)) {
            return Result.failure(const ValidationError(
              field: 'endDate',
              message: 'End date cannot be before creation date',
            ),);
          }

          // Validate business rules
          if (members.length > memberCapacity.value) {
            return Result.failure(BubbleCapacityExceededError(
              bubbleId: bubbleId.value,
              currentCount: members.length,
              maxCapacity: memberCapacity.value,
            ),);
          }

          // Validate active call participants are members
          if (activeCall != null) {
            final memberIds = members.map((m) => m.id.value).toSet();
            final invalidParticipants = activeCall.participants
                .where((p) => !memberIds.contains(p.value))
                .toList();
            
            if (invalidParticipants.isNotEmpty) {
              return Result.failure(const ValidationError(
                field: 'activeCall',
                message: 'All call participants must be bubble members',
              ),);
            }
          }

          // Validate votes are between existing members
          final memberIds = members.map((m) => m.id.value).toSet();
          for (final vote in activeVotes) {
            if (!memberIds.contains(vote.voterId.value) || 
                !memberIds.contains(vote.targetMemberId.value)) {
              return Result.failure(const ValidationError(
                field: 'activeVotes',
                message: 'All votes must be between existing members',
              ),);
            }
          }

          final bubble = BubbleEntity(
            id: bubbleId,
            name: bubbleName,
            capacity: memberCapacity,
            members: members,
            createdAt: createdAt,
            endDate: endDate,
            activeCall: activeCall,
            status: status,
            activeVotes: activeVotes,
          );

          // Check if bubble should be dissolved due to insufficient members
          final activeMemberCount = bubble.activeMembersCount;
          if (activeMemberCount < minActiveMembers && 
              activeMemberCount > 0 && 
              status == BubbleLifecycleStatus.active) {
            return Result.success(bubble.copyWith(status: BubbleLifecycleStatus.dissolved));
          }

          return Result.success(bubble);
        }),
      ),
    );
  }

  /// Business logic methods

  /// Check if user is a member (regardless of status)
  bool isMember(String userId) => members.any((member) => member.id.value == userId);

  /// Check if user is an active member
  bool isActiveMember(String userId) => 
    members.any((member) => member.id.value == userId && member.isActive);

  /// Get member by ID
  BubbleMemberEntity? getMember(String userId) {
    try {
      return members.firstWhere((member) => member.id.value == userId);
    } catch (e) {
      return null;
    }
  }

  /// Get active members only
  List<BubbleMemberEntity> get activeMembers => 
    members.where((member) => member.isActive).toList();

  /// Get active members count
  int get activeMembersCount => activeMembers.length;

  // Backward compatibility helpers from BubbleModel
  /// Check if bubble is full (alias for isAtCapacity)
  bool get isFull => isAtCapacity;
  
  /// Check if bubble has no members
  bool get isEmpty => members.isEmpty;
  
  /// Get total member count (including inactive)
  int get memberCount => members.length;
  
  /// Check if user is a member (alias for isMember for compatibility)
  bool hasMember(String userId) => isMember(userId);

  /// Check if bubble is active (not expired or dissolved)
  bool get isActive {
    if (status != BubbleLifecycleStatus.active) return false;
    if (endDate == null) return true;
    return DateTime.now().isBefore(endDate!);
  }

  /// Check if bubble is at capacity (active members)
  bool get isAtCapacity => activeMembersCount >= capacity.value;

  /// Check if bubble should be dissolved (less than minimum active members)
  bool get shouldDissolve => activeMembersCount < minActiveMembers && activeMembersCount > 0;

  /// Check if there's an active call
  bool get hasActiveCall => activeCall?.isActive == true;

  /// Get online members count
  int get onlineMembersCount => members.where((member) => member.isOnline).length;

  /// Get total unread messages count
  int get totalUnreadMessages => members.fold(0, (sum, member) => sum + member.unreadMessageCount);

  /// Calculate days since creation
  int get daysSinceCreation => DateTime.now().difference(createdAt).inDays;

  /// Calculate remaining days if bubble has an end date
  int? get daysRemaining {
    if (endDate == null) return null;
    final remaining = endDate!.difference(DateTime.now()).inDays;
    return remaining > 0 ? remaining : 0;
  }

  /// Get valid votes (within validity period)
  List<RemovalVote> get validVotes => 
    activeVotes.where((vote) => vote.isValid()).toList();

  /// Get votes for a specific member
  List<RemovalVote> getVotesForMember(String memberId) =>
    validVotes.where((vote) => vote.targetMemberId.value == memberId).toList();

  /// Check if member can be voted out (needs majority votes)
  bool canMemberBeVotedOut(String memberId) {
    final votes = getVotesForMember(memberId);
    final totalActiveMembers = activeMembersCount;
    final requiredVotes = (totalActiveMembers / 2).ceil();
    return votes.length >= requiredVotes;
  }

  /// Validate business rules for operations

  /// Validate join operation
  Result<void> validateJoin(String userId) {
    if (isActiveMember(userId)) {
      return Result.failure(const ValidationError(
        field: 'userId',
        message: 'User is already an active member of this bubble',
      ),);
    }

    if (isAtCapacity) {
      return Result.failure(BubbleCapacityExceededError(
        bubbleId: id.value,
        currentCount: activeMembersCount,
        maxCapacity: capacity.value,
      ),);
    }

    if (!isActive) {
      return Result.failure(BubbleDurationExpiredError(
        bubbleId: id.value,
        expiredAt: endDate ?? DateTime.now(),
      ),);
    }

    return Result.success(null);
  }

  /// Validate leave operation
  Result<void> validateLeave(String userId) {
    if (!isActiveMember(userId)) {
      return Result.failure(const NotInBubbleError());
    }

    return Result.success(null);
  }

  /// Validate invite operation
  Result<void> validateInvite(String inviterId, String inviteeId) {
    if (!isActiveMember(inviterId)) {
      return Result.failure(BubbleAccessDeniedError(
        bubbleId: id.value,
        operation: 'invite members to',
      ),);
    }

    if (isActiveMember(inviteeId)) {
      return Result.failure(const ValidationError(
        field: 'inviteeId',
        message: 'User is already an active member of this bubble',
      ),);
    }

    if (isAtCapacity) {
      return Result.failure(BubbleCapacityExceededError(
        bubbleId: id.value,
        currentCount: activeMembersCount,
        maxCapacity: capacity.value,
      ),);
    }

    return Result.success(null);
  }

  /// Validate propose (same as invite for now)
  Result<void> validatePropose(String proposerId, String proposeeId) => validateInvite(proposerId, proposeeId);

  /// Validate kickout/vote operation
  Result<void> validateVote(String voterId, String targetId) {
    if (!isActiveMember(voterId)) {
      return Result.failure(BubbleAccessDeniedError(
        bubbleId: id.value,
        operation: 'vote in',
      ),);
    }

    if (!isActiveMember(targetId)) {
      return Result.failure(const ValidationError(
        field: 'targetId',
        message: 'Target user is not an active member of this bubble',
      ),);
    }

    if (voterId == targetId) {
      return Result.failure(const ValidationError(
        field: 'targetId',
        message: 'User cannot vote to remove themselves',
      ),);
    }

    // Business rule: Kickout disabled when bubble has <= 2 active members
    if (activeMembersCount <= 2) {
      return Result.failure(const ValidationError(
        field: 'kickout',
        message: 'Kickout is not allowed in bubbles with 2 or fewer members. Members can leave the bubble instead.',
      ),);
    }

    // Check if voter already voted for this target
    final existingVote = validVotes.any((vote) =>
      vote.voterId.value == voterId && vote.targetMemberId.value == targetId,);

    if (existingVote) {
      return Result.failure(const ValidationError(
        field: 'voterId',
        message: 'User has already voted to remove this member',
      ),);
    }

    return Result.success(null);
  }

  /// Validate start operation (activate bubble) - DEPRECATED: Bubbles are created as active
  Result<void> validateStart() {
    if (status != BubbleLifecycleStatus.active) {
      return Result.failure(const ValidationError(
        field: 'status',
        message: 'Bubble is already active or in a final state',
      ),);
    }

    if (activeMembersCount < minActiveMembers) {
      return Result.failure(const ValidationError(
        field: 'members',
        message: 'Bubble needs at least $minActiveMembers active members to start',
      ),);
    }

    return Result.success(null);
  }

  /// Create BubbleEntity from JSON data
  static Result<BubbleEntity> fromJson(Map<String, dynamic> json) {
    try {
      final id = json['id'] as String? ?? '';
      final name = json['name'] as String? ?? '';
      final maxMembers = json['max_members'] as int? ?? json['maxMembers'] as int? ?? BubbleEntity.maxMembers;
      final status = json['status'] as String?;
      final createdAtStr = json['created_at'] as String? ?? json['createdAt'] as String?;
      final expiresAtStr = json['expires_at'] as String? ?? json['expiresAt'] as String?;
      
      DateTime? createdAt;
      if (createdAtStr != null) {
        createdAt = DateTime.tryParse(createdAtStr);
      }
      createdAt ??= DateTime.now();
      
      DateTime? endDate;
      if (expiresAtStr != null) {
        endDate = DateTime.tryParse(expiresAtStr);
      }
      
      var lifecycleStatus = BubbleLifecycleStatus.active; // Default to active
      if (status != null) {
        switch (status.toLowerCase()) {
          case 'active':
            lifecycleStatus = BubbleLifecycleStatus.active;
            break;
          case 'expired':
            lifecycleStatus = BubbleLifecycleStatus.expired;
            break;
          case 'archived':
            lifecycleStatus = BubbleLifecycleStatus.archived;
            break;
          case 'dissolved':
            lifecycleStatus = BubbleLifecycleStatus.dissolved;
            break;
          default:
            lifecycleStatus = BubbleLifecycleStatus.active; // Default to active
        }
      }
      
      // Parse members if available
      final members = <BubbleMemberEntity>[];
      final membersJson = json['members'] as List<dynamic>?;
      if (membersJson != null) {
        for (final memberJson in membersJson) {
          if (memberJson is Map<String, dynamic>) {
            final memberResult = BubbleMemberEntity.create(
              id: memberJson['id'] as String? ?? memberJson['user_id'] as String? ?? '',
              name: memberJson['name'] as String? ?? 
                    '${memberJson['first_name'] ?? ''} ${memberJson['last_name'] ?? ''}'.trim(),
              avatarUrl: memberJson['avatar_url'] as String? ?? memberJson['avatar_url'] as String?,
              joinedAt: DateTime.tryParse(memberJson['joined_at'] as String? ?? '') ?? DateTime.now(),
              isOnline: memberJson['is_online'] as bool? ?? false,
            );
            if (memberResult.isSuccess) {
              members.add(memberResult.data);
            }
          }
        }
      }
      
      return create(
        id: id,
        name: name,
        capacity: maxMembers,
        members: members,
        createdAt: createdAt,
        endDate: endDate,
        status: lifecycleStatus,
      );
    } catch (e) {
      return Result.failure(ValidationError(
        field: 'json',
        message: 'Failed to parse bubble from JSON: $e',
      ),);
    }
  }

  /// Copy with updated values
  BubbleEntity copyWith({
    BubbleName? name,
    MemberCapacity? capacity,
    DateTime? createdAt,
    DateTime? endDate,
    List<BubbleMemberEntity>? members,
    BubbleLifecycleStatus? status,
    BubbleCallEntity? activeCall,
    List<RemovalVote>? activeVotes,
    bool clearEndDate = false,
    bool clearActiveCall = false,
  }) {
    return BubbleEntity(
      id: id,
      name: name ?? this.name,
      capacity: capacity ?? this.capacity,
      createdAt: createdAt ?? this.createdAt,
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
      members: members ?? this.members,
      status: status ?? this.status,
      activeCall: clearActiveCall ? null : (activeCall ?? this.activeCall),
      activeVotes: activeVotes ?? this.activeVotes,
    );
  }

  BubbleEntity updateMember(BubbleMemberEntity updatedMember) {
    final memberIndex = members.indexWhere((m) => m.id == updatedMember.id);
    if (memberIndex != -1) {
      final updatedMembers = List<BubbleMemberEntity>.from(members);
      updatedMembers[memberIndex] = updatedMember;
      return copyWith(members: updatedMembers);
    }
    return this;
  }

  @override
  List<Object?> get props => [
    id,
    name,
    capacity,
    members,
    createdAt,
    endDate,
    activeCall,
    status,
    activeVotes,
  ];
} 
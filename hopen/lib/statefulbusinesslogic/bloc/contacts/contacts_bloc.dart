import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../repositories/contacts/contacts_repository.dart';
import '../../../provider/services/presence/presence_service.dart';
import '../../core/models/user_bubble_status.dart';
import '../../core/models/relationship_type.dart';
import '../auth/auth_bloc.dart';
import '../auth/auth_state.dart';
import 'contacts_event.dart';
import 'contacts_state.dart';

// Extension to add copyWith functionality to UserContact
extension UserContactExtension on UserContact {
  UserContact copyWith({
    String? id,
    String? name,
    String? username,
    String? imageUrl,
    BubbleMembershipStatus? bubbleStatus,
    RelationshipType? relationshipType,
    bool? isOnline,
    DateTime? contactSince,
  }) => UserContact(
    id: id ?? this.id,
    name: name ?? this.name,
    username: username ?? this.username,
    imageUrl: imageUrl ?? this.imageUrl,
    bubbleStatus: bubbleStatus ?? this.bubbleStatus,
    relationshipType: relationshipType ?? this.relationshipType,
    isOnline: isOnline ?? this.isOnline,
    contactSince: contactSince ?? this.contactSince,
  );
}

class ContactsBloc extends Bloc<ContactsEvent, ContactsState> {
  ContactsBloc({
    required this.contactsRepository,
    required AuthBloc authBloc,
    required PresenceService presenceService,
  }) : _authBloc = authBloc,
       _presenceService = presenceService,
       super(const ContactsState()) {
    on<LoadContacts>(_onLoadContacts);
    on<SearchContacts>(_onSearchContacts);
    on<AddContact>(_onAddContact);
    on<RemoveContact>(_onRemoveContact);
    on<BlockContact>(_onBlockContact);
    on<UpdateContactRelationship>(_onUpdateContactRelationship);
    on<FilterContactsByType>(_onFilterContactsByType);
    on<SortContacts>(_onSortContacts);
    on<UpdateContactsPresence>(_onUpdateContactsPresence);

    // Subscribe to real-time presence updates
    _presenceSubscription = _presenceService.presenceUpdates.listen(
      _onPresenceUpdate,
    );
  }
  final ContactsRepository contactsRepository;
  final AuthBloc _authBloc;
  final PresenceService _presenceService;

  StreamSubscription<Map<String, UserPresence>>? _presenceSubscription;

  @override
  Future<void> close() async {
    await _presenceSubscription?.cancel();
    return super.close();
  }

  /// Handle real-time presence updates from stream
  void _onPresenceUpdate(Map<String, UserPresence> presenceMap) {
    // Trigger the event to update contacts with new presence data
    add(UpdateContactsPresence(presenceMap));
  }

  /// Handle presence update event
  Future<void> _onUpdateContactsPresence(
    UpdateContactsPresence event,
    Emitter<ContactsState> emit,
  ) async {
    // Update contacts with new presence data
    final currentContacts = state.contacts;
    if (currentContacts.isEmpty) return;

    var hasUpdates = false;
    final updatedContacts =
        currentContacts.map((contact) {
          final presence = event.presenceMap[contact.id];
          if (presence != null) {
            final newIsOnline = presence.isOnline;
            if (contact.isOnline != newIsOnline) {
              hasUpdates = true;
              return contact.copyWith(isOnline: newIsOnline);
            }
          }
          return contact;
        }).toList();

    // Only emit new state if there were actual changes
    if (hasUpdates) {
      final sortedContacts = _sortContacts(updatedContacts, state.sortOption);
      final filteredContacts = _filterContacts(
        sortedContacts,
        state.filterType,
        state.searchQuery,
      );

      emit(
        state.copyWith(
          contacts: sortedContacts,
          filteredContacts: filteredContacts,
        ),
      );
    }
  }

  Future<void> _onLoadContacts(
    LoadContacts event,
    Emitter<ContactsState> emit,
  ) async {
    emit(state.copyWith(status: ContactsStatus.loading));
    try {
      final contacts = await contactsRepository.getContacts();

      // Get current user ID from AuthBloc to ensure current user is excluded
      String? currentUserId;
      final authState = _authBloc.state;
      if (authState.status == AuthStatus.authenticated) {
        currentUserId = authState.userId;
      }

      // Filter out current user (additional safety check)
      final contactsWithoutSelf =
          currentUserId != null
              ? contacts
                  .where((contact) => contact.id != currentUserId)
                  .toList()
              : contacts;

      // Apply current sorting and filtering
      final sortedContacts = _sortContacts(
        contactsWithoutSelf,
        state.sortOption,
      );
      final filteredContacts = _filterContacts(
        sortedContacts,
        state.filterType,
        state.searchQuery,
      );

      emit(
        state.copyWith(
          status: ContactsStatus.loaded,
          contacts: sortedContacts,
          filteredContacts: filteredContacts,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: ContactsStatus.error,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  Future<void> _onSearchContacts(
    SearchContacts event,
    Emitter<ContactsState> emit,
  ) async {
    final searchTerm = event.searchTerm.trim();
    print(
      '🔍 ContactsBloc: SearchContacts event received with term: "$searchTerm"',
    );

    // If search term is empty, just reset filtering locally
    if (searchTerm.isEmpty) {
      print('🔍 ContactsBloc: Empty search term, resetting to local filtering');
      final filteredContacts = _filterContacts(
        state.contacts,
        state.filterType,
        null,
      );

      emit(
        state.copyWith(filteredContacts: filteredContacts, searchQuery: null),
      );
      return;
    }

    print('🔍 ContactsBloc: Performing remote search for: "$searchTerm"');
    // Perform remote search to ensure users outside current list are included
    final remoteResults = await contactsRepository.searchUsers(searchTerm);
    print(
      '🔍 ContactsBloc: Remote search returned ${remoteResults.length} results',
    );

    // Get current user ID from AuthBloc to ensure current user is excluded
    String? currentUserId;
    final authState = _authBloc.state;
    if (authState.status == AuthStatus.authenticated) {
      currentUserId = authState.userId;
    }

    // Filter out current user from search results (additional safety check)
    final searchResultsWithoutSelf =
        currentUserId != null
            ? remoteResults
                .where((contact) => contact.id != currentUserId)
                .toList()
            : remoteResults;

    final lowerQuery = searchTerm.toLowerCase();

    // Apply current sorting
    final sortedResults = _sortContacts(
      searchResultsWithoutSelf,
      state.sortOption,
    );

    // Apply additional local filters (bubble status, relationship filter handled in UI)
    final filteredContacts = _filterContacts(
      sortedResults,
      state.filterType,
      lowerQuery,
    );

    emit(
      state.copyWith(
        contacts: sortedResults,
        filteredContacts: filteredContacts,
        searchQuery: lowerQuery,
      ),
    );
  }

  Future<void> _onAddContact(
    AddContact event,
    Emitter<ContactsState> emit,
  ) async {
    try {
      await contactsRepository.addContact(
        event.userId,
        relationshipType: event.relationshipType,
      );

      // Reload contacts to include the new contact
      add(LoadContacts());
    } catch (e) {
      emit(
        state.copyWith(
          status: ContactsStatus.error,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  Future<void> _onRemoveContact(
    RemoveContact event,
    Emitter<ContactsState> emit,
  ) async {
    try {
      await contactsRepository.removeContact(event.contactId);

      // Update local contacts list by removing the contact
      final updatedContacts =
          List<UserContact>.from(
            state.contacts,
          ).where((contact) => contact.id != event.contactId).toList();

      final filteredContacts = _filterContacts(
        updatedContacts,
        state.filterType,
        state.searchQuery,
      );

      emit(
        state.copyWith(
          contacts: updatedContacts,
          filteredContacts: filteredContacts,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: ContactsStatus.error,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  Future<void> _onBlockContact(
    BlockContact event,
    Emitter<ContactsState> emit,
  ) async {
    try {
      await contactsRepository.blockContact(event.contactId);

      // Update local contacts list by removing the blocked contact
      final updatedContacts =
          List<UserContact>.from(
            state.contacts,
          ).where((contact) => contact.id != event.contactId).toList();

      final filteredContacts = _filterContacts(
        updatedContacts,
        state.filterType,
        state.searchQuery,
      );

      emit(
        state.copyWith(
          contacts: updatedContacts,
          filteredContacts: filteredContacts,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: ContactsStatus.error,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  Future<void> _onUpdateContactRelationship(
    UpdateContactRelationship event,
    Emitter<ContactsState> emit,
  ) async {
    try {
      await contactsRepository.updateContactRelationship(
        event.contactId,
        event.relationshipType,
      );

      // Update the contact in the local list
      final updatedContacts =
          state.contacts.map((contact) {
            if (contact.id == event.contactId) {
              return contact.copyWith(relationshipType: event.relationshipType);
            }
            return contact;
          }).toList();

      final filteredContacts = _filterContacts(
        updatedContacts,
        state.filterType,
        state.searchQuery,
      );

      emit(
        state.copyWith(
          contacts: updatedContacts,
          filteredContacts: filteredContacts,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: ContactsStatus.error,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  void _onFilterContactsByType(
    FilterContactsByType event,
    Emitter<ContactsState> emit,
  ) {
    final filterType = event.filterType;

    final filteredContacts = _filterContacts(
      state.contacts,
      filterType,
      state.searchQuery,
    );

    emit(
      state.copyWith(
        filteredContacts: filteredContacts,
        filterType: filterType,
      ),
    );
  }

  void _onSortContacts(SortContacts event, Emitter<ContactsState> emit) {
    final sortedContacts = _sortContacts(state.contacts, event.sortOption);
    final filteredContacts = _filterContacts(
      sortedContacts,
      state.filterType,
      state.searchQuery,
    );

    emit(
      state.copyWith(
        contacts: sortedContacts,
        filteredContacts: filteredContacts,
        sortOption: event.sortOption,
      ),
    );
  }

  // Helper method to filter contacts
  List<UserContact> _filterContacts(
    List<UserContact> contacts,
    RelationshipType? filterType,
    String? searchQuery,
  ) {
    var result = List<UserContact>.from(contacts);

    // Apply relationship type filter
    if (filterType != null) {
      result =
          result
              .where((contact) => contact.relationshipType == filterType)
              .toList();
    }

    // Apply search filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final lowercaseQuery = searchQuery.toLowerCase();
      result =
          result.where((contact) {
            final nameMatch = contact.name.toLowerCase().contains(
              lowercaseQuery,
            );
            final usernameMatch =
                contact.username?.toLowerCase().contains(lowercaseQuery) ??
                false;
            return nameMatch || usernameMatch;
          }).toList();
    }

    return result;
  }

  // Helper method to sort contacts
  List<UserContact> _sortContacts(
    List<UserContact> contacts,
    ContactsSortOption sortOption,
  ) {
    final sortedContacts = List<UserContact>.from(contacts);

    switch (sortOption) {
      case ContactsSortOption.nameAsc:
        sortedContacts.sort((a, b) => a.name.compareTo(b.name));
        break;
      case ContactsSortOption.nameDesc:
        sortedContacts.sort((a, b) => b.name.compareTo(a.name));
        break;
      case ContactsSortOption.recentlyAdded:
        sortedContacts.sort(
          (a, b) => (b.contactSince ?? DateTime.now()).compareTo(
            a.contactSince ?? DateTime.now(),
          ),
        );
        break;
      case ContactsSortOption.relationshipType:
        sortedContacts.sort((a, b) {
          final compareResult = a.relationshipType.index.compareTo(
            b.relationshipType.index,
          );
          return compareResult != 0 ? compareResult : a.name.compareTo(b.name);
        });
        break;
    }

    return sortedContacts;
  }
}

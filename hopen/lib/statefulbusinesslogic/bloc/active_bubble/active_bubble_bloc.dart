import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import '../../../repositories/active_bubble/active_bubble_repository.dart';
import 'active_bubble_event.dart';
import 'active_bubble_state.dart';

/// BLoC for managing active bubble state and operations
class ActiveBubbleBloc extends Bloc<ActiveBubbleEvent, ActiveBubbleState> {

  ActiveBubbleBloc({
    required ActiveBubbleRepository repository,
  }) : _repository = repository,
       super(const ActiveBubbleInitial()) {
    
    on<LoadActiveBubbleEvent>(_onLoadActiveBubble);
    on<RefreshActiveBubbleEvent>(_onRefreshActiveBubble);
    on<UpdateBubbleCountdownEvent>(_onUpdateBubbleCountdown);
    on<HandleBubbleExpirationEvent>(_onHandleBubbleExpiration);
    on<JoinActiveBubbleEvent>(_onJoinActiveBubble);
    on<LeaveActiveBubbleEvent>(_onLeaveActiveBubble);
    on<UpdateMemberStatusEvent>(_onUpdateMemberStatus);
    on<ResetActiveBubbleEvent>(_onResetActiveBubble);
    on<ClearErrorEvent>(_onClearError);
  }
  final ActiveBubbleRepository _repository;
  final Logger _logger = Logger();

  /// Handles the [LoadActiveBubbleEvent] to load user's active bubble.
  Future<void> _onLoadActiveBubble(
    LoadActiveBubbleEvent event,
    Emitter<ActiveBubbleState> emit,
  ) async {
    try {
      _logger.i('Loading active bubble for user: ${event.userId}');
      
      emit(const ActiveBubbleLoading());

      final result = await _repository.getActiveBubble(event.userId);
      
      result.fold(
        onFailure: (failure) {
          _logger.e('Failed to load active bubble: $failure');
          emit(ActiveBubbleError(
            message: failure.toString(),
          ),);
        },
        onSuccess: (bubble) {
          _logger.i('Successfully loaded active bubble: ${bubble?.id}');
          emit(ActiveBubbleLoaded(
            activeBubble: bubble,
          ),);
        },
      );
    } on Exception catch (e, stackTrace) {
      _logger.e('Error loading active bubble', error: e, stackTrace: stackTrace);
      
      emit(ActiveBubbleError(
        message: _getErrorMessage(e),
      ),);
    }
  }

  /// Handles the [RefreshActiveBubbleEvent] to refresh active bubble data.
  Future<void> _onRefreshActiveBubble(
    RefreshActiveBubbleEvent event,
    Emitter<ActiveBubbleState> emit,
  ) async {
    try {
      _logger.i('Refreshing active bubble');
      
      if (state is! ActiveBubbleLoaded) {
        _logger.w('No active bubble to refresh');
        return;
      }

      final currentState = state as ActiveBubbleLoaded;
      if (currentState.activeBubble == null) {
        _logger.w('No active bubble to refresh');
        return;
      }

      emit(ActiveBubbleUpdating(
        activeBubble: currentState.activeBubble!,
        updateType: 'refresh',
      ),);

      final result = await _repository.refreshActiveBubble(event.userId);
      
      result.fold(
        onFailure: (failure) {
          _logger.e('Failed to refresh active bubble: $failure');
          emit(ActiveBubbleError(
            message: failure.toString(),
          ),);
        },
        onSuccess: (bubble) {
          _logger.i('Successfully refreshed active bubble');
          emit(ActiveBubbleLoaded(
            activeBubble: bubble,
          ),);
        },
      );
    } on Exception catch (e, stackTrace) {
      _logger.e('Error refreshing active bubble', error: e, stackTrace: stackTrace);
      
      emit(ActiveBubbleError(
        message: _getErrorMessage(e),
      ),);
    }
  }

  /// Handles the [UpdateBubbleCountdownEvent] to update bubble countdown.
  void _onUpdateBubbleCountdown(
    UpdateBubbleCountdownEvent event,
    Emitter<ActiveBubbleState> emit,
  ) {
    _logger.d('Updating bubble countdown: ${event.newEndTime}');
    
    if (state is ActiveBubbleLoaded) {
      final currentState = state as ActiveBubbleLoaded;
      if (currentState.activeBubble != null) {
        emit(ActiveBubbleLoaded(
          activeBubble: currentState.activeBubble!.copyWith(
            endDate: event.newEndTime,
          ),
        ),);
      }
    }
  }

  /// Handles the [HandleBubbleExpirationEvent] when bubble expires.
  Future<void> _onHandleBubbleExpiration(
    HandleBubbleExpirationEvent event,
    Emitter<ActiveBubbleState> emit,
  ) async {
    try {
      _logger.i('Handling bubble expiration for bubble: ${event.bubbleId}');
      
      if (state is ActiveBubbleLoaded) {
        final currentState = state as ActiveBubbleLoaded;
        if (currentState.activeBubble != null) {
          emit(ActiveBubbleExpired(
            expiredBubble: currentState.activeBubble!,
            expiredAt: DateTime.now(),
          ),);
        }
      }
    } on Exception catch (e, stackTrace) {
      _logger.e('Error handling bubble expiration', error: e, stackTrace: stackTrace);
      
      emit(ActiveBubbleError(
        message: _getErrorMessage(e),
      ),);
    }
  }

  /// Handles the [JoinActiveBubbleEvent] to join a bubble.
  Future<void> _onJoinActiveBubble(
    JoinActiveBubbleEvent event,
    Emitter<ActiveBubbleState> emit,
  ) async {
    try {
      _logger.i('Joining bubble: ${event.bubbleId}');
      
      emit(const ActiveBubbleLoading());

      // Refresh active bubble after join operation
      final result = await _repository.getActiveBubble(event.userId);
      
      result.fold(
        onFailure: (failure) {
          _logger.e('Failed to get active bubble after join: $failure');
          emit(ActiveBubbleError(
            message: failure.toString(),
          ),);
        },
        onSuccess: (bubble) {
          _logger.i('Successfully joined bubble');
          emit(ActiveBubbleLoaded(
            activeBubble: bubble,
          ),);
        },
      );
    } on Exception catch (e, stackTrace) {
      _logger.e('Error joining bubble', error: e, stackTrace: stackTrace);
      
      emit(ActiveBubbleError(
        message: _getErrorMessage(e),
      ),);
    }
  }

  /// Handles the [LeaveActiveBubbleEvent] to leave current bubble.
  Future<void> _onLeaveActiveBubble(
    LeaveActiveBubbleEvent event,
    Emitter<ActiveBubbleState> emit,
  ) async {
    try {
      _logger.i('Leaving active bubble');
      
      if (state is! ActiveBubbleLoaded) {
        _logger.w('No active bubble to leave');
        return;
      }

      final currentState = state as ActiveBubbleLoaded;
      if (currentState.activeBubble == null) {
        _logger.w('No active bubble to leave');
        return;
      }

      emit(const ActiveBubbleLoading());

      // After leaving, user should have no active bubble
      emit(const ActiveBubbleInitial());
    } on Exception catch (e, stackTrace) {
      _logger.e('Error leaving bubble', error: e, stackTrace: stackTrace);
      
      emit(ActiveBubbleError(
        message: _getErrorMessage(e),
      ),);
    }
  }

  /// Handles the [UpdateMemberStatusEvent] to update member status.
  Future<void> _onUpdateMemberStatus(
    UpdateMemberStatusEvent event,
    Emitter<ActiveBubbleState> emit,
  ) async {
    try {
      _logger.d('Updating member status: ${event.userId} -> ${event.status}');
      
      if (state is ActiveBubbleLoaded) {
        final currentState = state as ActiveBubbleLoaded;
        if (currentState.activeBubble != null) {
          final updatedMembers = currentState.activeBubble!.members.map((member) {
            if (member.id.value == event.userId) {
              return member.copyWith(isOnline: event.status == 'online');
            }
            return member;
          }).toList();

          emit(ActiveBubbleLoaded(
            activeBubble: currentState.activeBubble!.copyWith(
              members: updatedMembers,
            ),
          ),);
        }
      }
    } on Exception catch (e, stackTrace) {
      _logger.e('Error updating member status', error: e, stackTrace: stackTrace);
      
      emit(ActiveBubbleError(
        message: _getErrorMessage(e),
      ),);
    }
  }

  /// Handles the [ResetActiveBubbleEvent] to reset state.
  void _onResetActiveBubble(
    ResetActiveBubbleEvent event,
    Emitter<ActiveBubbleState> emit,
  ) {
    _logger.d('Resetting active bubble state');
    
    emit(const ActiveBubbleInitial());
  }

  /// Handles the [ClearErrorEvent] to clear error state.
  void _onClearError(
    ClearErrorEvent event,
    Emitter<ActiveBubbleState> emit,
  ) {
    _logger.d('Clearing error state');
    
    emit(const ActiveBubbleInitial());
  }

  /// Converts exceptions to user-friendly error messages.
  String _getErrorMessage(Object error) {
    if (error is Exception) {
      return error.toString().replaceFirst('Exception: ', '');
    }
    return error.toString();
  }
}

// REMOVED: Duplicate BubbleStatus enum - use BubbleLifecycleStatus from bubble_entity.dart instead
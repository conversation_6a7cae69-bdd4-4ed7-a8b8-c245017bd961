/// Validation utilities for the Flutter client
/// CRITICAL: Provides validation for backend optimization requirements

class ValidationUtils {
  // UUID validation regex pattern
  static final RegExp _uuidRegex = RegExp(
    r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
  );

  /// CRITICAL: Validates UUID format for backend compatibility
  /// The backend expects proper UUID format for efficient operations
  static bool isValidUuid(String? uuid) {
    if (uuid == null || uuid.isEmpty) return false;
    return _uuidRegex.hasMatch(uuid);
  }

  /// CRITICAL: Validates that a message has the required createdAt timestamp
  /// This is essential for the backend optimization that eliminates read-before-write
  static bool hasValidCreatedAt(DateTime? createdAt) {
    if (createdAt == null) return false;
    
    // Ensure the timestamp is not in the future (with 1 minute tolerance)
    final now = DateTime.now();
    final maxAllowed = now.add(const Duration(minutes: 1));
    
    // Ensure the timestamp is not too old (more than 1 year)
    final minAllowed = now.subtract(const Duration(days: 365));
    
    return createdAt.isBefore(maxAllowed) && createdAt.isAfter(minAllowed);
  }

  /// CRITICAL: Validates edit message request
  static ValidationResult validateEditMessageRequest({
    required String messageId,
    required String newContent,
    required DateTime createdAt,
    String? bubbleId,
  }) {
    // Validate message ID
    if (!isValidUuid(messageId)) {
      return ValidationResult.error('Invalid message ID format');
    }

    // Validate content
    if (newContent.trim().isEmpty) {
      return ValidationResult.error('Message content cannot be empty');
    }

    if (newContent.length > 4000) {
      return ValidationResult.error('Message content too long (max 4000 characters)');
    }

    // CRITICAL: Validate createdAt timestamp
    if (!hasValidCreatedAt(createdAt)) {
      return ValidationResult.error('Invalid or missing created_at timestamp');
    }

    // Validate bubble ID if provided
    if (bubbleId != null && !isValidUuid(bubbleId)) {
      return ValidationResult.error('Invalid bubble ID format');
    }

    return ValidationResult.success();
  }

  /// CRITICAL: Validates delete message request
  static ValidationResult validateDeleteMessageRequest({
    required String messageId,
    required DateTime createdAt,
    String? bubbleId,
  }) {
    // Validate message ID
    if (!isValidUuid(messageId)) {
      return ValidationResult.error('Invalid message ID format');
    }

    // CRITICAL: Validate createdAt timestamp
    if (!hasValidCreatedAt(createdAt)) {
      return ValidationResult.error('Invalid or missing created_at timestamp');
    }

    // Validate bubble ID if provided
    if (bubbleId != null && !isValidUuid(bubbleId)) {
      return ValidationResult.error('Invalid bubble ID format');
    }

    return ValidationResult.success();
  }

  /// CRITICAL: Validates mark as read request
  static ValidationResult validateMarkAsReadRequest({
    required String messageId,
    required DateTime createdAt,
    String? conversationId,
  }) {
    // Validate message ID
    if (!isValidUuid(messageId)) {
      return ValidationResult.error('Invalid message ID format');
    }

    // CRITICAL: Validate createdAt timestamp
    if (!hasValidCreatedAt(createdAt)) {
      return ValidationResult.error('Invalid or missing created_at timestamp');
    }

    // Validate conversation ID if provided
    if (conversationId != null && !isValidUuid(conversationId)) {
      return ValidationResult.error('Invalid conversation ID format');
    }

    return ValidationResult.success();
  }

  /// Validates general message structure
  static ValidationResult validateMessage({
    required String messageId,
    required String senderId,
    required DateTime timestamp,
    String? bubbleId,
    String? conversationId,
  }) {
    if (!isValidUuid(messageId)) {
      return ValidationResult.error('Invalid message ID format');
    }

    if (!isValidUuid(senderId)) {
      return ValidationResult.error('Invalid sender ID format');
    }

    if (!hasValidCreatedAt(timestamp)) {
      return ValidationResult.error('Invalid timestamp');
    }

    if (bubbleId != null && !isValidUuid(bubbleId)) {
      return ValidationResult.error('Invalid bubble ID format');
    }

    if (conversationId != null && !isValidUuid(conversationId)) {
      return ValidationResult.error('Invalid conversation ID format');
    }

    return ValidationResult.success();
  }
}

/// Result class for validation operations
class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  const ValidationResult._(this.isValid, this.errorMessage);

  factory ValidationResult.success() => const ValidationResult._(true, null);
  
  factory ValidationResult.error(String message) => ValidationResult._(false, message);

  bool get isError => !isValid;
}

/// CRITICAL: Error messages for backend optimization requirements
class BackendOptimizationErrors {
  static const String missingCreatedAt = 
      'created_at timestamp is required for backend optimization';
  
  static const String invalidUuidFormat = 
      'Invalid UUID format - backend requires proper UUID structure';
  
  static const String timestampTooOld = 
      'Timestamp is too old - operation may have expired';
  
  static const String timestampInFuture = 
      'Timestamp cannot be in the future';
  
  static const String operationRequiresTimestamp = 
      'This operation requires the original message timestamp for efficient processing';

  /// Get user-friendly error message for backend optimization errors
  static String getUserFriendlyMessage(String technicalError) {
    if (technicalError.contains('created_at')) {
      return 'Unable to process request - please try refreshing the chat';
    }
    
    if (technicalError.contains('UUID') || technicalError.contains('format')) {
      return 'Invalid message format - please try again';
    }
    
    if (technicalError.contains('timestamp')) {
      return 'Message timestamp issue - please refresh and try again';
    }
    
    return 'Unable to process request - please try again';
  }
}

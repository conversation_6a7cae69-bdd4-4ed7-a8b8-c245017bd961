import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../models/api_models.dart';

/// Mapper for converting between API models and domain entities
class BubbleMapper {
  /// Convert ApiBubble to BubbleEntity
  static Result<BubbleEntity> fromApiBubble(ApiBubble apiBubble) {
    try {
      return BubbleEntity.create(
        id: apiBubble.id,
        name: apiBubble.name,
        capacity: apiBubble.maxMembers ?? BubbleEntity.maxMembers,
        createdAt: apiBubble.createdAt ?? DateTime.now(),
        endDate: apiBubble.expiresAt,
        status: _mapLifecycleStatus(apiBubble.status),
      );
    } catch (e) {
      return Result.failure(ValidationError(message: 'Failed to map API bubble to domain: $e'));
    }
  }

  /// Convert BubbleMemberResponse to BubbleMemberEntity
  static Result<BubbleMemberEntity> _mapBubbleMember(BubbleMemberResponse memberResponse) {
    try {
      return BubbleMemberEntity.create(
        id: memberResponse.userId,
        name: 'User ${memberResponse.userId}', // Name would come from user service
        joinedAt: memberResponse.joinedAt,
        isOnline: memberResponse.isOnline,
        status: _mapMemberStatus(memberResponse.status),
        unreadMessageCount: memberResponse.unreadMessageCount,
        leftAt: memberResponse.leftAt,
        leaveReason: _mapLeaveReason(memberResponse.leaveReason),
      );
    } catch (e) {
      return Result.failure(ValidationError(message: 'Failed to map bubble member: $e'));
    }
  }

  /// Convert BubbleEntity to ApiBubble
  static ApiBubble toApiBubble(BubbleEntity bubble) => ApiBubble(
      id: bubble.id.value,
      name: bubble.name.value,
      maxMembers: bubble.capacity.value,
      currentMembers: bubble.activeMembersCount,
      currentMemberCount: bubble.activeMembersCount,
      status: _mapStatusToString(bubble.status),
      createdAt: bubble.createdAt,
      expiresAt: bubble.endDate,
    );

  /// Map string status to BubbleLifecycleStatus enum
  static BubbleLifecycleStatus _mapLifecycleStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return BubbleLifecycleStatus.active;
      case 'expired':
        return BubbleLifecycleStatus.expired;
      case 'dissolved':
        return BubbleLifecycleStatus.dissolved;
      case 'archived':
        return BubbleLifecycleStatus.archived;
      case 'dissolved':
        return BubbleLifecycleStatus.dissolved;
      default:
        return BubbleLifecycleStatus.active;
    }
  }

  /// Map BubbleLifecycleStatus enum to string
  static String _mapStatusToString(BubbleLifecycleStatus status) {
    switch (status) {
      case BubbleLifecycleStatus.active:
        return 'active';
      case BubbleLifecycleStatus.expired:
        return 'expired';
      case BubbleLifecycleStatus.dissolved:
        return 'dissolved';
      case BubbleLifecycleStatus.archived:
        return 'archived';
    }
  }

  /// Map string status to BubbleMemberStatus enum - UPDATED FOR BACKEND ALIGNMENT
  static BubbleMemberStatus _mapMemberStatus(String status) {
    return BubbleMemberStatusExtension.fromBackendString(status);
  }

  /// Map string leave reason to LeaveReason enum
  static LeaveReason? _mapLeaveReason(String? leaveReason) {
    switch (leaveReason?.toLowerCase()) {
      case 'voluntary':
        return LeaveReason.voluntary;
      case 'kicked_out':
      case 'kickedout':
        return LeaveReason.kickedOut;
      default:
        return null;
    }
  }
}

extension BubbleResponseMapper on BubbleResponse {
  /// Convert BubbleResponse (with members) to Domain Entity
  Result<BubbleEntity> toDomain() {
    try {
      // Convert members
      final convertedMembers = <BubbleMemberEntity>[];
      for (final member in members) {
        final memberResult = member.toDomain();
        if (memberResult.isFailure) {
          return Result.failure(memberResult.error);
        }
        convertedMembers.add(memberResult.data);
      }

      final entity = BubbleEntity.create(
        id: id,
        name: name,
        capacity: capacity,
        members: convertedMembers,
        createdAt: createdAt,
        endDate: endDate,
        status: _mapLifecycleStatus(status),
      );

      if (entity.isFailure) {
        return Result.failure(entity.error);
      }

      return Result.success(entity.data);
    } catch (e) {
      return Result.failure(ValidationError(message: 'Failed to map BubbleResponse to domain: $e'));
    }
  }

  /// Map string status to enum
  BubbleLifecycleStatus _mapLifecycleStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return BubbleLifecycleStatus.active;
      case 'expired':
        return BubbleLifecycleStatus.expired;
      case 'dissolved':
        return BubbleLifecycleStatus.dissolved;
      case 'archived':
        return BubbleLifecycleStatus.archived;
      case 'dissolved':
        return BubbleLifecycleStatus.dissolved;
      default:
        return BubbleLifecycleStatus.active;
    }
  }
}

extension BubbleMemberResponseMapper on BubbleMemberResponse {
  /// Convert API member DTO to Domain entity
  Result<BubbleMemberEntity> toDomain() {
    try {
      final entity = BubbleMemberEntity.create(
        id: userId,
        name: userId, // Using userId as name for now since name is not available in BubbleMemberResponse
        joinedAt: joinedAt ?? DateTime.now(),
        isOnline: isOnline ?? false,
        status: _mapMemberStatus(status),
        unreadMessageCount: unreadMessageCount ?? 0,
        leftAt: leftAt,
        leaveReason: _mapLeaveReason(leaveReason),
      );

      if (entity.isFailure) {
        return Result.failure(entity.error);
      }

      return Result.success(entity.data);
    } catch (e) {
      return Result.failure(ValidationError(message: 'Failed to map BubbleMemberResponse to domain: $e'));
    }
  }

  /// Map string status to enum - UPDATED FOR BACKEND ALIGNMENT
  BubbleMemberStatus _mapMemberStatus(String? status) {
    return BubbleMemberStatusExtension.fromBackendString(status ?? 'active');
  }

  /// Map string leave reason to enum
  LeaveReason? _mapLeaveReason(String? reason) {
    switch (reason?.toLowerCase()) {
      case 'voluntary':
        return LeaveReason.voluntary;
      case 'kickedout':
        return LeaveReason.kickedOut;
      default:
        return null;
    }
  }
}

extension BubbleEntityMapper on BubbleEntity {
  /// Convert Domain Entity to API DTO
  ApiBubble toDto() => ApiBubble(
      id: id.value,
      name: name.value,
      maxMembers: capacity.value,
      currentMembers: activeMembersCount,
      currentMemberCount: activeMembersCount,
      status: _mapStatusToString(status),
      createdAt: createdAt,
      expiresAt: endDate,
    );

  /// Convert Domain Entity to BubbleResponse
  BubbleResponse toBubbleResponse() => BubbleResponse(
      id: id.value,
      name: name.value,
      capacity: capacity.value,
      members: members.map((member) => member.toBubbleMemberResponse()).toList(),
      createdAt: createdAt,
      endDate: endDate,
      status: _mapStatusToString(status),
    );

  /// Map enum status to string
  String _mapStatusToString(BubbleLifecycleStatus status) {
    switch (status) {
      case BubbleLifecycleStatus.active:
        return 'active';
      case BubbleLifecycleStatus.expired:
        return 'expired';
      case BubbleLifecycleStatus.dissolved:
        return 'dissolved';
      case BubbleLifecycleStatus.archived:
        return 'archived';
    }
  }
}

extension BubbleMemberEntityMapper on BubbleMemberEntity {
  /// Convert Domain Member to BubbleMemberResponse
  BubbleMemberResponse toBubbleMemberResponse() => BubbleMemberResponse(
      userId: id.value,
      joinedAt: joinedAt,
      status: _mapStatusToString(status),
      isOnline: isOnline,
      leftAt: leftAt,
      leaveReason: _mapLeaveReasonToString(leaveReason),
      unreadMessageCount: unreadMessageCount,
    );

  /// Map enum status to string - UPDATED FOR BACKEND ALIGNMENT
  String _mapStatusToString(BubbleMemberStatus status) {
    return status.toBackendString();
  }

  /// Map enum leave reason to string
  String? _mapLeaveReasonToString(LeaveReason? reason) {
    switch (reason) {
      case LeaveReason.voluntary:
        return 'voluntary';
      case LeaveReason.kickedOut:
        return 'kickedout';
      default:
        return null;
    }
  }
}

/// Extension to add toDomain method to ApiBubble
extension ApiBubbleMapper on ApiBubble {
  /// Convert ApiBubble to BubbleEntity
  Result<BubbleEntity> toDomain() => BubbleEntity.create(
      id: id,
      name: name,
      capacity: maxMembers,
      createdAt: createdAt ?? DateTime.now(),
      endDate: expiresAt,
      status: _mapLifecycleStatus(status),
    );

  /// Map string status to BubbleLifecycleStatus enum
  BubbleLifecycleStatus _mapLifecycleStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return BubbleLifecycleStatus.active;
      case 'expired':
        return BubbleLifecycleStatus.expired;
      case 'dissolved':
        return BubbleLifecycleStatus.dissolved;
      case 'archived':
        return BubbleLifecycleStatus.archived;
      default:
        return BubbleLifecycleStatus.active;
    }
  }
} 
import 'package:json_annotation/json_annotation.dart';

part 'api_models.g.dart';

// ============================================================================
// API Request/Response Models for Hopen Backend
// ============================================================================

// -----------------------------------------------------------------------------
// Authentication Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class LoginRequest {
  LoginRequest({required this.email, required this.password});

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);
  final String email;
  final String password;
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class SignupRequest {
  SignupRequest({
    required this.email,
    required this.password,
    required this.firstName,
    this.lastName,
    this.username,
    this.birthday,
    this.profilePictureUrl,
    this.notificationsEnabled = false,
  });

  factory SignupRequest.fromJson(Map<String, dynamic> json) =>
      _$SignupRequestFromJson(json);
  final String email;
  final String password;
  @JsonKey(name: 'first_name')
  final String firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;
  final String? username;
  final DateTime? birthday;
  @JsonKey(name: 'avatar_url')
  final String? profilePictureUrl;
  @JsonKey(name: 'notifications_enabled')
  final bool notificationsEnabled;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{
      'email': email,
      'password': password,
      'first_name': firstName,
      'username': username,
      'notifications_enabled': notificationsEnabled,
    };
    if (lastName != null) map['last_name'] = lastName;
    if (birthday != null) {
      map['date_of_birth'] = birthday!.toIso8601String().split('T').first;
    }
    if (profilePictureUrl != null) {
      map['avatar_url'] = profilePictureUrl;
    }
    return map;
  }
}

@JsonSerializable()
class AuthResponse {
  AuthResponse({
    this.token,
    this.accessToken,
    this.refreshToken,
    this.userId,
    this.expiresIn,
    this.user,
    this.message,
    this.success,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);
  final String? token;
  @JsonKey(name: 'access_token')
  final String? accessToken;
  @JsonKey(name: 'refresh_token')
  final String? refreshToken;
  @JsonKey(name: 'user_id')
  final String? userId;
  @JsonKey(name: 'expires_in')
  final int? expiresIn;
  final ApiUserProfile? user;
  final String? message;
  final bool? success;
  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

@JsonSerializable()
class RefreshTokenRequest {
  RefreshTokenRequest({required this.refreshToken});

  factory RefreshTokenRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshTokenRequestFromJson(json);
  @JsonKey(name: 'refresh_token')
  final String refreshToken;
  Map<String, dynamic> toJson() => _$RefreshTokenRequestToJson(this);
}

// -----------------------------------------------------------------------------
// User Profile Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiUserProfile {
  ApiUserProfile({
    required this.id,
    required this.username,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.displayName,
    required this.dateOfBirth,
    required this.createdAt,
    required this.updatedAt,
    this.avatarBucketName,
    this.avatarObjectKey,
    this.avatarUrl,
    this.isPremium = false,
    this.isActive = true,
    this.isPrivate = false,
    this.isBanned = false,
    this.bannedAt,
    this.lastActiveAt,
    this.presenceStatus = 'offline',
    this.notificationSettings,
  });

  factory ApiUserProfile.fromJson(Map<String, dynamic> json) =>
      _$ApiUserProfileFromJson(json);

  final String id;
  final String username;
  final String email;
  @JsonKey(name: 'first_name')
  final String firstName;
  @JsonKey(name: 'last_name')
  final String lastName;
  @JsonKey(name: 'display_name')
  final String displayName;
  @JsonKey(name: 'avatar_bucket_name')
  final String? avatarBucketName;
  @JsonKey(name: 'avatar_object_key')
  final String? avatarObjectKey;
  @JsonKey(name: 'avatar_url')
  final String? avatarUrl;
  @JsonKey(name: 'date_of_birth')
  final DateTime dateOfBirth;
  @JsonKey(name: 'is_premium')
  final bool isPremium;
  @JsonKey(name: 'is_active')
  final bool isActive;
  @JsonKey(name: 'is_private')
  final bool isPrivate;
  @JsonKey(name: 'is_banned')
  final bool isBanned;
  @JsonKey(name: 'banned_at')
  final DateTime? bannedAt;
  @JsonKey(name: 'last_active_at')
  final DateTime? lastActiveAt;
  @JsonKey(name: 'presence_status')
  final String presenceStatus;
  @JsonKey(name: 'notification_settings')
  final Map<String, dynamic>? notificationSettings;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  Map<String, dynamic> toJson() => _$ApiUserProfileToJson(this);
}

@JsonSerializable()
class UpdateUserProfileRequest {
  UpdateUserProfileRequest({
    this.firstName,
    this.lastName,
    this.username,
    this.profilePictureUrl,
  });

  factory UpdateUserProfileRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateUserProfileRequestFromJson(json);
  @JsonKey(name: 'first_name')
  final String? firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;
  final String? username;
  @JsonKey(name: 'avatar_url')
  final String? profilePictureUrl;
  Map<String, dynamic> toJson() => _$UpdateUserProfileRequestToJson(this);
}

@JsonSerializable()
class UpdateOnboardingStatusRequest {
  UpdateOnboardingStatusRequest({required this.hasCompletedOnboarding});

  factory UpdateOnboardingStatusRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateOnboardingStatusRequestFromJson(json);
  @JsonKey(name: 'hasCompletedOnboarding')
  final bool hasCompletedOnboarding;
  Map<String, dynamic> toJson() => _$UpdateOnboardingStatusRequestToJson(this);
}

// -----------------------------------------------------------------------------
// Bubble Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiBubble {
  ApiBubble({
    required this.id,
    required this.name,
    required this.capacity,
    required this.memberCount,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.creatorId,
    this.expiresAt,
    this.friendRequestOnExpire = false,
  });

  factory ApiBubble.fromJson(Map<String, dynamic> json) =>
      _$ApiBubbleFromJson(json);

  final String id;
  @JsonKey(name: 'creator_id')
  final String? creatorId;
  final String name;
  final int capacity;
  @JsonKey(name: 'member_count')
  final int memberCount;
  final String status; // BubbleLifecycleStatus
  @JsonKey(name: 'expires_at')
  final DateTime? expiresAt;
  @JsonKey(name: 'friend_request_on_expire')
  final bool friendRequestOnExpire;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  Map<String, dynamic> toJson() => _$ApiBubbleToJson(this);
}

@JsonSerializable()
class CreateBubbleRequest {
  CreateBubbleRequest({
    required this.name,
    this.maxMembers,
    this.invitedUserIds,
  });

  factory CreateBubbleRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateBubbleRequestFromJson(json);
  final String name;
  @JsonKey(name: 'max_members')
  final int? maxMembers;
  @JsonKey(name: 'invited_user_ids')
  final List<String>? invitedUserIds;
  Map<String, dynamic> toJson() => _$CreateBubbleRequestToJson(this);
}

@JsonSerializable()
class JoinBubbleRequest {
  JoinBubbleRequest({required this.bubbleId});

  factory JoinBubbleRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinBubbleRequestFromJson(json);
  @JsonKey(name: 'bubble_id')
  final String bubbleId;
  Map<String, dynamic> toJson() => _$JoinBubbleRequestToJson(this);
}

// -----------------------------------------------------------------------------
// Contact & Friendship Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiContact {
  ApiContact({
    required this.id,
    this.firstName,
    this.lastName,
    this.username,
    this.email,
    this.profilePictureUrl,
    this.relationshipType,
    this.isOnline,
    this.lastSeen,
    this.userId,
    this.contactUserId,
    this.senderId,
    this.receiverId,
    this.senderName,
    this.receiverName,
    this.sentAt,
    this.respondedAt,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory ApiContact.fromJson(Map<String, dynamic> json) =>
      _$ApiContactFromJson(json);
  final String id;
  @JsonKey(name: 'first_name')
  final String? firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;
  final String? username;
  final String? email;
  @JsonKey(name: 'avatar_url')
  final String? profilePictureUrl;
  @JsonKey(name: 'relationship_type')
  final String? relationshipType;
  @JsonKey(name: 'is_online')
  final bool? isOnline;
  @JsonKey(name: 'last_seen')
  final DateTime? lastSeen;
  @JsonKey(name: 'user_id')
  final String? userId;
  @JsonKey(name: 'contact_user_id')
  final String? contactUserId;
  @JsonKey(name: 'senderId')
  final String? senderId;
  @JsonKey(name: 'receiverId')
  final String? receiverId;
  @JsonKey(name: 'senderName')
  final String? senderName;
  @JsonKey(name: 'receiverName')
  final String? receiverName;
  @JsonKey(name: 'sentAt')
  final DateTime? sentAt;
  @JsonKey(name: 'respondedAt')
  final DateTime? respondedAt;
  final String? status;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  Map<String, dynamic> toJson() => _$ApiContactToJson(this);
}

@JsonSerializable()
class SendContactRequestRequest {
  SendContactRequestRequest({required this.recipientId, this.message});

  factory SendContactRequestRequest.fromJson(Map<String, dynamic> json) =>
      _$SendContactRequestRequestFromJson(json);
  @JsonKey(name: 'recipient_id')
  final String recipientId;
  final String? message;
  Map<String, dynamic> toJson() => _$SendContactRequestRequestToJson(this);
}

@JsonSerializable()
class AcceptContactRequestRequest {
  AcceptContactRequestRequest({required this.contactRequestId});

  factory AcceptContactRequestRequest.fromJson(Map<String, dynamic> json) =>
      _$AcceptContactRequestRequestFromJson(json);
  @JsonKey(name: 'contact_request_id')
  final String contactRequestId;
  Map<String, dynamic> toJson() => _$AcceptContactRequestRequestToJson(this);
}

// -----------------------------------------------------------------------------
// Chat Models (Cassandra-based)
// -----------------------------------------------------------------------------

// Bubble Chat Messages (Cassandra bubble_chat table)
@JsonSerializable()
class ApiBubbleChatMessage {
  ApiBubbleChatMessage({
    required this.bubbleId,
    required this.messageId,
    required this.senderId,
    required this.content,
    required this.messageType,
    required this.timestamp,
    this.metadata,
    this.editedAt,
    this.deletedAt,
    this.replyToMessageId,
  });

  factory ApiBubbleChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ApiBubbleChatMessageFromJson(json);

  @JsonKey(name: 'bubble_id')
  final String bubbleId;
  @JsonKey(name: 'message_id')
  final String messageId;
  @JsonKey(name: 'sender_id')
  final String senderId;
  final String content;
  @JsonKey(name: 'message_type')
  final String messageType;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;
  @JsonKey(name: 'edited_at')
  final DateTime? editedAt;
  @JsonKey(name: 'deleted_at')
  final DateTime? deletedAt;
  @JsonKey(name: 'reply_to_message_id')
  final String? replyToMessageId;

  Map<String, dynamic> toJson() => _$ApiBubbleChatMessageToJson(this);
}

// Friends Chat Conversations (Cassandra friends_chat table)
@JsonSerializable()
class ApiFriendsChat {
  ApiFriendsChat({
    required this.conversationId,
    required this.participant1Id,
    required this.participant2Id,
    this.lastMessageId,
    this.lastMessageAt,
    required this.createdAt,
  });

  factory ApiFriendsChat.fromJson(Map<String, dynamic> json) =>
      _$ApiFriendsChatFromJson(json);

  @JsonKey(name: 'conversation_id')
  final String conversationId;
  @JsonKey(name: 'participant1_id')
  final String participant1Id;
  @JsonKey(name: 'participant2_id')
  final String participant2Id;
  @JsonKey(name: 'last_message_id')
  final String? lastMessageId;
  @JsonKey(name: 'last_message_at')
  final DateTime? lastMessageAt;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  Map<String, dynamic> toJson() => _$ApiFriendsChatToJson(this);
}

// Contact Chat Messages (Cassandra contact_chat table)
@JsonSerializable()
class ApiContactChatMessage {
  ApiContactChatMessage({
    required this.conversationId,
    required this.messageId,
    required this.senderId,
    required this.recipientId,
    required this.content,
    required this.messageType,
    required this.timestamp,
    this.metadata,
    this.editedAt,
    this.deletedAt,
    this.readAt,
  });

  factory ApiContactChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ApiContactChatMessageFromJson(json);

  @JsonKey(name: 'conversation_id')
  final String conversationId;
  @JsonKey(name: 'message_id')
  final String messageId;
  @JsonKey(name: 'sender_id')
  final String senderId;
  @JsonKey(name: 'recipient_id')
  final String recipientId;
  final String content;
  @JsonKey(name: 'message_type')
  final String messageType;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;
  @JsonKey(name: 'edited_at')
  final DateTime? editedAt;
  @JsonKey(name: 'deleted_at')
  final DateTime? deletedAt;
  @JsonKey(name: 'read_at')
  final DateTime? readAt;

  Map<String, dynamic> toJson() => _$ApiContactChatMessageToJson(this);
}

// User Chat Lists - separate models for different chat types

// User Friends Chat List (Cassandra user_friends_chat table)
@JsonSerializable()
class ApiUserFriendsChat {
  ApiUserFriendsChat({
    required this.userId,
    required this.conversationId,
    required this.friendId,
    required this.createdAt,
    this.lastMessageAt,
    this.unreadCount = 0,
  });

  factory ApiUserFriendsChat.fromJson(Map<String, dynamic> json) =>
      _$ApiUserFriendsChatFromJson(json);

  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'conversation_id')
  final String conversationId;
  @JsonKey(name: 'friend_id')
  final String friendId;
  @JsonKey(name: 'last_message_at')
  final DateTime? lastMessageAt;
  @JsonKey(name: 'unread_count')
  final int unreadCount;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  Map<String, dynamic> toJson() => _$ApiUserFriendsChatToJson(this);
}

// User Contact Chat List (Cassandra user_contact_chat table)
@JsonSerializable()
class ApiUserContactChat {
  ApiUserContactChat({
    required this.userId,
    required this.conversationId,
    required this.contactId,
    required this.createdAt,
    this.lastMessageAt,
    this.unreadCount = 0,
  });

  factory ApiUserContactChat.fromJson(Map<String, dynamic> json) =>
      _$ApiUserContactChatFromJson(json);

  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'conversation_id')
  final String conversationId;
  @JsonKey(name: 'contact_id')
  final String contactId;
  @JsonKey(name: 'last_message_at')
  final DateTime? lastMessageAt;
  @JsonKey(name: 'unread_count')
  final int unreadCount;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  Map<String, dynamic> toJson() => _$ApiUserContactChatToJson(this);
}

// Message Count Counters (Cassandra bubble_chat_counts table)
@JsonSerializable()
class ApiBubbleChatCount {
  ApiBubbleChatCount({
    required this.bubbleId,
    required this.totalMessages,
  });

  factory ApiBubbleChatCount.fromJson(Map<String, dynamic> json) =>
      _$ApiBubbleChatCountFromJson(json);

  @JsonKey(name: 'bubble_id')
  final String bubbleId;
  @JsonKey(name: 'total_messages')
  final int totalMessages;

  Map<String, dynamic> toJson() => _$ApiBubbleChatCountToJson(this);
}

// Friends Chat Unread Counters (Cassandra friends_chat_unread_counts table)
@JsonSerializable()
class ApiFriendsChatUnreadCount {
  ApiFriendsChatUnreadCount({
    required this.conversationId,
    required this.userId,
    required this.unreadCount,
  });

  factory ApiFriendsChatUnreadCount.fromJson(Map<String, dynamic> json) =>
      _$ApiFriendsChatUnreadCountFromJson(json);

  @JsonKey(name: 'conversation_id')
  final String conversationId;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'unread_count')
  final int unreadCount;

  Map<String, dynamic> toJson() => _$ApiFriendsChatUnreadCountToJson(this);
}

// Contact Chat Unread Counters (Cassandra contact_chat_unread_counts table)
@JsonSerializable()
class ApiContactChatUnreadCount {
  ApiContactChatUnreadCount({
    required this.conversationId,
    required this.userId,
    required this.unreadCount,
  });

  factory ApiContactChatUnreadCount.fromJson(Map<String, dynamic> json) =>
      _$ApiContactChatUnreadCountFromJson(json);

  @JsonKey(name: 'conversation_id')
  final String conversationId;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'unread_count')
  final int unreadCount;

  Map<String, dynamic> toJson() => _$ApiContactChatUnreadCountToJson(this);
}

// -----------------------------------------------------------------------------
// Legacy Chat Models (for backward compatibility)
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiChatMessage {
  ApiChatMessage({
    required this.id,
    required this.senderId,
    required this.content,
    required this.messageType,
    required this.createdAt,
    this.bubbleId,
    this.conversationId,
    this.mediaUrl,
    this.replyToId,
    this.isEdited = false,
    this.isDeleted = false,
    this.isRead = false,
    this.updatedAt,
  });

  factory ApiChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ApiChatMessageFromJson(json);

  final String id;
  @JsonKey(name: 'sender_id')
  final String senderId;
  @JsonKey(name: 'bubble_id')
  final String? bubbleId;
  @JsonKey(name: 'conversation_id')
  final String? conversationId;
  final String content;
  @JsonKey(name: 'message_type')
  final String messageType;
  @JsonKey(name: 'media_url')
  final String? mediaUrl;
  @JsonKey(name: 'reply_to_id')
  final String? replyToId;
  @JsonKey(name: 'is_edited')
  final bool isEdited;
  @JsonKey(name: 'is_deleted')
  final bool isDeleted;
  @JsonKey(name: 'is_read')
  final bool isRead;
  @JsonKey(name: 'created_at')
  final DateTime createdAt; // CRITICAL: This timestamp is needed for backend optimization
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  Map<String, dynamic> toJson() => _$ApiChatMessageToJson(this);
}

@JsonSerializable()
class SendMessageRequest {
  SendMessageRequest({
    required this.bubbleId,
    required this.content,
    this.messageType = 'text',
  });

  factory SendMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$SendMessageRequestFromJson(json);
  @JsonKey(name: 'bubble_id')
  final String bubbleId;
  final String content;
  @JsonKey(name: 'message_type')
  final String messageType;
  Map<String, dynamic> toJson() => _$SendMessageRequestToJson(this);
}

// CRITICAL: New API models for backend optimization
@JsonSerializable()
class EditMessageRequest {
  EditMessageRequest({
    required this.content,
    required this.createdAt,
  });

  factory EditMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$EditMessageRequestFromJson(json);

  final String content;
  @JsonKey(name: 'created_at')
  final DateTime createdAt; // CRITICAL: Required for backend optimization

  Map<String, dynamic> toJson() => _$EditMessageRequestToJson(this);
}

@JsonSerializable()
class DeleteMessageRequest {
  DeleteMessageRequest({
    required this.createdAt,
  });

  factory DeleteMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteMessageRequestFromJson(json);

  @JsonKey(name: 'created_at')
  final DateTime createdAt; // CRITICAL: Required for backend optimization

  Map<String, dynamic> toJson() => _$DeleteMessageRequestToJson(this);
}

@JsonSerializable()
class MarkAsReadRequest {
  MarkAsReadRequest({
    required this.createdAt,
  });

  factory MarkAsReadRequest.fromJson(Map<String, dynamic> json) =>
      _$MarkAsReadRequestFromJson(json);

  @JsonKey(name: 'created_at')
  final DateTime createdAt; // CRITICAL: Required for backend optimization

  Map<String, dynamic> toJson() => _$MarkAsReadRequestToJson(this);
}

@JsonSerializable()
class GetChatMessagesRequest {
  GetChatMessagesRequest({
    required this.chatId,
    this.limit,
    this.beforeMessageId,
  });

  factory GetChatMessagesRequest.fromJson(Map<String, dynamic> json) =>
      _$GetChatMessagesRequestFromJson(json);
  @JsonKey(name: 'chat_id')
  final String chatId;
  final int? limit;
  @JsonKey(name: 'before_message_id')
  final String? beforeMessageId;
  Map<String, dynamic> toJson() => _$GetChatMessagesRequestToJson(this);
}

@JsonSerializable()
class GetChatMessagesResponse {
  GetChatMessagesResponse({
    required this.messages,
    required this.hasMore,
    this.nextCursor,
  });

  factory GetChatMessagesResponse.fromJson(Map<String, dynamic> json) =>
      _$GetChatMessagesResponseFromJson(json);
  final List<ApiChatMessage> messages;
  @JsonKey(name: 'has_more')
  final bool hasMore;
  @JsonKey(name: 'next_cursor')
  final String? nextCursor;
  Map<String, dynamic> toJson() => _$GetChatMessagesResponseToJson(this);
}

// -----------------------------------------------------------------------------
// Notification Model
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiNotification {
  ApiNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.isRead,
    required this.createdAt,
  });

  factory ApiNotification.fromJson(Map<String, dynamic> json) =>
      _$ApiNotificationFromJson(json);
  final String id;
  final String title;
  final String message;
  final String type;
  @JsonKey(name: 'is_read')
  final bool isRead;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  Map<String, dynamic> toJson() => _$ApiNotificationToJson(this);
}

// -----------------------------------------------------------------------------
// Call/WebRTC Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class CallInitiateResponse {
  CallInitiateResponse({required this.callId, this.offerSdp, this.iceServers});

  factory CallInitiateResponse.fromJson(Map<String, dynamic> json) =>
      _$CallInitiateResponseFromJson(json);
  @JsonKey(name: 'call_id')
  final String callId;
  @JsonKey(name: 'offer_sdp')
  final String? offerSdp;
  @JsonKey(name: 'ice_servers')
  final List<Map<String, dynamic>>? iceServers;
  Map<String, dynamic> toJson() => _$CallInitiateResponseToJson(this);
}

// -----------------------------------------------------------------------------
// Health Check Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class HealthCheckResponse {
  HealthCheckResponse({
    required this.status,
    this.version,
    this.timestamp,
    this.details,
  });

  factory HealthCheckResponse.fromJson(Map<String, dynamic> json) =>
      _$HealthCheckResponseFromJson(json);
  final String status;
  final String? version;
  final DateTime? timestamp;
  final Map<String, dynamic>? details;
  Map<String, dynamic> toJson() => _$HealthCheckResponseToJson(this);
}

// -----------------------------------------------------------------------------
// Common Response Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiResponseString {
  ApiResponseString({
    required this.success,
    this.data,
    this.message,
    this.code,
  });

  factory ApiResponseString.fromJson(Map<String, dynamic> json) =>
      _$ApiResponseStringFromJson(json);
  final bool success;
  final String? data;
  final String? message;
  final int? code;
  Map<String, dynamic> toJson() => _$ApiResponseStringToJson(this);
}

@JsonSerializable()
class ApiResponseMap {
  ApiResponseMap({required this.success, this.data, this.message, this.code});

  factory ApiResponseMap.fromJson(Map<String, dynamic> json) =>
      _$ApiResponseMapFromJson(json);
  final bool success;
  final Map<String, dynamic>? data;
  final String? message;
  final int? code;
  Map<String, dynamic> toJson() => _$ApiResponseMapToJson(this);
}

@JsonSerializable()
class ApiError {
  ApiError({required this.code, required this.message, this.details});

  factory ApiError.fromJson(Map<String, dynamic> json) =>
      _$ApiErrorFromJson(json);
  final String code;
  final String message;
  final Map<String, dynamic>? details;
  Map<String, dynamic> toJson() => _$ApiErrorToJson(this);
}

@JsonSerializable()
class PaginatedUserResponse {
  PaginatedUserResponse({
    required this.data,
    required this.page,
    required this.perPage,
    required this.total,
    required this.totalPages,
  });

  factory PaginatedUserResponse.fromJson(Map<String, dynamic> json) =>
      _$PaginatedUserResponseFromJson(json);
  final List<ApiUserProfile> data;
  final int page;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int total;
  @JsonKey(name: 'total_pages')
  final int totalPages;
  Map<String, dynamic> toJson() => _$PaginatedUserResponseToJson(this);
}

@JsonSerializable()
class PaginatedChatMessageResponse {
  PaginatedChatMessageResponse({
    required this.data,
    required this.page,
    required this.perPage,
    required this.total,
    required this.totalPages,
  });

  factory PaginatedChatMessageResponse.fromJson(Map<String, dynamic> json) =>
      _$PaginatedChatMessageResponseFromJson(json);
  final List<ApiChatMessage> data;
  final int page;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int total;
  @JsonKey(name: 'total_pages')
  final int totalPages;
  Map<String, dynamic> toJson() => _$PaginatedChatMessageResponseToJson(this);
}

@JsonSerializable()
class PaginatedNotificationResponse {
  PaginatedNotificationResponse({
    required this.data,
    required this.page,
    required this.perPage,
    required this.total,
    required this.totalPages,
  });

  factory PaginatedNotificationResponse.fromJson(Map<String, dynamic> json) =>
      _$PaginatedNotificationResponseFromJson(json);
  final List<ApiNotification> data;
  final int page;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int total;
  @JsonKey(name: 'total_pages')
  final int totalPages;
  Map<String, dynamic> toJson() => _$PaginatedNotificationResponseToJson(this);
}

// -----------------------------------------------------------------------------
// Blocked User Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiBlockedUser {
  ApiBlockedUser({
    required this.id,
    required this.userId,
    required this.blockedUserId,
    required this.blockedAt,
    this.blockedUserName,
    this.blockedUserUsername,
    this.reason,
  });

  factory ApiBlockedUser.fromJson(Map<String, dynamic> json) =>
      _$ApiBlockedUserFromJson(json);
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'blocked_user_id')
  final String blockedUserId;
  @JsonKey(name: 'blocked_user_name')
  final String? blockedUserName;
  @JsonKey(name: 'blocked_user_username')
  final String? blockedUserUsername;
  @JsonKey(name: 'blocked_at')
  final DateTime blockedAt;
  final String? reason;
  Map<String, dynamic> toJson() => _$ApiBlockedUserToJson(this);
}

// -----------------------------------------------------------------------------
// Media Files Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiMediaFile {
  ApiMediaFile({
    required this.id,
    required this.userId,
    required this.filename,
    required this.originalFilename,
    required this.contentType,
    required this.sizeBytes,
    required this.bucketName,
    required this.objectKey,
    required this.createdAt,
    this.metadata,
  });

  factory ApiMediaFile.fromJson(Map<String, dynamic> json) =>
      _$ApiMediaFileFromJson(json);

  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  final String filename;
  @JsonKey(name: 'original_filename')
  final String originalFilename;
  @JsonKey(name: 'content_type')
  final String contentType;
  @JsonKey(name: 'size_bytes')
  final int sizeBytes;
  @JsonKey(name: 'bucket_name')
  final String bucketName;
  @JsonKey(name: 'object_key')
  final String objectKey;
  final Map<String, dynamic>? metadata;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  Map<String, dynamic> toJson() => _$ApiMediaFileToJson(this);
}

// -----------------------------------------------------------------------------
// Call Sessions Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiCallSession {
  ApiCallSession({
    required this.id,
    required this.initiatorId,
    required this.participants,
    required this.callType,
    required this.startedAt,
    this.bubbleId,
    this.endedAt,
    this.durationSeconds = 0,
    this.metadata,
  });

  factory ApiCallSession.fromJson(Map<String, dynamic> json) =>
      _$ApiCallSessionFromJson(json);

  final String id;
  @JsonKey(name: 'bubble_id')
  final String? bubbleId;
  @JsonKey(name: 'initiator_id')
  final String initiatorId;
  final List<String> participants;
  @JsonKey(name: 'call_type')
  final String callType;
  @JsonKey(name: 'started_at')
  final DateTime startedAt;
  @JsonKey(name: 'ended_at')
  final DateTime? endedAt;
  @JsonKey(name: 'duration_seconds')
  final int durationSeconds;
  final Map<String, dynamic>? metadata;

  Map<String, dynamic> toJson() => _$ApiCallSessionToJson(this);
}

// -----------------------------------------------------------------------------
// Bubble Requests Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiBubbleRequest {
  ApiBubbleRequest({
    required this.id,
    required this.requestType,
    required this.requesterId,
    required this.status,
    required this.expiresAt,
    required this.createdAt,
    required this.updatedAt,
    this.bubbleId,
    this.targetUserId,
    this.requiresUnanimous = true,
    this.completedAt,
  });

  factory ApiBubbleRequest.fromJson(Map<String, dynamic> json) =>
      _$ApiBubbleRequestFromJson(json);

  final String id;
  @JsonKey(name: 'request_type')
  final String requestType; // BubbleRequestType
  @JsonKey(name: 'bubble_id')
  final String? bubbleId;
  @JsonKey(name: 'requester_id')
  final String requesterId;
  @JsonKey(name: 'target_user_id')
  final String? targetUserId;
  final String status; // RequestStatus
  @JsonKey(name: 'requires_unanimous')
  final bool requiresUnanimous;
  @JsonKey(name: 'expires_at')
  final DateTime expiresAt;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  @JsonKey(name: 'completed_at')
  final DateTime? completedAt;

  Map<String, dynamic> toJson() => _$ApiBubbleRequestToJson(this);
}

// -----------------------------------------------------------------------------
// Request Votes Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiRequestVote {
  ApiRequestVote({
    required this.id,
    required this.requestId,
    required this.voterId,
    required this.vote,
    required this.votedAt,
    required this.createdAt,
  });

  factory ApiRequestVote.fromJson(Map<String, dynamic> json) =>
      _$ApiRequestVoteFromJson(json);

  final String id;
  @JsonKey(name: 'request_id')
  final String requestId;
  @JsonKey(name: 'voter_id')
  final String voterId;
  final String vote; // VoteType
  @JsonKey(name: 'voted_at')
  final DateTime votedAt;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  Map<String, dynamic> toJson() => _$ApiRequestVoteToJson(this);
}

// -----------------------------------------------------------------------------
// User Relationships Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiUserRelationship {
  ApiUserRelationship({
    required this.id,
    required this.fromUserId,
    required this.toUserId,
    required this.relationshipType,
    required this.createdAt,
    required this.updatedAt,
    this.status = 'active',
    this.createdBy,
    this.reason,
    this.metadata,
    this.expiresAt,
  });

  factory ApiUserRelationship.fromJson(Map<String, dynamic> json) =>
      _$ApiUserRelationshipFromJson(json);

  final String id;
  @JsonKey(name: 'from_user_id')
  final String fromUserId;
  @JsonKey(name: 'to_user_id')
  final String toUserId;
  @JsonKey(name: 'relationship_type')
  final String relationshipType; // UserRelationshipType
  final String status; // UserRelationshipStatus
  @JsonKey(name: 'created_by')
  final String? createdBy;
  final String? reason;
  final Map<String, dynamic>? metadata;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  @JsonKey(name: 'expires_at')
  final DateTime? expiresAt;

  Map<String, dynamic> toJson() => _$ApiUserRelationshipToJson(this);
}

// -----------------------------------------------------------------------------
// Support Ticket Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiSupportTicket {
  ApiSupportTicket({
    required this.id,
    required this.userId,
    required this.subject,
    required this.description,
    required this.status,
    required this.priority,
    required this.category,
    required this.createdAt,
    this.updatedAt,
    this.resolvedAt,
    this.assignedTo,
  });

  factory ApiSupportTicket.fromJson(Map<String, dynamic> json) =>
      _$ApiSupportTicketFromJson(json);
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  final String subject;
  final String description;
  final String status;
  final String priority;
  final String category;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  @JsonKey(name: 'resolved_at')
  final DateTime? resolvedAt;
  @JsonKey(name: 'assigned_to')
  final String? assignedTo;
  Map<String, dynamic> toJson() => _$ApiSupportTicketToJson(this);
}

// -----------------------------------------------------------------------------
// Enhanced Bubble Response Models
// -----------------------------------------------------------------------------

/// API model for bubble member response
@JsonSerializable()
class BubbleMemberResponse {
  const BubbleMemberResponse({
    required this.userId,
    required this.joinedAt,
    required this.status,
    required this.isOnline,
    required this.unreadMessageCount,
    this.leftAt,
    this.leaveReason,
  });

  factory BubbleMemberResponse.fromJson(Map<String, dynamic> json) =>
      _$BubbleMemberResponseFromJson(json);
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'joined_at')
  final DateTime joinedAt;
  final String status;
  @JsonKey(name: 'is_online')
  final bool isOnline;
  @JsonKey(name: 'left_at')
  final DateTime? leftAt;
  @JsonKey(name: 'leave_reason')
  final String? leaveReason;
  @JsonKey(name: 'unread_message_count')
  final int unreadMessageCount;
  Map<String, dynamic> toJson() => _$BubbleMemberResponseToJson(this);
}

/// API model for bubble response
@JsonSerializable()
class BubbleResponse {
  const BubbleResponse({
    required this.id,
    required this.name,
    required this.capacity,
    required this.members,
    required this.createdAt,
    required this.status,
    this.endDate,
  });

  factory BubbleResponse.fromJson(Map<String, dynamic> json) =>
      _$BubbleResponseFromJson(json);
  final String id;
  final String name;
  final int capacity;
  final List<BubbleMemberResponse> members;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'end_date')
  final DateTime? endDate;
  final String status;
  Map<String, dynamic> toJson() => _$BubbleResponseToJson(this);
}

/// Generic API Response wrapper
class ApiResponse<T> {
  const ApiResponse({
    required this.data,
    this.success = true,
    this.message,
    this.statusCode,
  });

  factory ApiResponse.success(T data, {String? message, int? statusCode}) =>
      ApiResponse(data: data, message: message, statusCode: statusCode);

  factory ApiResponse.error(String message, {int? statusCode}) => ApiResponse(
    data: null as T,
    success: false,
    message: message,
    statusCode: statusCode,
  );
  final T data;
  final bool success;
  final String? message;
  final int? statusCode;
}

// -----------------------------------------------------------------------------
// Media/File Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class MediaFile {
  MediaFile({
    required this.id,
    @JsonKey(name: 'user_id') required this.userId,
    @JsonKey(name: 'file_name') required this.fileName,
    @JsonKey(name: 'original_name') required this.originalName,
    @JsonKey(name: 'content_type') required this.contentType,
    required this.size,
    @JsonKey(name: 'bucket_name') required this.bucketName,
    @JsonKey(name: 'object_key') required this.objectKey,
    required this.url,
    required this.category,
    @JsonKey(name: 'is_public') required this.isPublic,
    @JsonKey(name: 'created_at') this.createdAt,
    @JsonKey(name: 'updated_at') this.updatedAt,
  });

  factory MediaFile.fromJson(Map<String, dynamic> json) => _$MediaFileFromJson(json);
  final String id;
  final String userId;
  final String fileName;
  final String originalName;
  final String contentType;
  final int size;
  final String bucketName;
  final String objectKey;
  final String url;
  final String category;
  final bool isPublic;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Map<String, dynamic> toJson() => _$MediaFileToJson(this);
}

@JsonSerializable()
class UploadParams {
  UploadParams({required this.category, @JsonKey(name: 'is_public') this.isPublic = false});

  factory UploadParams.fromJson(Map<String, dynamic> json) => _$UploadParamsFromJson(json);
  final String category;
  @JsonKey(name: 'is_public')
  final bool isPublic;
  Map<String, dynamic> toJson() => _$UploadParamsToJson(this);
}

@JsonSerializable()
class UploadResponse {
  UploadResponse({required this.success, required this.file, required this.message});

  factory UploadResponse.fromJson(Map<String, dynamic> json) => _$UploadResponseFromJson(json);
  final bool success;
  final MediaFile file;
  final String message;
  Map<String, dynamic> toJson() => _$UploadResponseToJson(this);
}

import 'package:flutter/material.dart';
import '../../statefulbusinesslogic/core/models/user_bubble_status.dart';

/// A widget that displays a stylized badge indicating a user's bubble status.
///
/// This badge visually represents one of the [BubbleMembershipStatus] values with distinct
/// colors and shadows for better readability. It supports responsive sizing
/// based on the screen dimensions when no explicit font size is provided.
class BubbleStatusBadge extends StatelessWidget {
  /// Creates a BubbleStatusBadge.
  ///
  /// [status] determines the appearance and text of the badge.
  /// [fontSize] allows customizing the text size. If null, a responsive size will be calculated.
  /// [horizontalPadding] allows customizing the horizontal padding. If null, a responsive padding will be calculated.
  /// [verticalPadding] allows customizing the vertical padding. If null, a responsive padding will be calculated.
  const BubbleStatusBadge({
    required this.status,
    super.key,
    this.fontSize,
    this.horizontalPadding,
    this.verticalPadding,
  });
  final BubbleMembershipStatus status;
  final double? fontSize;
  final double? horizontalPadding;
  final double? verticalPadding;

  @override
  Widget build(BuildContext context) {
    late final Color? backgroundColor;
    late final Gradient? backgroundGradient;
    late final String label;
    late final Color shadowColor;

    // Calculate responsive sizes if not explicitly provided
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // Match the filter chip sizing from contacts page
    final actualFontSize = fontSize ?? (screenHeight * 0.010).clamp(7.0, 9.0);
    final actualVerticalPadding =
        verticalPadding ?? (screenHeight * 0.0025).clamp(1.5, 2.5);
    final actualHorizontalPadding =
        horizontalPadding ?? (screenWidth * 0.010).clamp(4.0, 6.0);

    switch (status) {
      case BubbleMembershipStatus.noBubble:
        backgroundColor = const Color(0xFF67E8F9);
        shadowColor = const Color(0xFF67E8F9);
        label = 'no bubble';
        backgroundGradient = null;
        break;
      case BubbleMembershipStatus.notFullBubble:
        backgroundColor = null;
        backgroundGradient = const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF00C4FF), Color(0xFF00A3E0)],
        );
        shadowColor = const Color(0xFF00A3E0);
        label = 'in a bubble';
        break;
      case BubbleMembershipStatus.fullBubble:
        backgroundColor = const Color(0xFF7B8FA1);
        shadowColor = const Color(0xFF7B8FA1);
        label = 'bubble full';
        backgroundGradient = null;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: actualHorizontalPadding,
        vertical: actualVerticalPadding,
      ),
      decoration: ShapeDecoration(
        color: backgroundColor,
        gradient: backgroundGradient,
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(5),
        ),
        shadows: [
          BoxShadow(
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0.5,
            color: shadowColor.withValues(alpha: 0.5),
          ),
        ],
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: actualFontSize,
          color: Colors.white,
          height: 1,
        ),
      ),
    );
  }
}

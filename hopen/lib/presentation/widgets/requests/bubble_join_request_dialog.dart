import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../statefulbusinesslogic/bloc/bubble/bubble_bloc.dart';
import '../../../statefulbusinesslogic/bloc/bubble/bubble_event.dart';
import '../../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../gradient_background.dart';
import '../../utils/color_transition_controller.dart';
import '../../../statefulbusinesslogic/bloc/bubble_request/bubble_request_bloc.dart';
import '../../../statefulbusinesslogic/bloc/bubble_request/bubble_request_event.dart';
import '../../../statefulbusinesslogic/bloc/bubble_request/bubble_request_state.dart';
import '../../../statefulbusinesslogic/bloc/bubble_join_request/bubble_join_request_bloc.dart';
import '../../../statefulbusinesslogic/bloc/bubble_join_request/bubble_join_request_event.dart';
import '../../../statefulbusinesslogic/bloc/bubble_join_request/bubble_join_request_state.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_event.dart';
import '../animated_gradient_text.dart';

/// A dialog that appears when a contact requests to join the current user's bubble.
///
/// Features:
/// * Displays the requester's profile picture and name
/// * Shows the timestamp of when the request was made
/// * Provides accept/decline buttons with appropriate confirmation messages
/// * Uses the same design pattern and UI style as the existing dialogs
/// * Includes animations and transitions consistent with other dialogs
class BubbleJoinRequestDialog extends StatefulWidget {
  const BubbleJoinRequestDialog({
    super.key,
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.bubbleName,
    required this.members,
    required this.bubbleId,
    this.message,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final String bubbleName;
  final List<BubbleMember> members;
  final String bubbleId;
  final String? message;

  /// Shows the bubble join request dialog.
  static Future<bool?> show(
    BuildContext context, {
    required String requestId,
    required String requesterId,
    required String requesterName,
    required String bubbleName,
    required List<BubbleMember> members,
    required String bubbleId,
    String? message,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false, // Make the dialog non-dismissible
      barrierColor: Colors.black.withValues(alpha: 0.9),
      builder: (context) => BubbleJoinRequestDialog(
        requestId: requestId,
        requesterId: requesterId,
        requesterName: requesterName,
        bubbleName: bubbleName,
        members: members,
        bubbleId: bubbleId,
        message: message,
      ),
    );
  }

  @override
  State<BubbleJoinRequestDialog> createState() =>
      _BubbleJoinRequestDialogState();
}

class _BubbleJoinRequestDialogState extends State<BubbleJoinRequestDialog> {
  late BubbleJoinRequestBloc _bubbleJoinRequestBloc;
  bool _isSubmitting = false;
  String? _errorMessage;

  /// Controller for managing color transitions
  late ColorTransitionController _colorController;

  @override
  void initState() {
    super.initState();
    _bubbleJoinRequestBloc = BlocProvider.of<BubbleJoinRequestBloc>(context);
    _colorController = ColorTransitionController();
    _colorController.startColorLoop();

    // Listen to color changes and update the UI
    _colorController.addListener(() {
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _colorController.dispose();
    super.dispose();
  }

  double _getImageSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 370) {
      return 120;
    } else if (width < 600) {
      return 140;
    } else {
      return 150;
    }
  }

  double _getDialogVerticalSpacing(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 370) {
      return 6; // Match friends_choice_dialog spacing
    } else if (width < 600) {
      return 8; // Match friends_choice_dialog spacing
    } else {
      return 10; // Match friends_choice_dialog spacing
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d').format(timestamp);
    }
  }

  @override
  Widget build(BuildContext context) =>
      BlocListener<BubbleJoinRequestBloc, BubbleJoinRequestState>(
        listener: (context, state) {
          if (state.status == BubbleJoinRequestStatus.accepting) {
            setState(() {
              _isSubmitting = true;
              _errorMessage = null;
            });
          } else if (state.status == BubbleJoinRequestStatus.accepted) {
            setState(() {
              _isSubmitting = false;
              _errorMessage = null;
            });
            Navigator.of(context).pop(true);
          } else if (state.status == BubbleJoinRequestStatus.declining) {
            setState(() {
              _isSubmitting = true;
              _errorMessage = null;
            });
          } else if (state.status == BubbleJoinRequestStatus.declined) {
            setState(() {
              _isSubmitting = false;
              _errorMessage = null;
            });
            Navigator.of(context).pop(false);
          } else if (state.status == BubbleJoinRequestStatus.error) {
            setState(() {
              _isSubmitting = false;
              _errorMessage = state.errorMessage ?? 'An error occurred';
            });
          }
        },
        child: _buildDialogContent(context),
      );

  Widget _buildDialogContent(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    // Get current color and glow effect from the controller
    final currentActiveColor = _colorController.currentColor;
    final titleGlowEffect = _colorController.getGlowEffect();

    final imageSize = _getImageSize(context);
    final verticalSpacing = ColorTransitionController.getDialogVerticalSpacing(
      context,
    );

    return AlertDialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(25),
      ), // Match friends_choice_dialog
      insetPadding: EdgeInsets.zero, // Match friends_choice_dialog
      titlePadding: const EdgeInsets.only(
        top: 16, // Match friends_choice_dialog
      ),
      contentPadding:
          const EdgeInsets.symmetric(), // Match friends_choice_dialog
      title: Center(
        // Title itself is a Column, acting as main content area before buttons
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedGradientText(
              text: 'Bubble request',
              style: TextStyle(
                fontFamily: 'Omnes',
                fontSize: ColorTransitionController.getTitleSize(context),
                fontWeight: FontWeight.bold,
              ),
              colors: const <Color>[
                Color(0xFFFF00FF), // #f0f
                Color(0xFFFB43BB), // #fb43bb
                Color(0xFFF3C935), // #f3c935
                Color(0xFFF0FF00), // #f0ff00
                Color(0xFFC4FF2D), // #c4ff2d
                Color(0xFF91FF64), // #91ff64
                Color(0xFF64FF93), // #64ff93
                Color(0xFF40FFBA), // #40ffba
                Color(0xFF24FFD8), // #24ffd8
                Color(0xFF10FFED), // #10ffed
                Color(0xFF04FFFA), // #04fffa
                Color(0xFF00FFFF), // aqua
              ],
              stops: const <double>[
                0,
                0.12,
                0.37,
                0.48,
                0.53,
                0.6,
                0.67,
                0.74,
                0.81,
                0.88,
                0.94,
                1,
              ],
              duration: const Duration(seconds: 8),
            ),
            SizedBox(height: verticalSpacing * 8), // Spacing after title
            _buildRequesterProfile(context, imageSize * 1.4),
            SizedBox(height: verticalSpacing * 4), // Spacing after profile pic
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
              ), // Reduced if text is centered well
              child: Text(
                '${widget.requesterName} wants to join your bubble',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: ColorTransitionController.getSubtitleSize(context),
                  height: 1.4, // Added line height for readability
                ),
              ),
            ),
            SizedBox(height: verticalSpacing * 0.8), // Spacing after main text
            Text(
              'Request sent ${_formatTimestamp(DateTime.now())}',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white54,
                fontSize:
                    ColorTransitionController.getSubtitleSize(context) * 0.8,
              ),
            ),
            // SizedBox(height: verticalSpacing * 0.5), // Removed, actions padding will handle space to buttons
          ],
        ),
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width,
        height:
            screenHeight *
            0.1, // Smaller content area since we don't have a list like in friends_choice_dialog
      ),
      actionsPadding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 16,
      ), // Match friends_choice_dialog style
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        Column(
          // Kept Column to allow for _errorMessage if needed above buttons
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  _errorMessage!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.red, fontSize: 14),
                ),
              ),
            SizedBox(
              width: double.infinity,
              height: 50, // Match friends_choice_dialog button height
              child: Row(
                children: [
                  Expanded(child: _buildDeclineButton(context)),
                  const SizedBox(width: 16), // Explicit spacing between buttons
                  Expanded(child: _buildAcceptButton(context)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRequesterProfile(BuildContext context, double imageSize) =>
      Container(
        width: imageSize,
        height: imageSize,
        decoration: ShapeDecoration(
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(imageSize * 0.4),
            side: BorderSide(
              color: Colors.white.withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          color: Colors.white.withValues(alpha: 0.1),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(imageSize * 0.4),
          child: widget.members[0].profilePicUrl != null
              ? Image.network(
                  widget.members[0].profilePicUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => _buildFallbackAvatar(imageSize),
                )
              : _buildFallbackAvatar(imageSize),
        ),
      );

  Widget _buildFallbackAvatar(double imageSize) => Center(
    child: Text(
      widget.bubbleName.isNotEmpty
          ? widget.bubbleName[0].toUpperCase()
          : '?',
      style: TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
        fontSize: imageSize * 0.4,
      ),
    ),
  );

  Widget _buildDeclineButton(BuildContext context) => ElevatedButton(
    onPressed: () => _handleDecline(),
    style: ElevatedButton.styleFrom(
      backgroundColor: const Color(0xFF4A4A4A), // Darker grey for decline
      foregroundColor: Colors.white,
      disabledBackgroundColor: Colors.grey.withValues(alpha: 0.3),
      disabledForegroundColor: Colors.white.withValues(alpha: 0.5),
      shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(18)),
      padding: EdgeInsets.zero,
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    ).copyWith(
      elevation: WidgetStateProperty.all(
        0,
      ), // Explicitly set elevation to 0 if needed, or use theme
    ),
    child: const Center(
      // Ensure text is centered
      child: Text(
        'Decline',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    ),
  );

  Widget _buildAcceptButton(BuildContext context) => ElevatedButton(
    onPressed: () => _handleAccept(),
    style: ElevatedButton.styleFrom(
      backgroundColor: Colors.blue, // Original blue color
      foregroundColor: Colors.white,
      disabledBackgroundColor: Colors.grey.withValues(alpha: 0.3),
      disabledForegroundColor: Colors.white.withValues(alpha: 0.5),
      shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(18)),
      padding: EdgeInsets.zero,
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    ).copyWith(
      elevation: WidgetStateProperty.all(
        0,
      ), // Explicitly set elevation to 0 if needed
    ),
    child: const Center(
      // Ensure text is centered
      child: Text(
        'Accept',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    ),
  );

  void _handleAccept() {
    _bubbleJoinRequestBloc.add(
      AcceptBubbleJoinRequestEvent(
        requestId: widget.requestId,
        bubbleId: widget.bubbleId,
        requesterId: widget.requesterId,
      ),
    );
    // Notify UnifiedProfileBloc
    context.read<UnifiedProfileBloc>().add(
      BubbleRequestAcceptedEvent(targetUserId: widget.requesterId),
    );
  }

  void _handleDecline() {
    _bubbleJoinRequestBloc.add(
      DeclineBubbleJoinRequestEvent(
        requestId: widget.requestId,
        bubbleId: widget.bubbleId,
        requesterId: widget.requesterId,
      ),
    );
    // Notify UnifiedProfileBloc
    context.read<UnifiedProfileBloc>().add(
      BubbleRequestDeclinedEvent(targetUserId: widget.requesterId),
    );
  }
}

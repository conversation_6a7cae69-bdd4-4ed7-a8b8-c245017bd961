import 'dart:ui';

import 'package:flutter/foundation.dart'; // For kDebugMode
import 'package:flutter/material.dart';
// Import BLoC and Event
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../statefulbusinesslogic/bloc/call/call_bloc.dart';
import '../../statefulbusinesslogic/bloc/call/call_event.dart';
import '../pages/call/bubble_call_page.dart'; // Added import for navigation
// import 'package:hopen/presentation/pages/chat/bubble_chat_page.dart'; // Not directly used
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';
import 'animated_gradient_text.dart';
import 'custom_toast.dart';

/// A dialog that displays call options for bubble members using a glass-effect design.
///
/// This dialog presents three communication options positioned at the bottom of the screen
/// for easy thumb access:
/// 1. Audio Call - Initiates or joins a group audio call with all bubble members
/// 2. Video Call - Starts or joins a video conference with all bubble members
/// 3. Screen Share Call - Begins or joins a call with screen sharing capabilities
///
/// The buttons feature a modern glass-effect design with blur and transparency
/// effects that create a sophisticated and contemporary look.
class BubbleCallDialog extends StatelessWidget {
  const BubbleCallDialog({
    required this.members,
    required this.bubbleId,
    super.key,
    this.isJoining = false,
    this.activeCallId,
    this.currentBubbleName,
  });
  final List<BubbleMemberEntity> members;
  final String bubbleId;
  final bool isJoining;
  final String? activeCallId;
  final String? currentBubbleName;

  /// Shows the bubble call dialog.
  ///
  /// This is a convenience method that handles showing the dialog with the correct
  /// configuration for the glass effect and background.
  static Future<void> show(
    BuildContext context,
    List<BubbleMemberEntity> members,
    String bubbleId, {
    bool isJoining = false,
    String? activeCallId,
    String? currentBubbleName,
  }) => showDialog<void>(
    context: context,
    barrierColor: Colors.black.withValues(alpha: 0.85),
    builder:
        (context) => BubbleCallDialog(
          members: members,
          bubbleId: bubbleId,
          isJoining: isJoining,
          activeCallId: activeCallId,
          currentBubbleName: currentBubbleName,
        ),
  );

  /// Creates a glass-effect button with an SVG icon and label.
  ///
  /// Parameters:
  /// - [iconPath]: Path to the SVG icon asset
  /// - [label]: Text displayed below the button
  /// - [onPressed]: Callback function when the button is pressed
  /// - [blurSigma]: Amount of blur for the glass effect (default: 5.0)
  /// - [glassColor]: Base color of the glass effect (default: white)
  /// - [glassOpacity]: Opacity of the glass tint (default: 0.1)
  /// - [borderOpacity]: Opacity of the button border (default: 0.2)
  Widget _buildGlassButton({
    required String iconPath,
    required String label,
    required VoidCallback onPressed,
    double blurSigma = 5.0,
    Color glassColor = Colors.white,
    double glassOpacity = 0.1,
    double borderOpacity = 0.2,
    double buttonSize = 50.0,
    double iconSize = 20.0,
    double labelSpacing = 6.0,
    double labelFontSize = 12.0,
  }) {
    // Re-wrap button in Column and add SizedBox + Text for label
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ClipOval(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: blurSigma, sigmaY: blurSigma),
            child: Container(
              width: buttonSize,
              height: buttonSize,
              decoration: BoxDecoration(
                color: glassColor.withValues(alpha: glassOpacity),
                shape: BoxShape.circle,
                border: Border.all(
                  color: glassColor.withValues(alpha: borderOpacity),
                ),
              ),
              child: Material(
                color: Colors.transparent,
                shape: const CircleBorder(),
                child: InkWell(
                  customBorder: const CircleBorder(),
                  onTap: onPressed,
                  child: Center(
                    child: SvgPicture.asset(
                      iconPath,
                      width: iconSize,
                      height: iconSize,
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: labelSpacing), // Add spacing back
        SizedBox(
          width:
              buttonSize * 1.5, // Make label width proportional to button size
          child: Text(
            label,
            style: TextStyle(
              color: Colors.white,
              fontSize: labelFontSize,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;

    final titleText =
        isJoining ? "Join your bubble's call" : 'Call your bubble';

    final audioButtonLabel = isJoining ? 'Call' : 'Call';
    final videoButtonLabel = isJoining ? 'Video call' : 'Video call';
    final screenShareButtonLabel = isJoining ? 'Share screen' : 'Share screen';

    // Define the Hopen gradient colors and stops (as they were before)
    const hopenGradientColors = <Color>[
      Color(0xFFFF00FF), // #f0f
      Color(0xFFFB43BB), // #fb43bb
      Color(0xFFF3C935), // #f3c935
      Color(0xFFF0FF00), // #f0ff00
      Color(0xFFC4FF2D), // #c4ff2d
      Color(0xFF91FF64), // #91ff64
      Color(0xFF64FF93), // #64ff93
      Color(0xFF40FFBA), // #40ffba
      Color(0xFF24FFD8), // #24ffd8
      Color(0xFF10FFED), // #10ffed
      Color(0xFF04FFFA), // #04fffa
      Color(0xFF00FFFF), // aqua
    ];
    const hopenGradientStops = <double>[
      0,
      0.12,
      0.37,
      0.48,
      0.53,
      0.6,
      0.67,
      0.74,
      0.81,
      0.88,
      0.94,
      1,
    ];

    void handleCallAction(
      BuildContext ctx, {
      required bool withVideo,
      required bool withScreenShare,
    }) {
      Navigator.of(ctx).pop(); // Close dialog first
      if (isJoining) {
        if (kDebugMode) {
          print(
            'Join Call Action: BubbleID: $bubbleId, BubbleName: $currentBubbleName, ActiveCallID: $activeCallId, Video: $withVideo, ScreenShare: $withScreenShare.',
          );
        }
        // Dispatch JoinCallEvent to CallBloc
        if (activeCallId == null) {
          if (kDebugMode) {
            print(
              'Error: activeCallId is null when trying to join a call. BubbleID: $bubbleId',
            );
          }
          CustomToast.showError(ctx, 'Error: Call ID is missing, cannot join');
          return;
        }
        if (currentBubbleName == null) {
          if (kDebugMode) {
            print(
              'Error: currentBubbleName is null when trying to join a call. BubbleID: $bubbleId',
            );
          }
          CustomToast.showError(
            ctx,
            'Error: Bubble name is missing, cannot join',
          );
          return;
        }
        BlocProvider.of<CallBloc>(ctx).add(
          JoinCallEvent(
            bubbleId: bubbleId,
            callId: activeCallId!,
            withVideo: withVideo,
            withScreenShare: withScreenShare,
            bubbleName: currentBubbleName!,
          ),
        );

        // Navigate to BubbleCallPage
        Navigator.of(ctx).push(
          MaterialPageRoute(
            builder:
                (context) => BubbleCallPage(
                  bubbleId: bubbleId,
                  initialIsVideoEnabled: withVideo,
                  initialIsScreenSharing: withScreenShare,
                ),
          ),
        );
      } else {
        if (kDebugMode) {
          print(
            'Initiate Call Action: BubbleID: $bubbleId, BubbleName: $currentBubbleName, Video: $withVideo, ScreenShare: $withScreenShare.',
          );
        }
        // Dispatch InitiateCallEvent to CallBloc
        BlocProvider.of<CallBloc>(ctx).add(
          InitiateCallEvent(
            targetId: bubbleId, // For group calls, targetId is the bubbleId
            isGroup: true,
            withVideo: withVideo,
            withScreenShare: withScreenShare,
            targetName: currentBubbleName,
          ),
        );

        // Navigate to BubbleCallPage
        Navigator.of(ctx).push(
          MaterialPageRoute(
            builder:
                (context) => BubbleCallPage(
                  bubbleId: bubbleId,
                  initialIsVideoEnabled: withVideo,
                  initialIsScreenSharing: withScreenShare,
                ),
          ),
        );
      }
    }

    return FadeTransition(
      opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: ModalRoute.of(context)!.animation!,
          curve: Curves.easeOut,
        ),
      ),
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.05),
          end: Offset.zero,
        ).animate(
          CurvedAnimation(
            parent: ModalRoute.of(context)!.animation!,
            curve: Curves.easeOutCubic,
          ),
        ),
        child: Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: EdgeInsets.zero,
          child: Stack(
            children: [
              Positioned.fill(
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(color: Colors.transparent),
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AnimatedGradientText(
                      text: titleText,
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                      colors: hopenGradientColors,
                      stops: hopenGradientStops,
                      duration: const Duration(seconds: 8),
                    ),
                    const SizedBox(height: 24),
                    Padding(
                      padding: EdgeInsets.only(
                        bottom: bottomPadding + 96,
                        left: 24,
                        right: 24,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildGlassButton(
                            iconPath: 'assets/icons/phone.svg',
                            label: audioButtonLabel,
                            onPressed:
                                () => handleCallAction(
                                  context,
                                  withVideo: false,
                                  withScreenShare: false,
                                ),
                          ),
                          _buildGlassButton(
                            iconPath: 'assets/icons/video.svg',
                            label: videoButtonLabel,
                            onPressed:
                                () => handleCallAction(
                                  context,
                                  withVideo: true,
                                  withScreenShare: false,
                                ),
                          ),
                          _buildGlassButton(
                            iconPath: 'assets/icons/smartphone.svg',
                            label: screenShareButtonLabel,
                            onPressed:
                                () => handleCallAction(
                                  context,
                                  withVideo: false,
                                  withScreenShare: true,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

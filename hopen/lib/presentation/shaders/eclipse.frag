vec3 bluenoise( vec2 st ) {
    return texture( iChannel0, st / iChannelResolution[0].xy ).rgb * 2. - 1.;
}


float gradient( in float x ) {
	
    float t = iTime * 1.;
    
    float a = sin( t - x ) + cos( t * 3.39 - x * 2. ) + ( sin( t * 1.721 - x - 3842.18 )* .4);
	a *= .5;
    
    return a;
}

void mainImage( out vec4 fragColor, in vec2 fragCoord ) {

    vec2 uv = ( fragCoord -.5 * iResolution.xy ) / iResolution.y;
    uv.x = mix( 1. - uv.x, uv.x, smoothstep( .3, .45, length( uv ) ) );
    
   
    float shift = ( sin( iTime ) * .5 + .5 ) * .2 + .1;
    float noise = bluenoise( .75 * fragCoord + 1337. * fract( iTime ) ).r * .2;

	vec3 c = vec3(
    	gradient( uv.x + noise - shift ),
    	gradient( uv.x + noise ),
        gradient( uv.x + noise + shift )
    );
    
    fragColor = vec4( c, 1.0 );
}
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_event.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_state.dart';
import '../../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../../../statefulbusinesslogic/core/models/user_bubble_status.dart';
import '../../../statefulbusinesslogic/core/models/relationship_type.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart'
    as core_models;
import '../../../statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import '../../router/app_router.dart';
import '../../widgets/bubble_status_badge.dart';
import '../../widgets/custom_toast.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/online_status_indicator.dart';
import '../../widgets/rgb_shader_effect.dart';
import '../../widgets/shimmer_placeholders.dart';

// Mock data class, can remain the same for now if parts of UI still use it directly
class MutualConnection {
  MutualConnection({required this.id, required this.name, this.imageUrl});
  final String id;
  final String name;
  final String? imageUrl;
}

class UnifiedProfilePage extends StatefulWidget {
  final String userId;
  final String? bubbleStatusForBadge;
  final Color? profileNameColor;
  final DateTime? profileSinceDate;
  final String? profileSinceDateTextPrefix;
  final String? bottomButtonText;
  final bool isBottomButtonEnabled;
  final VoidCallback? onBottomButtonPressed;
  final VoidCallback? onMoreOptionsMute;
  final VoidCallback? onMoreOptionsBlock;
  final VoidCallback? onMoreOptionsReport;
  final VoidCallback? onMoreOptionsUnfriend;
  final VoidCallback? onAudioCallPressed;
  final VoidCallback? onVideoCallPressed;
  final VoidCallback? onScreenSharePressed;
  final bool showCallActionButtons;
  final String? infoDialogTitle;
  final String? infoDialogContent;

  const UnifiedProfilePage({
    Key? key,
    required this.userId,
    this.bubbleStatusForBadge,
    this.profileNameColor,
    this.profileSinceDate,
    this.profileSinceDateTextPrefix,
    this.bottomButtonText,
    this.isBottomButtonEnabled = true,
    this.onBottomButtonPressed,
    this.onMoreOptionsMute,
    this.onMoreOptionsBlock,
    this.onMoreOptionsReport,
    this.onMoreOptionsUnfriend,
    this.onAudioCallPressed,
    this.onVideoCallPressed,
    this.onScreenSharePressed,
    this.showCallActionButtons = false,
    this.infoDialogTitle,
    this.infoDialogContent,
  }) : super(key: key);

  @override
  State<UnifiedProfilePage> createState() => _UnifiedProfilePageState();
}

class _UnifiedProfilePageState extends State<UnifiedProfilePage> {
  List<BubbleMember> _bubbleMembers = [];
  NavBarVisibilityNotifier? _navBarNotifier;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Store reference to NavBarVisibilityNotifier to safely use in dispose
    _navBarNotifier = Provider.of<NavBarVisibilityNotifier>(
      context,
      listen: false,
    );
  }

  @override
  void initState() {
    super.initState();

    // Use addPostFrameCallback to safely access context after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _navBarNotifier?.hideNavBar();

        // Load user profile data
        context.read<UnifiedProfileBloc>().add(
          LoadUnifiedProfile(userId: widget.userId),
        );
      }
    });
  }

  @override
  void dispose() {
    // Use stored reference instead of Provider.of in dispose
    _navBarNotifier?.showNavBar();
    super.dispose();
  }

  void _showSampleInfoDialog(
    BuildContext dialogContext,
    String title,
    String content,
  ) {
    showDialog(
      context: dialogContext,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF1A2B4D), // Much darker background
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
            title: Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Color(0xFF00FFFF),
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            content: Text(
              content,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            actionsAlignment: MainAxisAlignment.center,
            actions: [
              TextButton(
                child: const Text(
                  'OK',
                  style: TextStyle(color: Color(0xFF00FFFF)),
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
    );
  }

  // Responsive text size helpers (copied from original)
  double _getHeadingSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 360
        ? 22.0
        : width < 600
        ? 26.0
        : 30.0;
  }

  double _getSubheadingSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 360
        ? 12.0
        : width < 600
        ? 14.0
        : 16.0;
  }

  double _getBodyTextSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 360
        ? 10.0
        : width < 600
        ? 12.0
        : 14.0;
  }

  double _getAppBarTextSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 360
        ? 14.0
        : width < 600
        ? 16.0
        : 18.0;
  }

  Widget _buildBottomActionButton(
    BuildContext context,
    String buttonText,
    bool isEnabled,
    VoidCallback? onPressed,
    String? dialogTitle,
    String? dialogContent,
  ) {
    final screenHeight = MediaQuery.of(context).size.height;
    final fieldHeight = screenHeight / 16;

    final ButtonStyle buttonStyle;
    var effectiveOnPressed = onPressed;

    if (isEnabled) {
      buttonStyle = ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withValues(alpha: 0.1),
        foregroundColor: Colors.white,
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
          side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
        padding: EdgeInsets.zero,
        minimumSize: const Size(0, 0),
        maximumSize: const Size(double.infinity, double.infinity),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      );
    } else {
      buttonStyle = ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withValues(alpha: 0.1),
        foregroundColor: Colors.white.withValues(alpha: 0.5),
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
          side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
        padding: EdgeInsets.zero,
        minimumSize: const Size(0, 0),
        maximumSize: const Size(double.infinity, double.infinity),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      );
      // If not enabled and an info dialog is specified for the sample, show it on tap
      if (dialogTitle != null && dialogContent != null) {
        effectiveOnPressed = () {
          // Check if widget is still mounted before using context
          if (!mounted) return;

          _showSampleInfoDialog(context, dialogTitle, dialogContent);
        };
      } else {
        effectiveOnPressed = null; // Otherwise, truly disabled
      }
    }

    return Padding(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        bottom: MediaQuery.of(context).padding.bottom + 10,
        top: 10,
      ),
      child: SizedBox(
        width: double.infinity,
        height: fieldHeight,
        child: ElevatedButton(
          style: buttonStyle,
          onPressed: effectiveOnPressed,
          child: Center(
            child: Text(
              buttonText,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGlassButton({
    required BuildContext context,
    required String iconPath,
    required String label,
    required VoidCallback onPressed,
    double blurSigma = 5.0,
    Color glassColor = Colors.white,
    double glassOpacity = 0.1,
    double borderOpacity = 0.2,
    double buttonSize = 50.0,
    double iconSize = 20.0,
    double labelSpacing = 6.0,
    double labelFontSize = 12.0,
  }) => Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          // Enhanced shadow system following Material Design elevation principles
          boxShadow: [
            // Primary shadow - provides main depth
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, 4),
            ),
            // Secondary shadow - adds ambient lighting effect
            BoxShadow(
              color: Colors.black.withOpacity(0.12),
              blurRadius: 6,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
            // Tertiary shadow - subtle ambient glow
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 16,
              spreadRadius: -4,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: ClipOval(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: blurSigma, sigmaY: blurSigma),
            child: Container(
              width: buttonSize,
              height: buttonSize,
              decoration: BoxDecoration(
                color: glassColor.withValues(alpha: glassOpacity),
                shape: BoxShape.circle,
                border: Border.all(
                  color: glassColor.withValues(alpha: borderOpacity),
                  width: 1.0,
                ),
                // Inner shadow for glass effect depth
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.1),
                    blurRadius: 1,
                    spreadRadius: 0,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                shape: const CircleBorder(),
                child: InkWell(
                  customBorder: const CircleBorder(),
                  onTap: onPressed,
                  splashColor: glassColor.withOpacity(0.2),
                  highlightColor: glassColor.withOpacity(0.1),
                  child: Center(
                    child: SvgPicture.asset(
                      iconPath,
                      width: iconSize,
                      height: iconSize,
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      SizedBox(height: labelSpacing),
      // Enhanced text shadow for better readability
      Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Text(
          label,
          style: TextStyle(
            color: Colors.white,
            fontSize: labelFontSize,
            fontWeight: FontWeight.w500,
            shadows: [
              Shadow(
                color: Colors.black.withOpacity(0.5),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          textAlign: TextAlign.center,
        ),
      ),
    ],
  );

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UnifiedProfileBloc, UnifiedProfileState>(
      builder: (context, state) {
        Widget content;

        if (state is UnifiedProfileInitial || state is UnifiedProfileLoading) {
          content = _buildProfileContentWithShimmer();
        } else if (state is UnifiedProfileLoaded) {
          content = _buildProfileContent(state.user, state);
        } else if (state is UnifiedProfileContactRequestSent) {
          content = _buildProfileContent(state.user, state);
        } else if (state is UnifiedProfileBubbleRequestSent) {
          content = _buildProfileContent(state.user, state);
        } else if (state is UnifiedProfileActionSuccess) {
          content = _buildProfileContent(state.user, state);
        } else if (state is UnifiedProfileError) {
          content = _buildProfileContentWithError(state.message);
        } else {
          content = const Center(child: Text('Something went wrong'));
        }

        // Wrap content in FadeTransition to match main page transitions
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 150),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(opacity: animation, child: child);
          },
          child: content,
        );
      },
    );
  }

  RelationshipType _getRelationshipType(UnifiedProfileState state) {
    if (state is UnifiedProfileLoaded) {
      return state.relationshipType;
    } else if (state is UnifiedProfileContactRequestSent) {
      return state.relationshipType;
    } else if (state is UnifiedProfileBubbleRequestSent) {
      return state.relationshipType;
    }
    return RelationshipType.none;
  }

  /// Builds the profile content with shimmer placeholders during loading
  Widget _buildProfileContentWithShimmer() {
    final screenSize = MediaQuery.of(context).size;

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Column(
          children: [
            Expanded(
              child: CustomScrollView(
                slivers: <Widget>[
                  SliverAppBar(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () {
                        if (!mounted) return;
                        if (Navigator.of(context).canPop()) {
                          Navigator.of(context).pop();
                        } else {
                          context.go(AppRoutes.bubble);
                        }
                      },
                    ),
                    title: ShimmerPlaceholders.username(width: 120, height: 18),
                    actions: [
                      PopupMenuButton<String>(
                        icon: const Icon(Icons.more_vert, color: Colors.white),
                        onSelected: (result) {
                          // Disabled during loading
                        },
                        itemBuilder:
                            (context) => const [
                              PopupMenuItem<String>(
                                value: 'mute',
                                child: Text('Mute Notifications'),
                              ),
                              PopupMenuItem<String>(
                                value: 'block',
                                child: Text(
                                  'Block User',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ),
                              PopupMenuItem<String>(
                                value: 'report',
                                child: Text(
                                  'Report User',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ),
                            ],
                        color: Colors.white.withValues(alpha: 0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: Colors.white.withValues(alpha: 0.3),
                          ),
                        ),
                      ),
                    ],
                    expandedHeight: screenSize.height * 0.40,
                    flexibleSpace: FlexibleSpaceBar(
                      background: ShaderMask(
                        shaderCallback:
                            (bounds) => LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.black,
                                Colors.black.withValues(alpha: 0.9),
                                Colors.transparent,
                              ],
                              stops: const [0.0, 0.7, 1.0],
                            ).createShader(bounds),
                        blendMode: BlendMode.dstIn,
                        child: ShimmerPlaceholders.createAccessibleShimmer(
                          child: Container(
                            width: double.infinity,
                            height: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.5),
                            ),
                          ),
                        ),
                      ),
                    ),
                    pinned: true,
                  ),
                  SliverToBoxAdapter(
                    child: Transform.translate(
                      offset: Offset(0, -(screenSize.height * 0.03)),
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Flexible(
                                  child:
                                      ShimmerPlaceholders.createAccessibleShimmer(
                                        child: Container(
                                          width: 200,
                                          height: _getHeadingSize(context),
                                          decoration: BoxDecoration(
                                            color: Colors.white.withValues(
                                              alpha: 0.3,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                        ),
                                      ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left: 8),
                                  child: ShimmerPlaceholders.onlineStatus(),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left: 8),
                                  child:
                                      ShimmerPlaceholders.bubbleStatusBadge(),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            ShimmerPlaceholders.profileSinceDate(width: 150),
                            Padding(
                              padding: const EdgeInsets.only(top: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ShimmerPlaceholders.mutualConnectionsSection(
                                    title: "friends in common",
                                    avatarCount: 5,
                                  ),
                                  const SizedBox(height: 16),
                                  ShimmerPlaceholders.mutualConnectionsSection(
                                    title: "contacts in common",
                                    avatarCount: 3,
                                  ),
                                  const SizedBox(height: 16),
                                  ShimmerPlaceholders.mutualConnectionsSection(
                                    title: "bubblers",
                                    avatarCount: 4,
                                  ),
                                ],
                              ),
                            ),
                            if (widget.showCallActionButtons)
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 20,
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    ShimmerPlaceholders.callActionButton(),
                                    ShimmerPlaceholders.callActionButton(),
                                    ShimmerPlaceholders.callActionButton(),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 24,
                right: 24,
                bottom: MediaQuery.of(context).padding.bottom + 10,
                top: 10,
              ),
              child: SizedBox(
                width: double.infinity,
                height: MediaQuery.of(context).size.height / 16,
                child: ShimmerPlaceholders.actionButton(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the profile content with error indicators while maintaining page structure
  Widget _buildProfileContentWithError(String errorMessage) {
    final screenSize = MediaQuery.of(context).size;

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Column(
          children: [
            Expanded(
              child: CustomScrollView(
                slivers: <Widget>[
                  SliverAppBar(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () {
                        if (!mounted) return;
                        if (Navigator.of(context).canPop()) {
                          Navigator.of(context).pop();
                        } else {
                          context.go(AppRoutes.bubble);
                        }
                      },
                    ),
                    title: const Text(
                      'Profile error',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    expandedHeight: screenSize.height * 0.40,
                    flexibleSpace: FlexibleSpaceBar(
                      background: ShaderMask(
                        shaderCallback:
                            (bounds) => LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.black,
                                Colors.black.withValues(alpha: 0.9),
                                Colors.transparent,
                              ],
                              stops: const [0.0, 0.7, 1.0],
                            ).createShader(bounds),
                        blendMode: BlendMode.dstIn,
                        child: ColoredBox(
                          color: Colors.white.withValues(alpha: 0.1),
                          child: Image.asset(
                            'assets/images/error/planet.png',
                            width: screenSize.height * 0.15,
                            height: screenSize.height * 0.15,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                    pinned: true,
                  ),
                  SliverToBoxAdapter(
                    child: Transform.translate(
                      offset: Offset(0, -(screenSize.height * 0.03)),
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              'Houston, we\'ve had a problem...',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: _getHeadingSize(context),
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              errorMessage,
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: _getBodyTextSize(context),
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 24),
                            SizedBox(
                              height: MediaQuery.of(context).size.height / 16,
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  if (!mounted) return;
                                  context.read<UnifiedProfileBloc>().add(
                                    LoadUnifiedProfile(userId: widget.userId),
                                  );
                                },
                                icon: const Icon(Icons.refresh),
                                label: const Text('Retry'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.white.withValues(
                                    alpha: 0.1,
                                  ),
                                  foregroundColor: Colors.white,
                                  shape: RoundedSuperellipseBorder(
                                    borderRadius: BorderRadius.circular(18),
                                    side: BorderSide(
                                      color: Colors.white.withValues(
                                        alpha: 0.3,
                                      ),
                                    ),
                                  ),
                                  padding: EdgeInsets.zero,
                                  minimumSize: Size.zero,
                                  maximumSize: const Size(
                                    double.infinity,
                                    double.infinity,
                                  ),
                                  tapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                  fixedSize: Size.fromHeight(
                                    MediaQuery.of(context).size.height / 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 24,
                right: 24,
                bottom: MediaQuery.of(context).padding.bottom + 10,
                top: 10,
              ),
              child: SizedBox(
                width: double.infinity,
                height: MediaQuery.of(context).size.height / 16,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                    foregroundColor: Colors.white.withValues(alpha: 0.5),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                      side: BorderSide(
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                    ),
                  ),
                  onPressed: null,
                  child: const Text(
                    'Unable to load profile',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileContent(
    core_models.UserModel user,
    UnifiedProfileState state,
  ) {
    final screenSize = MediaQuery.of(context).size;
    final dateFormatter = DateFormat('MMM d, yyyy');

    // User profile data
    final displayProfileName = user.fullName;
    final displayUsername = user.username ?? '';
    final displayProfilePhoto = user.profilePictureUrl ?? '';
    final displayIsOnline =
        user.onlineStatus == core_models.OnlineStatus.online;

    // Bubble status
    final displayBubbleStatus =
        widget.bubbleStatusForBadge != null
            ? BubbleMembershipStatus.fromString(widget.bubbleStatusForBadge!)
            : BubbleMembershipStatus.noBubble;

    // Determine if this is a bubbler (user in the same bubble as the current user)
    // We'll get this from the BLoC state instead of the user model
    final relationshipType = _getRelationshipType(state);
    final isBubbler = relationshipType == RelationshipType.bubbler;

    // Set the name color - use a special color for bubblers
    final displayNameColor =
        isBubbler ? const Color(0xFF00FFFF) : widget.profileNameColor;

    // Relationship info
    final displayProfileSinceDate = widget.profileSinceDate;
    // Set the relationship text prefix based on the relationship type
    final displayProfileSinceTextPrefix =
        isBubbler
            ? 'Bubbler since'
            : widget.profileSinceDateTextPrefix ?? 'Member since';

    // Mutual connections
    final mutualFriendsFromBloc = user.mutualFriends;
    final mutualContactsFromBloc = user.mutualContacts;

    // Bubble members coming from BLoC state
    List<BubbleMember> stateBubbleMembers = [];
    if (state is UnifiedProfileLoaded) {
      stateBubbleMembers = state.bubbleMembers;
    } else if (state is UnifiedProfileContactRequestSent) {
      stateBubbleMembers = state.bubbleMembers;
    } else if (state is UnifiedProfileBubbleRequestSent) {
      stateBubbleMembers = state.bubbleMembers;
    }

    // Update local cache for backward-compat in widget tree
    _bubbleMembers = stateBubbleMembers;

    // Button configuration
    String actualBottomButtonText;
    bool actualIsBottomButtonEnabled;
    VoidCallback? actualOnBottomButtonPressed;

    // Check if we're in a request sent state
    if (state is UnifiedProfileContactRequestSent) {
      actualBottomButtonText = 'Contact request sent';
      actualIsBottomButtonEnabled = false;
      actualOnBottomButtonPressed = null;
    } else if (state is UnifiedProfileBubbleRequestSent) {
      // Set button text based on the specific request type
      switch (state.requestType) {
        case BubbleRequestType.startBubbleTogether:
          actualBottomButtonText = 'Bubble start request sent';
          break;
        case BubbleRequestType.inviteToOwnBubble:
          actualBottomButtonText = 'Bubble invite request sent';
          break;
        case BubbleRequestType.requestToJoinBubble:
          actualBottomButtonText = 'Bubble join request sent';
          break;
      }
      actualIsBottomButtonEnabled = false;
      actualOnBottomButtonPressed = null;
    } else if (user.profilePageActionType != null) {
      // Use data from BLoC state
      actualBottomButtonText = user.profilePageButtonText ?? 'Loading...';
      actualIsBottomButtonEnabled = user.isProfilePageButtonEnabled;

      // Set up button action based on action type
      actualOnBottomButtonPressed = () {
        // Check if widget is still mounted before using context
        if (!mounted) return;

        final bloc = context.read<UnifiedProfileBloc>();

        switch (user.profilePageActionType!) {
          case 'sendContactRequest':
            bloc.add(SendContactRequest(user.id));
            CustomToast.showInfo(
              context,
              'Contact request sent to ${user.firstName ?? user.username}',
            );
            break;
          case 'sendMessage':
            CustomToast.showInfo(context, 'Message feature coming soon');
            break;
          case 'startBubbleTogether':
            bloc.add(
              SendBubbleRequestEvent(
                targetUserId: user.id,
                requestType: BubbleRequestType.startBubbleTogether,
              ),
            );
            CustomToast.showInfo(
              context,
              'Bubble start request sent to ${user.firstName ?? user.username}',
            );
            break;
          case 'inviteToOwnBubble':
            bloc.add(
              SendBubbleRequestEvent(
                targetUserId: user.id,
                requestType: BubbleRequestType.inviteToOwnBubble,
              ),
            );
            CustomToast.showInfo(
              context,
              'Bubble invitation sent to ${user.firstName ?? user.username}',
            );
            break;
          case 'requestToJoinBubble':
            bloc.add(
              SendBubbleRequestEvent(
                targetUserId: user.id,
                requestType: BubbleRequestType.requestToJoinBubble,
              ),
            );
            CustomToast.showInfo(
              context,
              'Join request sent to ${user.firstName ?? user.username}',
            );
            break;
          case 'unblockUser':
            bloc.add(UnblockUser(user.id));
            CustomToast.showSuccess(
              context,
              '${user.firstName ?? user.username} has been unblocked',
            );
            break;
          case 'unfriendUser':
            bloc.add(UnfriendUserEvent(targetUserId: user.id));
            CustomToast.showWarning(
              context,
              'Removed ${user.firstName ?? user.username} from contacts',
            );
            break;
          case 'blockUser':
            bloc.add(BlockUserEvent(targetUserId: user.id));
            CustomToast.showWarning(
              context,
              '${user.firstName ?? user.username} has been blocked',
            );
            break;
          case 'muteUser':
            bloc.add(MuteUserEvent(targetUserId: user.id));
            CustomToast.showInfo(
              context,
              '${user.firstName ?? user.username} has been muted',
            );
            break;
          case 'reportUser':
            bloc.add(
              ReportUserEvent(
                targetUserId: user.id,
                reason: 'Inappropriate behavior',
              ),
            );
            CustomToast.showInfo(
              context,
              '${user.firstName ?? user.username} has been reported',
            );
            break;
          default:
            // For other actions or if action is not specified
            if (widget.onBottomButtonPressed != null) {
              widget.onBottomButtonPressed!();
            } else {
              CustomToast.showInfo(context, 'Action not implemented yet');
            }
        }
      };
    } else {
      // Fallback to widget parameters
      actualBottomButtonText = widget.bottomButtonText ?? 'Loading...';
      actualIsBottomButtonEnabled = widget.isBottomButtonEnabled;
      actualOnBottomButtonPressed = widget.onBottomButtonPressed;
    }

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Column(
          children: [
            Expanded(
              child: CustomScrollView(
                slivers: <Widget>[
                  SliverAppBar(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    leading: IconButton(
                      icon: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            // Primary shadow for depth
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.4),
                              blurRadius: 6,
                              spreadRadius: 0,
                              offset: const Offset(0, 3),
                            ),
                            // Ambient shadow for softer edges
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 12,
                              spreadRadius: 0,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                          shadows: [
                            // Icon shadow for better contrast
                            Shadow(
                              color: Colors.black.withValues(alpha: 0.6),
                              blurRadius: 3,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                      onPressed: () {
                        // Check if widget is still mounted before using context
                        if (!mounted) return;

                        // Try standard navigation approaches first
                        if (Navigator.of(context).canPop()) {
                          // If we can pop (meaning we were pushed onto the stack), use pop
                          Navigator.of(context).pop();
                        } else {
                          // If we can't pop, explicitly navigate to the bubble page
                          // This handles the case where a bubbler profile was opened with context.go()
                          context.go(AppRoutes.bubble);
                        }
                      },
                    ),
                    title: Text(
                      displayUsername.isNotEmpty
                          ? '@$displayUsername'
                          : displayProfileName,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: _getAppBarTextSize(context),
                        shadows: [
                          // Primary text shadow for depth and readability
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.7),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                          // Secondary shadow for enhanced contrast
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.4),
                            blurRadius: 8,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                    actions: [
                      PopupMenuButton<String>(
                        icon: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              // Primary shadow for depth
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.4),
                                blurRadius: 6,
                                spreadRadius: 0,
                                offset: const Offset(0, 3),
                              ),
                              // Ambient shadow for softer edges
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.2),
                                blurRadius: 12,
                                spreadRadius: 0,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.more_vert,
                            color: Colors.white,
                            shadows: [
                              // Icon shadow for better contrast
                              Shadow(
                                color: Colors.black.withValues(alpha: 0.6),
                                blurRadius: 3,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                        onSelected: (result) {
                          final bloc = context.read<UnifiedProfileBloc>();

                          switch (result) {
                            case 'mute':
                              bloc.add(MuteUserEvent(targetUserId: user.id));
                              CustomToast.showInfo(
                                context,
                                '${user.firstName ?? user.username} has been muted',
                              );
                              break;
                            case 'block':
                              bloc.add(BlockUserEvent(targetUserId: user.id));
                              CustomToast.showWarning(
                                context,
                                '${user.firstName ?? user.username} has been blocked',
                              );
                              break;
                            case 'report':
                              // Show report dialog or use default reason
                              bloc.add(
                                ReportUserEvent(
                                  targetUserId: user.id,
                                  reason: 'Inappropriate behavior',
                                ),
                              );
                              CustomToast.showInfo(
                                context,
                                '${user.firstName ?? user.username} has been reported',
                              );
                              break;
                            case 'unfriend':
                              bloc.add(
                                UnfriendUserEvent(targetUserId: user.id),
                              );
                              CustomToast.showWarning(
                                context,
                                'Removed ${user.firstName ?? user.username} from contacts',
                              );
                              break;
                          }
                        },
                        itemBuilder: (context) {
                          final items = <PopupMenuEntry<String>>[
                            const PopupMenuItem<String>(
                              value: 'mute',
                              child: Text('Mute Notifications'),
                            ),
                            const PopupMenuItem<String>(
                              value: 'block',
                              child: Text(
                                'Block User',
                                style: TextStyle(color: Colors.red),
                              ),
                            ),
                            const PopupMenuItem<String>(
                              value: 'report',
                              child: Text(
                                'Report User',
                                style: TextStyle(color: Colors.red),
                              ),
                            ),
                          ];
                          // Only show unfriend option for non-bubblers
                          if (widget.onMoreOptionsUnfriend != null &&
                              !isBubbler) {
                            items.add(const PopupMenuDivider());
                            items.add(
                              const PopupMenuItem<String>(
                                value: 'unfriend',
                                child: Text(
                                  'Unfriend',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ),
                            );
                          }
                          return items;
                        },
                        color: Colors.white.withValues(alpha: 0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: Colors.white.withValues(alpha: 0.3),
                          ),
                        ),
                      ),
                    ],
                    expandedHeight: screenSize.height * 0.40,
                    flexibleSpace: FlexibleSpaceBar(
                      background: ShaderMask(
                        shaderCallback:
                            (bounds) => LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.black,
                                Colors.black.withValues(alpha: 1),
                                Colors.transparent,
                              ],
                              stops: const [0.0, 0.84, 1],
                            ).createShader(bounds),
                        blendMode: BlendMode.dstIn,
                        child:
                            displayProfilePhoto.isNotEmpty
                                ? RgbShaderEffect(
                                  child: CachedNetworkImage(
                                    imageUrl: displayProfilePhoto,
                                    fit: BoxFit.cover,
                                    placeholder:
                                        (context, url) => ColoredBox(
                                          color: Colors.white.withValues(
                                            alpha: 0.1,
                                          ),
                                          child: const Center(
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                    errorWidget:
                                        (context, url, error) => ColoredBox(
                                          color: Colors.white.withValues(
                                            alpha: 0.1,
                                          ),
                                          child: Icon(
                                            Icons.broken_image,
                                            size: screenSize.height * 0.2,
                                            color: Colors.white.withValues(
                                              alpha: 0.5,
                                            ),
                                          ),
                                        ),
                                  ),
                                )
                                : ColoredBox(
                                  color: Colors.white.withValues(alpha: 0.1),
                                  child: Icon(
                                    Icons.person,
                                    size: screenSize.height * 0.2,
                                    color: Colors.white.withValues(alpha: 0.5),
                                  ),
                                ),
                      ),
                    ),
                    pinned: true,
                  ),
                  SliverToBoxAdapter(
                    child: Transform.translate(
                      offset: Offset(0, -(screenSize.height * 0.03)),
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Flexible(
                                  child: Text(
                                    displayProfileName,
                                    style: TextStyle(
                                      color: displayNameColor,
                                      fontSize: _getHeadingSize(context),
                                      fontWeight: FontWeight.bold,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (displayIsOnline)
                                  Padding(
                                    padding: const EdgeInsets.only(left: 8),
                                    child: OnlineStatusIndicator(
                                      isOnline: displayIsOnline,
                                      size: 10,
                                    ),
                                  ),
                                Padding(
                                  padding: const EdgeInsets.only(left: 8),
                                  child: BubbleStatusBadge(
                                    status: displayBubbleStatus,
                                  ),
                                ),
                              ],
                            ),
                            if (displayProfileSinceDate != null)
                              Padding(
                                padding: const EdgeInsets.only(),
                                child: Text(
                                  '$displayProfileSinceTextPrefix ${dateFormatter.format(displayProfileSinceDate)}',
                                  style: TextStyle(
                                    fontSize: _getBodyTextSize(context),
                                    color: Colors.white70,
                                  ),
                                ),
                              ),
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${mutualFriendsFromBloc.length} ${mutualFriendsFromBloc.length == 1 ? 'friend' : 'friends'} in common',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: _getSubheadingSize(context),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  if (mutualFriendsFromBloc.isNotEmpty) ...[
                                    const SizedBox(height: 6),
                                    SizedBox(
                                      height: 45,
                                      child: SingleChildScrollView(
                                        scrollDirection: Axis.horizontal,
                                        child: Row(
                                          children:
                                              mutualFriendsFromBloc
                                                  .map(
                                                    (user) => Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                            right: 2,
                                                          ),
                                                      child: SizedBox(
                                                        width: 40,
                                                        child:
                                                            _buildMutualUserAvatar(
                                                              context,
                                                              user,
                                                            ),
                                                      ),
                                                    ),
                                                  )
                                                  .toList(),
                                        ),
                                      ),
                                    ),
                                  ],
                                  const SizedBox(height: 4),
                                  Text(
                                    '${mutualContactsFromBloc.length} ${mutualContactsFromBloc.length == 1 ? 'contact' : 'contacts'} in common',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: _getSubheadingSize(context),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  if (mutualContactsFromBloc.isNotEmpty) ...[
                                    const SizedBox(height: 6),
                                    SizedBox(
                                      height: 45,
                                      child: SingleChildScrollView(
                                        scrollDirection: Axis.horizontal,
                                        child: Row(
                                          children:
                                              mutualContactsFromBloc
                                                  .map(
                                                    (user) => Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                            right: 2,
                                                          ),
                                                      child: SizedBox(
                                                        width: 40,
                                                        child:
                                                            _buildMutualUserAvatar(
                                                              context,
                                                              user,
                                                            ),
                                                      ),
                                                    ),
                                                  )
                                                  .toList(),
                                        ),
                                      ),
                                    ),
                                  ],
                                  if (_bubbleMembers.isNotEmpty &&
                                      (displayBubbleStatus !=
                                              BubbleMembershipStatus.noBubble ||
                                          isBubbler))
                                    Padding(
                                      padding: const EdgeInsets.only(top: 12),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${_bubbleMembers.length} bubblers',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: _getSubheadingSize(
                                                context,
                                              ),
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 6),
                                          SizedBox(
                                            height: 45,
                                            child: SingleChildScrollView(
                                              scrollDirection: Axis.horizontal,
                                              child: Row(
                                                children:
                                                    _bubbleMembers
                                                        .map(
                                                          (member) => Padding(
                                                            padding:
                                                                const EdgeInsets.only(
                                                                  right: 2,
                                                                ),
                                                            child: SizedBox(
                                                              width: 40,
                                                              child:
                                                                  _buildBubbleMemberAvatar(
                                                                    context,
                                                                    member,
                                                                  ),
                                                            ),
                                                          ),
                                                        )
                                                        .toList(),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            if (widget
                                .showCallActionButtons) // This should also come from BLoC state if dynamic
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 20,
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    if (widget.onAudioCallPressed != null)
                                      _buildGlassButton(
                                        context: context,
                                        iconPath: 'assets/icons/phone.svg',
                                        label: 'Audio call',
                                        onPressed: widget.onAudioCallPressed!,
                                      ),
                                    if (widget.onVideoCallPressed != null)
                                      _buildGlassButton(
                                        context: context,
                                        iconPath: 'assets/icons/video.svg',
                                        label: 'Video call',
                                        onPressed: widget.onVideoCallPressed!,
                                      ),
                                    if (widget.onScreenSharePressed != null)
                                      _buildGlassButton(
                                        context: context,
                                        iconPath: 'assets/icons/smartphone.svg',
                                        label: 'Share screen',
                                        onPressed: widget.onScreenSharePressed!,
                                      ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            _buildBottomActionButton(
              context,
              actualBottomButtonText,
              actualIsBottomButtonEnabled,
              actualOnBottomButtonPressed,
              widget.infoDialogTitle,
              widget.infoDialogContent,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMutualUserAvatar(
    BuildContext context,
    String userId,
  ) => GestureDetector(
    onTap: () {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Use GoRouter for consistent navigation
      // Hide the current navigation bar before navigating to the new profile
      _navBarNotifier?.hideNavBar();
      context.push('${AppRoutes.userProfile}/$userId');
    },
    child: Stack(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: Colors.white.withValues(alpha: 0.1),
          // For now, show placeholder since we only have userId
          child: Icon(
            Icons.person,
            size: 20,
            color: Colors.white.withValues(alpha: 0.5),
          ),
        ),
        // Note: Can't show online status since we only have userId
      ],
    ),
  );

  Widget _buildBubbleMemberAvatar(BuildContext context, BubbleMember member) =>
      GestureDetector(
        onTap: () {
          // Check if widget is still mounted before using context
          if (!mounted) return;

          // Navigate to member's profile
          _navBarNotifier?.hideNavBar();
          context.push('${AppRoutes.userProfile}/${member.userId}');
        },
        child: CircleAvatar(
          radius: 20,
          backgroundColor: Colors.white.withValues(alpha: 0.1),
          backgroundImage:
              member.user?.profilePictureUrl != null
                  ? CachedNetworkImageProvider(member.user!.profilePictureUrl!)
                  : null,
          child:
              member.user?.profilePictureUrl == null
                  ? Icon(
                    Icons.person,
                    size: 20,
                    color: Colors.white.withValues(alpha: 0.5),
                  )
                  : null,
        ),
      );

  Widget _buildMutualAvatar(
    BuildContext context,
    MutualConnection connection,
  ) => GestureDetector(
    onTap: () {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Standardize navigation with GoRouter
      // Hide the current navigation bar before navigating to the new profile
      _navBarNotifier?.hideNavBar();
      context.push('${AppRoutes.userProfile}/${connection.id}');
    },
    child: CircleAvatar(
      radius: 20,
      backgroundColor: Colors.white.withValues(alpha: 0.1),
      backgroundImage:
          connection.imageUrl != null
              ? CachedNetworkImageProvider(connection.imageUrl!)
              : null,
      child:
          connection.imageUrl == null
              ? Icon(
                Icons.person,
                size: 20,
                color: Colors.white.withValues(alpha: 0.5),
              )
              : null,
    ),
  );
}

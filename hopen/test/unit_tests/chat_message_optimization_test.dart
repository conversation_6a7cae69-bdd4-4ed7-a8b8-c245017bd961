import 'package:flutter_test/flutter_test.dart';
import 'package:hopen/statefulbusinesslogic/core/models/chat_message.dart';
import 'package:hopen/statefulbusinesslogic/bloc/chat/chat_event.dart';
import 'package:hopen/provider/utils/validation_utils.dart';

/// CRITICAL: Tests for backend optimization features
/// These tests ensure the Flutter client properly handles createdAt timestamps
/// and validates requests for the optimized backend operations

void main() {
  group('ChatMessage Backend Optimization Tests', () {
    late DateTime testTimestamp;
    late ChatMessage testMessage;

    setUp(() {
      testTimestamp = DateTime.now().subtract(const Duration(minutes: 5));
      testMessage = ChatMessage(
        id: '550e8400-e29b-41d4-a716-************',
        content: 'Test message',
        senderId: '550e8400-e29b-41d4-a716-446655440001',
        timestamp: testTimestamp,
        bubbleId: '550e8400-e29b-41d4-a716-446655440002',
        conversationId: '550e8400-e29b-41d4-a716-446655440003',
        isEdited: false,
        isDeleted: false,
      );
    });

    group('ChatMessage Model Tests', () {
      test('should serialize with created_at field for backend compatibility', () {
        final json = testMessage.toJson();
        
        expect(json['created_at'], isNotNull);
        expect(json['created_at'], testTimestamp.toIso8601String());
        expect(json['bubble_id'], testMessage.bubbleId);
        expect(json['conversation_id'], testMessage.conversationId);
        expect(json['is_edited'], false);
        expect(json['is_deleted'], false);
      });

      test('should deserialize from backend JSON format', () {
        final json = {
          'id': testMessage.id,
          'content': testMessage.content,
          'sender_id': testMessage.senderId,
          'created_at': testTimestamp.toIso8601String(),
          'bubble_id': testMessage.bubbleId,
          'conversation_id': testMessage.conversationId,
          'message_type': 'text',
          'is_edited': false,
          'is_deleted': false,
        };

        final message = ChatMessage.fromJson(json);
        
        expect(message.id, testMessage.id);
        expect(message.timestamp, testTimestamp);
        expect(message.bubbleId, testMessage.bubbleId);
        expect(message.conversationId, testMessage.conversationId);
        expect(message.isEdited, false);
        expect(message.isDeleted, false);
      });

      test('should handle legacy timestamp field for backward compatibility', () {
        final json = {
          'id': testMessage.id,
          'content': testMessage.content,
          'sender_id': testMessage.senderId,
          'timestamp': testTimestamp.toIso8601String(), // Legacy field
          'message_type': 'text',
        };

        final message = ChatMessage.fromJson(json);
        expect(message.timestamp, testTimestamp);
      });
    });

    group('Chat Event Tests', () {
      test('DeleteMessageEvent should include createdAt timestamp', () {
        final event = DeleteMessageEvent(
          chatId: 'test-chat',
          messageId: testMessage.id,
          createdAt: testTimestamp,
          bubbleId: testMessage.bubbleId,
        );

        expect(event.messageId, testMessage.id);
        expect(event.createdAt, testTimestamp);
        expect(event.bubbleId, testMessage.bubbleId);
      });

      test('EditMessageEvent should include createdAt timestamp', () {
        final event = EditMessageEvent(
          messageId: testMessage.id,
          newContent: 'Updated content',
          createdAt: testTimestamp,
          bubbleId: testMessage.bubbleId,
        );

        expect(event.messageId, testMessage.id);
        expect(event.newContent, 'Updated content');
        expect(event.createdAt, testTimestamp);
        expect(event.bubbleId, testMessage.bubbleId);
      });

      test('MarkAsReadEvent should include createdAt timestamp', () {
        final event = MarkAsReadEvent(
          messageId: testMessage.id,
          createdAt: testTimestamp,
          conversationId: testMessage.conversationId,
        );

        expect(event.messageId, testMessage.id);
        expect(event.createdAt, testTimestamp);
        expect(event.conversationId, testMessage.conversationId);
      });
    });

    group('Validation Tests', () {
      test('should validate correct UUID format', () {
        expect(ValidationUtils.isValidUuid('550e8400-e29b-41d4-a716-************'), isTrue);
        expect(ValidationUtils.isValidUuid('invalid-uuid'), isFalse);
        expect(ValidationUtils.isValidUuid(''), isFalse);
        expect(ValidationUtils.isValidUuid(null), isFalse);
      });

      test('should validate createdAt timestamp', () {
        final validTimestamp = DateTime.now().subtract(const Duration(minutes: 5));
        final futureTimestamp = DateTime.now().add(const Duration(hours: 1));
        final oldTimestamp = DateTime.now().subtract(const Duration(days: 400));

        expect(ValidationUtils.hasValidCreatedAt(validTimestamp), isTrue);
        expect(ValidationUtils.hasValidCreatedAt(futureTimestamp), isFalse);
        expect(ValidationUtils.hasValidCreatedAt(oldTimestamp), isFalse);
        expect(ValidationUtils.hasValidCreatedAt(null), isFalse);
      });

      test('should validate edit message request', () {
        final validResult = ValidationUtils.validateEditMessageRequest(
          messageId: testMessage.id,
          newContent: 'Valid content',
          createdAt: testTimestamp,
          bubbleId: testMessage.bubbleId,
        );
        expect(validResult.isValid, isTrue);

        final invalidUuidResult = ValidationUtils.validateEditMessageRequest(
          messageId: 'invalid-uuid',
          newContent: 'Valid content',
          createdAt: testTimestamp,
        );
        expect(invalidUuidResult.isError, isTrue);
        expect(invalidUuidResult.errorMessage, contains('Invalid message ID format'));

        final emptyContentResult = ValidationUtils.validateEditMessageRequest(
          messageId: testMessage.id,
          newContent: '',
          createdAt: testTimestamp,
        );
        expect(emptyContentResult.isError, isTrue);
        expect(emptyContentResult.errorMessage, contains('cannot be empty'));
      });

      test('should validate delete message request', () {
        final validResult = ValidationUtils.validateDeleteMessageRequest(
          messageId: testMessage.id,
          createdAt: testTimestamp,
          bubbleId: testMessage.bubbleId,
        );
        expect(validResult.isValid, isTrue);

        final invalidTimestampResult = ValidationUtils.validateDeleteMessageRequest(
          messageId: testMessage.id,
          createdAt: DateTime.now().add(const Duration(hours: 1)),
        );
        expect(invalidTimestampResult.isError, isTrue);
        expect(invalidTimestampResult.errorMessage, contains('timestamp'));
      });

      test('should validate mark as read request', () {
        final validResult = ValidationUtils.validateMarkAsReadRequest(
          messageId: testMessage.id,
          createdAt: testTimestamp,
          conversationId: testMessage.conversationId,
        );
        expect(validResult.isValid, isTrue);

        final invalidConversationIdResult = ValidationUtils.validateMarkAsReadRequest(
          messageId: testMessage.id,
          createdAt: testTimestamp,
          conversationId: 'invalid-uuid',
        );
        expect(invalidConversationIdResult.isError, isTrue);
        expect(invalidConversationIdResult.errorMessage, contains('Invalid conversation ID format'));
      });
    });

    group('Error Message Tests', () {
      test('should provide user-friendly error messages', () {
        final createdAtError = BackendOptimizationErrors.getUserFriendlyMessage('created_at timestamp is required');
        expect(createdAtError, contains('refreshing'));

        final uuidError = BackendOptimizationErrors.getUserFriendlyMessage('Invalid UUID format');
        expect(uuidError, contains('Invalid message format'));

        final timestampError = BackendOptimizationErrors.getUserFriendlyMessage('timestamp issue');
        expect(timestampError, contains('timestamp'));

        final genericError = BackendOptimizationErrors.getUserFriendlyMessage('unknown error');
        expect(genericError, contains('try again'));
      });
    });
  });
}

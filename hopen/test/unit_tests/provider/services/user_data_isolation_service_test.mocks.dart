// Mocks generated by <PERSON>cki<PERSON> 5.4.5 from annotations
// in hopen/test/unit_tests/provider/services/user_data_isolation_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:drift/drift.dart' as _i3;
import 'package:drift/src/runtime/executor/stream_queries.dart' as _i4;
import 'package:hopen/provider/notifiers/user_profile_notifier.dart' as _i6;
import 'package:hopen/provider/repositories/user/cached_user_repository.dart'
    as _i8;
import 'package:hopen/statefulbusinesslogic/core/db/app_database.dart' as _i2;
import 'package:hopen/statefulbusinesslogic/core/error/result.dart' as _i9;
import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart' as _i7;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i10;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeChatMessageDao_0 extends _i1.SmartFake
    implements _i2.ChatMessageDao {
  _FakeChatMessageDao_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserSettingDao_1 extends _i1.SmartFake
    implements _i2.UserSettingDao {
  _FakeUserSettingDao_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeChatMetadataDao_2 extends _i1.SmartFake
    implements _i2.ChatMetadataDao {
  _FakeChatMetadataDao_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUnreadCountDao_3 extends _i1.SmartFake
    implements _i2.UnreadCountDao {
  _FakeUnreadCountDao_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDraftMessageDao_4 extends _i1.SmartFake
    implements _i2.DraftMessageDao {
  _FakeDraftMessageDao_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFcmMessageDao_5 extends _i1.SmartFake implements _i2.FcmMessageDao {
  _FakeFcmMessageDao_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeProfilePictureDao_6 extends _i1.SmartFake
    implements _i2.ProfilePictureDao {
  _FakeProfilePictureDao_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeContactRequestDao_7 extends _i1.SmartFake
    implements _i2.ContactRequestDao {
  _FakeContactRequestDao_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFriendRequestDao_8 extends _i1.SmartFake
    implements _i2.FriendRequestDao {
  _FakeFriendRequestDao_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserProfileDao_9 extends _i1.SmartFake
    implements _i2.UserProfileDao {
  _FakeUserProfileDao_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMigrationStrategy_10 extends _i1.SmartFake
    implements _i3.MigrationStrategy {
  _FakeMigrationStrategy_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$UserSettingsTable_11 extends _i1.SmartFake
    implements _i2.$UserSettingsTable {
  _Fake$UserSettingsTable_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$ChatMetadatasTable_12 extends _i1.SmartFake
    implements _i2.$ChatMetadatasTable {
  _Fake$ChatMetadatasTable_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$UnreadCountsTable_13 extends _i1.SmartFake
    implements _i2.$UnreadCountsTable {
  _Fake$UnreadCountsTable_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$DraftMessagesTable_14 extends _i1.SmartFake
    implements _i2.$DraftMessagesTable {
  _Fake$DraftMessagesTable_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$FcmMessagesTable_15 extends _i1.SmartFake
    implements _i2.$FcmMessagesTable {
  _Fake$FcmMessagesTable_15(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$ProfilePicturesTable_16 extends _i1.SmartFake
    implements _i2.$ProfilePicturesTable {
  _Fake$ProfilePicturesTable_16(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$ContactRequestsTable_17 extends _i1.SmartFake
    implements _i2.$ContactRequestsTable {
  _Fake$ContactRequestsTable_17(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$FriendRequestsTable_18 extends _i1.SmartFake
    implements _i2.$FriendRequestsTable {
  _Fake$FriendRequestsTable_18(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$BubbleInviteRequestsTable_19 extends _i1.SmartFake
    implements _i2.$BubbleInviteRequestsTable {
  _Fake$BubbleInviteRequestsTable_19(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$BubbleJoinRequestsTable_20 extends _i1.SmartFake
    implements _i2.$BubbleJoinRequestsTable {
  _Fake$BubbleJoinRequestsTable_20(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$UserProfilesTable_21 extends _i1.SmartFake
    implements _i2.$UserProfilesTable {
  _Fake$UserProfilesTable_21(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBubbleInviteRequestDao_22 extends _i1.SmartFake
    implements _i2.BubbleInviteRequestDao {
  _FakeBubbleInviteRequestDao_22(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBubbleJoinRequestDao_23 extends _i1.SmartFake
    implements _i2.BubbleJoinRequestDao {
  _FakeBubbleJoinRequestDao_23(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$AppDatabaseManager_24 extends _i1.SmartFake
    implements _i2.$AppDatabaseManager {
  _Fake$AppDatabaseManager_24(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGeneratedDatabase_25 extends _i1.SmartFake
    implements _i3.GeneratedDatabase {
  _FakeGeneratedDatabase_25(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDriftDatabaseOptions_26 extends _i1.SmartFake
    implements _i3.DriftDatabaseOptions {
  _FakeDriftDatabaseOptions_26(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStreamQueryUpdateRules_27 extends _i1.SmartFake
    implements _i3.StreamQueryUpdateRules {
  _FakeStreamQueryUpdateRules_27(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabaseConnection_28 extends _i1.SmartFake
    implements _i3.DatabaseConnection {
  _FakeDatabaseConnection_28(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQueryExecutor_29 extends _i1.SmartFake implements _i3.QueryExecutor {
  _FakeQueryExecutor_29(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStreamQueryStore_30 extends _i1.SmartFake
    implements _i4.StreamQueryStore {
  _FakeStreamQueryStore_30(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabaseConnectionUser_31 extends _i1.SmartFake
    implements _i3.DatabaseConnectionUser {
  _FakeDatabaseConnectionUser_31(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMigrator_32 extends _i1.SmartFake implements _i3.Migrator {
  _FakeMigrator_32(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFuture_33<T1> extends _i1.SmartFake implements _i5.Future<T1> {
  _FakeFuture_33(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeInsertStatement_34<T1 extends _i3.Table, D1> extends _i1.SmartFake
    implements _i3.InsertStatement<T1, D1> {
  _FakeInsertStatement_34(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUpdateStatement_35<T extends _i3.Table, D> extends _i1.SmartFake
    implements _i3.UpdateStatement<T, D> {
  _FakeUpdateStatement_35(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSimpleSelectStatement_36<T1 extends _i3.HasResultSet, D>
    extends _i1.SmartFake implements _i3.SimpleSelectStatement<T1, D> {
  _FakeSimpleSelectStatement_36(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeJoinedSelectStatement_37<FirstT extends _i3.HasResultSet, FirstD>
    extends _i1.SmartFake implements _i3.JoinedSelectStatement<FirstT, FirstD> {
  _FakeJoinedSelectStatement_37(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBaseSelectStatement_38<Row> extends _i1.SmartFake
    implements _i3.BaseSelectStatement<Row> {
  _FakeBaseSelectStatement_38(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDeleteStatement_39<T1 extends _i3.Table, D1> extends _i1.SmartFake
    implements _i3.DeleteStatement<T1, D1> {
  _FakeDeleteStatement_39(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSelectable_40<T> extends _i1.SmartFake implements _i3.Selectable<T> {
  _FakeSelectable_40(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGenerationContext_41 extends _i1.SmartFake
    implements _i3.GenerationContext {
  _FakeGenerationContext_41(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAppDatabase_42 extends _i1.SmartFake implements _i2.AppDatabase {
  _FakeAppDatabase_42(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [UserProfileNotifier].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserProfileNotifier extends _i1.Mock
    implements _i6.UserProfileNotifier {
  MockUserProfileNotifier() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isLoading => (super.noSuchMethod(
        Invocation.getter(#isLoading),
        returnValue: false,
      ) as bool);

  @override
  bool get hasCurrentUser => (super.noSuchMethod(
        Invocation.getter(#hasCurrentUser),
        returnValue: false,
      ) as bool);

  @override
  bool get isCurrentUserOnline => (super.noSuchMethod(
        Invocation.getter(#isCurrentUserOnline),
        returnValue: false,
      ) as bool);

  @override
  _i5.Future<void> initializeUser(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #initializeUser,
          [userId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> cacheUserProfile(_i7.UserModel? user) => (super.noSuchMethod(
        Invocation.method(
          #cacheUserProfile,
          [user],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i7.UserModel?> getUserProfile(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserProfile,
          [userId],
        ),
        returnValue: _i5.Future<_i7.UserModel?>.value(),
      ) as _i5.Future<_i7.UserModel?>);

  @override
  _i5.Stream<_i7.UserModel?> watchUserProfile(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchUserProfile,
          [userId],
        ),
        returnValue: _i5.Stream<_i7.UserModel?>.empty(),
      ) as _i5.Stream<_i7.UserModel?>);

  @override
  _i5.Future<void> updateUser(_i7.UserModel? user) => (super.noSuchMethod(
        Invocation.method(
          #updateUser,
          [user],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> updateCurrentUser(_i7.UserModel? updatedUser) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCurrentUser,
          [updatedUser],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> refreshCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #refreshCurrentUser,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> refreshUser() => (super.noSuchMethod(
        Invocation.method(
          #refreshUser,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void clearUser() => super.noSuchMethod(
        Invocation.method(
          #clearUser,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<Map<String, dynamic>> getCacheStats() => (super.noSuchMethod(
        Invocation.method(
          #getCacheStats,
          [],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<void> clearCache() => (super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [CachedUserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockCachedUserRepository extends _i1.Mock
    implements _i8.CachedUserRepository {
  MockCachedUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Stream<_i7.UserModel?> watchUserProfile(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchUserProfile,
          [userId],
        ),
        returnValue: _i5.Stream<_i7.UserModel?>.empty(),
      ) as _i5.Stream<_i7.UserModel?>);

  @override
  _i5.Future<_i7.UserModel?> getUserProfile(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserProfile,
          [userId],
        ),
        returnValue: _i5.Future<_i7.UserModel?>.value(),
      ) as _i5.Future<_i7.UserModel?>);

  @override
  _i5.Future<void> cacheUserProfile(_i7.UserModel? user) => (super.noSuchMethod(
        Invocation.method(
          #cacheUserProfile,
          [user],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> clearCache() => (super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>> getCacheStats() => (super.noSuchMethod(
        Invocation.method(
          #getCacheStats,
          [],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<void> cleanupOldCache() => (super.noSuchMethod(
        Invocation.method(
          #cleanupOldCache,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i7.UserModel?> getUserById(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #getUserById,
          [userId],
        ),
        returnValue: _i5.Future<_i7.UserModel?>.value(),
      ) as _i5.Future<_i7.UserModel?>);

  @override
  _i5.Future<_i7.UserModel?> getCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i5.Future<_i7.UserModel?>.value(),
      ) as _i5.Future<_i7.UserModel?>);

  @override
  _i5.Future<_i9.Result<_i7.UserModel>> getCurrentUserSafe() =>
      (super.noSuchMethod(
        Invocation.method(
          #getCurrentUserSafe,
          [],
        ),
        returnValue: _i5.Future<_i9.Result<_i7.UserModel>>.value(
            _i10.dummyValue<_i9.Result<_i7.UserModel>>(
          this,
          Invocation.method(
            #getCurrentUserSafe,
            [],
          ),
        )),
      ) as _i5.Future<_i9.Result<_i7.UserModel>>);

  @override
  _i5.Future<_i7.UserModel?> getUser(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #getUser,
          [userId],
        ),
        returnValue: _i5.Future<_i7.UserModel?>.value(),
      ) as _i5.Future<_i7.UserModel?>);

  @override
  _i5.Future<_i7.UserModel?> getEnhancedUser(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getEnhancedUser,
          [userId],
        ),
        returnValue: _i5.Future<_i7.UserModel?>.value(),
      ) as _i5.Future<_i7.UserModel?>);

  @override
  _i5.Future<Map<String, dynamic>> getUserInfo(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserInfo,
          [userId],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<List<_i7.UserModel>> getUsers(List<String>? userIds) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUsers,
          [userIds],
        ),
        returnValue: _i5.Future<List<_i7.UserModel>>.value(<_i7.UserModel>[]),
      ) as _i5.Future<List<_i7.UserModel>>);

  @override
  _i5.Future<List<_i7.UserModel>> getAllUsers() => (super.noSuchMethod(
        Invocation.method(
          #getAllUsers,
          [],
        ),
        returnValue: _i5.Future<List<_i7.UserModel>>.value(<_i7.UserModel>[]),
      ) as _i5.Future<List<_i7.UserModel>>);

  @override
  _i5.Future<List<_i7.UserModel>> getFriends(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getFriends,
          [userId],
        ),
        returnValue: _i5.Future<List<_i7.UserModel>>.value(<_i7.UserModel>[]),
      ) as _i5.Future<List<_i7.UserModel>>);

  @override
  _i5.Future<void> createUser(_i7.UserModel? user) => (super.noSuchMethod(
        Invocation.method(
          #createUser,
          [user],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> updateUser(_i7.UserModel? user) => (super.noSuchMethod(
        Invocation.method(
          #updateUser,
          [user],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i7.UserModel>> findUsers({
    String? firstName,
    String? lastName,
    String? email,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findUsers,
          [],
          {
            #firstName: firstName,
            #lastName: lastName,
            #email: email,
          },
        ),
        returnValue: _i5.Future<List<_i7.UserModel>>.value(<_i7.UserModel>[]),
      ) as _i5.Future<List<_i7.UserModel>>);

  @override
  _i5.Future<List<_i7.UserModel>> getUsersInBubble(String? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUsersInBubble,
          [bubbleId],
        ),
        returnValue: _i5.Future<List<_i7.UserModel>>.value(<_i7.UserModel>[]),
      ) as _i5.Future<List<_i7.UserModel>>);

  @override
  _i5.Future<Map<String, dynamic>> getGroupInfo(String? groupId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getGroupInfo,
          [groupId],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<List<_i7.UserModel>> getMutualFriends(
    String? userId1,
    String? userId2,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMutualFriends,
          [
            userId1,
            userId2,
          ],
        ),
        returnValue: _i5.Future<List<_i7.UserModel>>.value(<_i7.UserModel>[]),
      ) as _i5.Future<List<_i7.UserModel>>);

  @override
  _i5.Future<List<_i7.UserModel>> getMutualContacts(
    String? userId1,
    String? userId2,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMutualContacts,
          [
            userId1,
            userId2,
          ],
        ),
        returnValue: _i5.Future<List<_i7.UserModel>>.value(<_i7.UserModel>[]),
      ) as _i5.Future<List<_i7.UserModel>>);

  @override
  _i5.Future<void> addFriend(
    String? userId,
    String? friendId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #addFriend,
          [
            userId,
            friendId,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool> sendContactRequest({
    required String? fromUserId,
    required String? toUserId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendContactRequest,
          [],
          {
            #fromUserId: fromUserId,
            #toUserId: toUserId,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> acceptContactRequest({
    required String? fromUserId,
    required String? toUserId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptContactRequest,
          [],
          {
            #fromUserId: fromUserId,
            #toUserId: toUserId,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> rejectContactRequest({
    required String? fromUserId,
    required String? toUserId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #rejectContactRequest,
          [],
          {
            #fromUserId: fromUserId,
            #toUserId: toUserId,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> removeContact({
    required String? userId,
    required String? contactId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeContact,
          [],
          {
            #userId: userId,
            #contactId: contactId,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> blockUser({
    required String? userId,
    required String? targetUserId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #blockUser,
          [],
          {
            #userId: userId,
            #targetUserId: targetUserId,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> unblockUser({
    required String? userId,
    required String? targetUserId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #unblockUser,
          [],
          {
            #userId: userId,
            #targetUserId: targetUserId,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> reportUser({
    required String? reportedUserId,
    required String? reason,
    required String? category,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #reportUser,
          [],
          {
            #reportedUserId: reportedUserId,
            #reason: reason,
            #category: category,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);
}

/// A class which mocks [AppDatabase].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppDatabase extends _i1.Mock implements _i2.AppDatabase {
  MockAppDatabase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ChatMessageDao get chatMessageDao => (super.noSuchMethod(
        Invocation.getter(#chatMessageDao),
        returnValue: _FakeChatMessageDao_0(
          this,
          Invocation.getter(#chatMessageDao),
        ),
      ) as _i2.ChatMessageDao);

  @override
  _i2.UserSettingDao get userSettingDao => (super.noSuchMethod(
        Invocation.getter(#userSettingDao),
        returnValue: _FakeUserSettingDao_1(
          this,
          Invocation.getter(#userSettingDao),
        ),
      ) as _i2.UserSettingDao);

  @override
  _i2.ChatMetadataDao get chatMetadataDao => (super.noSuchMethod(
        Invocation.getter(#chatMetadataDao),
        returnValue: _FakeChatMetadataDao_2(
          this,
          Invocation.getter(#chatMetadataDao),
        ),
      ) as _i2.ChatMetadataDao);

  @override
  _i2.UnreadCountDao get unreadCountDao => (super.noSuchMethod(
        Invocation.getter(#unreadCountDao),
        returnValue: _FakeUnreadCountDao_3(
          this,
          Invocation.getter(#unreadCountDao),
        ),
      ) as _i2.UnreadCountDao);

  @override
  _i2.DraftMessageDao get draftMessageDao => (super.noSuchMethod(
        Invocation.getter(#draftMessageDao),
        returnValue: _FakeDraftMessageDao_4(
          this,
          Invocation.getter(#draftMessageDao),
        ),
      ) as _i2.DraftMessageDao);

  @override
  _i2.FcmMessageDao get fcmMessageDao => (super.noSuchMethod(
        Invocation.getter(#fcmMessageDao),
        returnValue: _FakeFcmMessageDao_5(
          this,
          Invocation.getter(#fcmMessageDao),
        ),
      ) as _i2.FcmMessageDao);

  @override
  _i2.ProfilePictureDao get profilePictureDao => (super.noSuchMethod(
        Invocation.getter(#profilePictureDao),
        returnValue: _FakeProfilePictureDao_6(
          this,
          Invocation.getter(#profilePictureDao),
        ),
      ) as _i2.ProfilePictureDao);

  @override
  _i2.ContactRequestDao get contactRequestDao => (super.noSuchMethod(
        Invocation.getter(#contactRequestDao),
        returnValue: _FakeContactRequestDao_7(
          this,
          Invocation.getter(#contactRequestDao),
        ),
      ) as _i2.ContactRequestDao);

  @override
  _i2.FriendRequestDao get friendRequestDao => (super.noSuchMethod(
        Invocation.getter(#friendRequestDao),
        returnValue: _FakeFriendRequestDao_8(
          this,
          Invocation.getter(#friendRequestDao),
        ),
      ) as _i2.FriendRequestDao);

  @override
  _i2.UserProfileDao get userProfileDao => (super.noSuchMethod(
        Invocation.getter(#userProfileDao),
        returnValue: _FakeUserProfileDao_9(
          this,
          Invocation.getter(#userProfileDao),
        ),
      ) as _i2.UserProfileDao);

  @override
  int get schemaVersion => (super.noSuchMethod(
        Invocation.getter(#schemaVersion),
        returnValue: 0,
      ) as int);

  @override
  _i3.MigrationStrategy get migration => (super.noSuchMethod(
        Invocation.getter(#migration),
        returnValue: _FakeMigrationStrategy_10(
          this,
          Invocation.getter(#migration),
        ),
      ) as _i3.MigrationStrategy);

  @override
  _i2.$UserSettingsTable get userSettings => (super.noSuchMethod(
        Invocation.getter(#userSettings),
        returnValue: _Fake$UserSettingsTable_11(
          this,
          Invocation.getter(#userSettings),
        ),
      ) as _i2.$UserSettingsTable);

  @override
  _i2.$ChatMetadatasTable get chatMetadatas => (super.noSuchMethod(
        Invocation.getter(#chatMetadatas),
        returnValue: _Fake$ChatMetadatasTable_12(
          this,
          Invocation.getter(#chatMetadatas),
        ),
      ) as _i2.$ChatMetadatasTable);

  @override
  _i2.$UnreadCountsTable get unreadCounts => (super.noSuchMethod(
        Invocation.getter(#unreadCounts),
        returnValue: _Fake$UnreadCountsTable_13(
          this,
          Invocation.getter(#unreadCounts),
        ),
      ) as _i2.$UnreadCountsTable);

  @override
  _i2.$DraftMessagesTable get draftMessages => (super.noSuchMethod(
        Invocation.getter(#draftMessages),
        returnValue: _Fake$DraftMessagesTable_14(
          this,
          Invocation.getter(#draftMessages),
        ),
      ) as _i2.$DraftMessagesTable);

  @override
  _i2.$FcmMessagesTable get fcmMessages => (super.noSuchMethod(
        Invocation.getter(#fcmMessages),
        returnValue: _Fake$FcmMessagesTable_15(
          this,
          Invocation.getter(#fcmMessages),
        ),
      ) as _i2.$FcmMessagesTable);

  @override
  _i2.$ProfilePicturesTable get profilePictures => (super.noSuchMethod(
        Invocation.getter(#profilePictures),
        returnValue: _Fake$ProfilePicturesTable_16(
          this,
          Invocation.getter(#profilePictures),
        ),
      ) as _i2.$ProfilePicturesTable);

  @override
  _i2.$ContactRequestsTable get contactRequests => (super.noSuchMethod(
        Invocation.getter(#contactRequests),
        returnValue: _Fake$ContactRequestsTable_17(
          this,
          Invocation.getter(#contactRequests),
        ),
      ) as _i2.$ContactRequestsTable);

  @override
  _i2.$FriendRequestsTable get friendRequests => (super.noSuchMethod(
        Invocation.getter(#friendRequests),
        returnValue: _Fake$FriendRequestsTable_18(
          this,
          Invocation.getter(#friendRequests),
        ),
      ) as _i2.$FriendRequestsTable);

  @override
  _i2.$BubbleInviteRequestsTable get bubbleInviteRequests =>
      (super.noSuchMethod(
        Invocation.getter(#bubbleInviteRequests),
        returnValue: _Fake$BubbleInviteRequestsTable_19(
          this,
          Invocation.getter(#bubbleInviteRequests),
        ),
      ) as _i2.$BubbleInviteRequestsTable);

  @override
  _i2.$BubbleJoinRequestsTable get bubbleJoinRequests => (super.noSuchMethod(
        Invocation.getter(#bubbleJoinRequests),
        returnValue: _Fake$BubbleJoinRequestsTable_20(
          this,
          Invocation.getter(#bubbleJoinRequests),
        ),
      ) as _i2.$BubbleJoinRequestsTable);

  @override
  _i2.$UserProfilesTable get userProfiles => (super.noSuchMethod(
        Invocation.getter(#userProfiles),
        returnValue: _Fake$UserProfilesTable_21(
          this,
          Invocation.getter(#userProfiles),
        ),
      ) as _i2.$UserProfilesTable);

  @override
  _i2.BubbleInviteRequestDao get bubbleInviteRequestDao => (super.noSuchMethod(
        Invocation.getter(#bubbleInviteRequestDao),
        returnValue: _FakeBubbleInviteRequestDao_22(
          this,
          Invocation.getter(#bubbleInviteRequestDao),
        ),
      ) as _i2.BubbleInviteRequestDao);

  @override
  _i2.BubbleJoinRequestDao get bubbleJoinRequestDao => (super.noSuchMethod(
        Invocation.getter(#bubbleJoinRequestDao),
        returnValue: _FakeBubbleJoinRequestDao_23(
          this,
          Invocation.getter(#bubbleJoinRequestDao),
        ),
      ) as _i2.BubbleJoinRequestDao);

  @override
  _i2.$AppDatabaseManager get managers => (super.noSuchMethod(
        Invocation.getter(#managers),
        returnValue: _Fake$AppDatabaseManager_24(
          this,
          Invocation.getter(#managers),
        ),
      ) as _i2.$AppDatabaseManager);

  @override
  Iterable<_i3.TableInfo<_i3.Table, Object?>> get allTables =>
      (super.noSuchMethod(
        Invocation.getter(#allTables),
        returnValue: <_i3.TableInfo<_i3.Table, Object?>>[],
      ) as Iterable<_i3.TableInfo<_i3.Table, Object?>>);

  @override
  List<_i3.DatabaseSchemaEntity> get allSchemaEntities => (super.noSuchMethod(
        Invocation.getter(#allSchemaEntities),
        returnValue: <_i3.DatabaseSchemaEntity>[],
      ) as List<_i3.DatabaseSchemaEntity>);

  @override
  _i3.GeneratedDatabase get attachedDatabase => (super.noSuchMethod(
        Invocation.getter(#attachedDatabase),
        returnValue: _FakeGeneratedDatabase_25(
          this,
          Invocation.getter(#attachedDatabase),
        ),
      ) as _i3.GeneratedDatabase);

  @override
  _i3.DriftDatabaseOptions get options => (super.noSuchMethod(
        Invocation.getter(#options),
        returnValue: _FakeDriftDatabaseOptions_26(
          this,
          Invocation.getter(#options),
        ),
      ) as _i3.DriftDatabaseOptions);

  @override
  _i3.StreamQueryUpdateRules get streamUpdateRules => (super.noSuchMethod(
        Invocation.getter(#streamUpdateRules),
        returnValue: _FakeStreamQueryUpdateRules_27(
          this,
          Invocation.getter(#streamUpdateRules),
        ),
      ) as _i3.StreamQueryUpdateRules);

  @override
  _i3.DatabaseConnection get connection => (super.noSuchMethod(
        Invocation.getter(#connection),
        returnValue: _FakeDatabaseConnection_28(
          this,
          Invocation.getter(#connection),
        ),
      ) as _i3.DatabaseConnection);

  @override
  _i3.SqlTypes get typeMapping => (super.noSuchMethod(
        Invocation.getter(#typeMapping),
        returnValue: _i10.dummyValue<_i3.SqlTypes>(
          this,
          Invocation.getter(#typeMapping),
        ),
      ) as _i3.SqlTypes);

  @override
  _i3.QueryExecutor get executor => (super.noSuchMethod(
        Invocation.getter(#executor),
        returnValue: _FakeQueryExecutor_29(
          this,
          Invocation.getter(#executor),
        ),
      ) as _i3.QueryExecutor);

  @override
  _i4.StreamQueryStore get streamQueries => (super.noSuchMethod(
        Invocation.getter(#streamQueries),
        returnValue: _FakeStreamQueryStore_30(
          this,
          Invocation.getter(#streamQueries),
        ),
      ) as _i4.StreamQueryStore);

  @override
  _i3.DatabaseConnectionUser get resolvedEngine => (super.noSuchMethod(
        Invocation.getter(#resolvedEngine),
        returnValue: _FakeDatabaseConnectionUser_31(
          this,
          Invocation.getter(#resolvedEngine),
        ),
      ) as _i3.DatabaseConnectionUser);

  @override
  _i3.Migrator createMigrator() => (super.noSuchMethod(
        Invocation.method(
          #createMigrator,
          [],
        ),
        returnValue: _FakeMigrator_32(
          this,
          Invocation.method(
            #createMigrator,
            [],
          ),
        ),
      ) as _i3.Migrator);

  @override
  _i5.Future<void> beforeOpen(
    _i3.QueryExecutor? executor,
    _i3.OpeningDetails? details,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #beforeOpen,
          [
            executor,
            details,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Stream<T> createStream<T extends Object>(
          _i4.QueryStreamFetcher<T>? stmt) =>
      (super.noSuchMethod(
        Invocation.method(
          #createStream,
          [stmt],
        ),
        returnValue: _i5.Stream<T>.empty(),
      ) as _i5.Stream<T>);

  @override
  T alias<T, D>(
    _i3.ResultSetImplementation<T, D>? table,
    String? alias,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #alias,
          [
            table,
            alias,
          ],
        ),
        returnValue: _i10.dummyValue<T>(
          this,
          Invocation.method(
            #alias,
            [
              table,
              alias,
            ],
          ),
        ),
      ) as T);

  @override
  void markTablesUpdated(Iterable<_i3.TableInfo<_i3.Table, dynamic>>? tables) =>
      super.noSuchMethod(
        Invocation.method(
          #markTablesUpdated,
          [tables],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyUpdates(Set<_i3.TableUpdate>? updates) => super.noSuchMethod(
        Invocation.method(
          #notifyUpdates,
          [updates],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Stream<Set<_i3.TableUpdate>> tableUpdates(
          [_i3.TableUpdateQuery? query = const _i3.TableUpdateQuery.any()]) =>
      (super.noSuchMethod(
        Invocation.method(
          #tableUpdates,
          [query],
        ),
        returnValue: _i5.Stream<Set<_i3.TableUpdate>>.empty(),
      ) as _i5.Stream<Set<_i3.TableUpdate>>);

  @override
  _i5.Future<T> doWhenOpened<T>(
          _i5.FutureOr<T> Function(_i3.QueryExecutor)? fn) =>
      (super.noSuchMethod(
        Invocation.method(
          #doWhenOpened,
          [fn],
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #doWhenOpened,
                  [fn],
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #doWhenOpened,
                [fn],
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i3.InsertStatement<T, D> into<T extends _i3.Table, D>(
          _i3.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #into,
          [table],
        ),
        returnValue: _FakeInsertStatement_34<T, D>(
          this,
          Invocation.method(
            #into,
            [table],
          ),
        ),
      ) as _i3.InsertStatement<T, D>);

  @override
  _i3.UpdateStatement<Tbl, R> update<Tbl extends _i3.Table, R>(
          _i3.TableInfo<Tbl, R>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [table],
        ),
        returnValue: _FakeUpdateStatement_35<Tbl, R>(
          this,
          Invocation.method(
            #update,
            [table],
          ),
        ),
      ) as _i3.UpdateStatement<Tbl, R>);

  @override
  _i3.SimpleSelectStatement<T, R> select<T extends _i3.HasResultSet, R>(
    _i3.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #select,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeSimpleSelectStatement_36<T, R>(
          this,
          Invocation.method(
            #select,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i3.SimpleSelectStatement<T, R>);

  @override
  _i3.JoinedSelectStatement<T, R> selectOnly<T extends _i3.HasResultSet, R>(
    _i3.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectOnly,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeJoinedSelectStatement_37<T, R>(
          this,
          Invocation.method(
            #selectOnly,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i3.JoinedSelectStatement<T, R>);

  @override
  _i3.BaseSelectStatement<_i3.TypedResult> selectExpressions(
          Iterable<_i3.Expression<Object>>? columns) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectExpressions,
          [columns],
        ),
        returnValue: _FakeBaseSelectStatement_38<_i3.TypedResult>(
          this,
          Invocation.method(
            #selectExpressions,
            [columns],
          ),
        ),
      ) as _i3.BaseSelectStatement<_i3.TypedResult>);

  @override
  _i3.DeleteStatement<T, D> delete<T extends _i3.Table, D>(
          _i3.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [table],
        ),
        returnValue: _FakeDeleteStatement_39<T, D>(
          this,
          Invocation.method(
            #delete,
            [table],
          ),
        ),
      ) as _i3.DeleteStatement<T, D>);

  @override
  _i5.Future<int> customUpdate(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i3.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customUpdate,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<int> customInsert(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customInsert,
          [query],
          {
            #variables: variables,
            #updates: updates,
          },
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<List<_i3.QueryRow>> customWriteReturning(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i3.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customWriteReturning,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i5.Future<List<_i3.QueryRow>>.value(<_i3.QueryRow>[]),
      ) as _i5.Future<List<_i3.QueryRow>>);

  @override
  _i3.Selectable<_i3.QueryRow> customSelect(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelect,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_40<_i3.QueryRow>(
          this,
          Invocation.method(
            #customSelect,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i3.Selectable<_i3.QueryRow>);

  @override
  _i3.Selectable<_i3.QueryRow> customSelectQuery(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelectQuery,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_40<_i3.QueryRow>(
          this,
          Invocation.method(
            #customSelectQuery,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i3.Selectable<_i3.QueryRow>);

  @override
  _i5.Future<void> customStatement(
    String? statement, [
    List<dynamic>? args,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #customStatement,
          [
            statement,
            args,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<T> transaction<T>(
    _i5.Future<T> Function()? action, {
    bool? requireNew = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #transaction,
          [action],
          {#requireNew: requireNew},
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #transaction,
                  [action],
                  {#requireNew: requireNew},
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #transaction,
                [action],
                {#requireNew: requireNew},
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i5.Future<T> exclusively<T>(_i5.Future<T> Function()? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #exclusively,
          [action],
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #exclusively,
                  [action],
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #exclusively,
                [action],
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i5.Future<void> batch(_i5.FutureOr<void> Function(_i3.Batch)? runInBatch) =>
      (super.noSuchMethod(
        Invocation.method(
          #batch,
          [runInBatch],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<T> runWithInterceptor<T>(
    _i5.Future<T> Function()? action, {
    required _i3.QueryInterceptor? interceptor,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #runWithInterceptor,
          [action],
          {#interceptor: interceptor},
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #runWithInterceptor,
                  [action],
                  {#interceptor: interceptor},
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #runWithInterceptor,
                [action],
                {#interceptor: interceptor},
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i3.GenerationContext $write(
    _i3.Component? component, {
    bool? hasMultipleTables,
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$write,
          [component],
          {
            #hasMultipleTables: hasMultipleTables,
            #startIndex: startIndex,
          },
        ),
        returnValue: _FakeGenerationContext_41(
          this,
          Invocation.method(
            #$write,
            [component],
            {
              #hasMultipleTables: hasMultipleTables,
              #startIndex: startIndex,
            },
          ),
        ),
      ) as _i3.GenerationContext);

  @override
  _i3.GenerationContext $writeInsertable(
    _i3.TableInfo<_i3.Table, dynamic>? table,
    _i3.Insertable<dynamic>? insertable, {
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$writeInsertable,
          [
            table,
            insertable,
          ],
          {#startIndex: startIndex},
        ),
        returnValue: _FakeGenerationContext_41(
          this,
          Invocation.method(
            #$writeInsertable,
            [
              table,
              insertable,
            ],
            {#startIndex: startIndex},
          ),
        ),
      ) as _i3.GenerationContext);

  @override
  String $expandVar(
    int? start,
    int? amount,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #$expandVar,
          [
            start,
            amount,
          ],
        ),
        returnValue: _i10.dummyValue<String>(
          this,
          Invocation.method(
            #$expandVar,
            [
              start,
              amount,
            ],
          ),
        ),
      ) as String);
}

/// A class which mocks [UserProfileDao].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserProfileDao extends _i1.Mock implements _i2.UserProfileDao {
  MockUserProfileDao() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AppDatabase get attachedDatabase => (super.noSuchMethod(
        Invocation.getter(#attachedDatabase),
        returnValue: _FakeAppDatabase_42(
          this,
          Invocation.getter(#attachedDatabase),
        ),
      ) as _i2.AppDatabase);

  @override
  _i3.DatabaseConnection get connection => (super.noSuchMethod(
        Invocation.getter(#connection),
        returnValue: _FakeDatabaseConnection_28(
          this,
          Invocation.getter(#connection),
        ),
      ) as _i3.DatabaseConnection);

  @override
  _i3.SqlTypes get typeMapping => (super.noSuchMethod(
        Invocation.getter(#typeMapping),
        returnValue: _i10.dummyValue<_i3.SqlTypes>(
          this,
          Invocation.getter(#typeMapping),
        ),
      ) as _i3.SqlTypes);

  @override
  _i3.DriftDatabaseOptions get options => (super.noSuchMethod(
        Invocation.getter(#options),
        returnValue: _FakeDriftDatabaseOptions_26(
          this,
          Invocation.getter(#options),
        ),
      ) as _i3.DriftDatabaseOptions);

  @override
  _i3.QueryExecutor get executor => (super.noSuchMethod(
        Invocation.getter(#executor),
        returnValue: _FakeQueryExecutor_29(
          this,
          Invocation.getter(#executor),
        ),
      ) as _i3.QueryExecutor);

  @override
  _i4.StreamQueryStore get streamQueries => (super.noSuchMethod(
        Invocation.getter(#streamQueries),
        returnValue: _FakeStreamQueryStore_30(
          this,
          Invocation.getter(#streamQueries),
        ),
      ) as _i4.StreamQueryStore);

  @override
  _i3.DatabaseConnectionUser get resolvedEngine => (super.noSuchMethod(
        Invocation.getter(#resolvedEngine),
        returnValue: _FakeDatabaseConnectionUser_31(
          this,
          Invocation.getter(#resolvedEngine),
        ),
      ) as _i3.DatabaseConnectionUser);

  @override
  _i2.$UserProfilesTable get userProfiles => (super.noSuchMethod(
        Invocation.getter(#userProfiles),
        returnValue: _Fake$UserProfilesTable_21(
          this,
          Invocation.getter(#userProfiles),
        ),
      ) as _i2.$UserProfilesTable);

  @override
  _i5.Future<_i2.DriftUserProfile?> getUserProfile(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserProfile,
          [userId],
        ),
        returnValue: _i5.Future<_i2.DriftUserProfile?>.value(),
      ) as _i5.Future<_i2.DriftUserProfile?>);

  @override
  _i5.Stream<_i2.DriftUserProfile?> watchUserProfile(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchUserProfile,
          [userId],
        ),
        returnValue: _i5.Stream<_i2.DriftUserProfile?>.empty(),
      ) as _i5.Stream<_i2.DriftUserProfile?>);

  @override
  _i5.Future<void> insertOrUpdateUserProfile(_i2.DriftUserProfile? profile) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertOrUpdateUserProfile,
          [profile],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> updateLastAccessed(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #updateLastAccessed,
          [userId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> markAsStale(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #markAsStale,
          [userId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> markAsFresh(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #markAsFresh,
          [userId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i2.DriftUserProfile>> getStaleProfiles() =>
      (super.noSuchMethod(
        Invocation.method(
          #getStaleProfiles,
          [],
        ),
        returnValue: _i5.Future<List<_i2.DriftUserProfile>>.value(
            <_i2.DriftUserProfile>[]),
      ) as _i5.Future<List<_i2.DriftUserProfile>>);

  @override
  _i5.Future<List<_i2.DriftUserProfile>> getProfilesNeedingRefresh(
          Duration? stalePeriod) =>
      (super.noSuchMethod(
        Invocation.method(
          #getProfilesNeedingRefresh,
          [stalePeriod],
        ),
        returnValue: _i5.Future<List<_i2.DriftUserProfile>>.value(
            <_i2.DriftUserProfile>[]),
      ) as _i5.Future<List<_i2.DriftUserProfile>>);

  @override
  _i5.Future<int> deleteUserProfile(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #deleteUserProfile,
          [userId],
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<void> clearAllProfiles() => (super.noSuchMethod(
        Invocation.method(
          #clearAllProfiles,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i2.DriftUserProfile>> getAllProfiles() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllProfiles,
          [],
        ),
        returnValue: _i5.Future<List<_i2.DriftUserProfile>>.value(
            <_i2.DriftUserProfile>[]),
      ) as _i5.Future<List<_i2.DriftUserProfile>>);

  @override
  _i5.Future<Map<String, dynamic>> getCacheStats() => (super.noSuchMethod(
        Invocation.method(
          #getCacheStats,
          [],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Stream<T> createStream<T extends Object>(
          _i4.QueryStreamFetcher<T>? stmt) =>
      (super.noSuchMethod(
        Invocation.method(
          #createStream,
          [stmt],
        ),
        returnValue: _i5.Stream<T>.empty(),
      ) as _i5.Stream<T>);

  @override
  T alias<T, D>(
    _i3.ResultSetImplementation<T, D>? table,
    String? alias,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #alias,
          [
            table,
            alias,
          ],
        ),
        returnValue: _i10.dummyValue<T>(
          this,
          Invocation.method(
            #alias,
            [
              table,
              alias,
            ],
          ),
        ),
      ) as T);

  @override
  void markTablesUpdated(Iterable<_i3.TableInfo<_i3.Table, dynamic>>? tables) =>
      super.noSuchMethod(
        Invocation.method(
          #markTablesUpdated,
          [tables],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyUpdates(Set<_i3.TableUpdate>? updates) => super.noSuchMethod(
        Invocation.method(
          #notifyUpdates,
          [updates],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Stream<Set<_i3.TableUpdate>> tableUpdates(
          [_i3.TableUpdateQuery? query = const _i3.TableUpdateQuery.any()]) =>
      (super.noSuchMethod(
        Invocation.method(
          #tableUpdates,
          [query],
        ),
        returnValue: _i5.Stream<Set<_i3.TableUpdate>>.empty(),
      ) as _i5.Stream<Set<_i3.TableUpdate>>);

  @override
  _i5.Future<T> doWhenOpened<T>(
          _i5.FutureOr<T> Function(_i3.QueryExecutor)? fn) =>
      (super.noSuchMethod(
        Invocation.method(
          #doWhenOpened,
          [fn],
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #doWhenOpened,
                  [fn],
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #doWhenOpened,
                [fn],
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i3.InsertStatement<T, D> into<T extends _i3.Table, D>(
          _i3.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #into,
          [table],
        ),
        returnValue: _FakeInsertStatement_34<T, D>(
          this,
          Invocation.method(
            #into,
            [table],
          ),
        ),
      ) as _i3.InsertStatement<T, D>);

  @override
  _i3.UpdateStatement<Tbl, R> update<Tbl extends _i3.Table, R>(
          _i3.TableInfo<Tbl, R>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [table],
        ),
        returnValue: _FakeUpdateStatement_35<Tbl, R>(
          this,
          Invocation.method(
            #update,
            [table],
          ),
        ),
      ) as _i3.UpdateStatement<Tbl, R>);

  @override
  _i3.SimpleSelectStatement<T, R> select<T extends _i3.HasResultSet, R>(
    _i3.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #select,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeSimpleSelectStatement_36<T, R>(
          this,
          Invocation.method(
            #select,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i3.SimpleSelectStatement<T, R>);

  @override
  _i3.JoinedSelectStatement<T, R> selectOnly<T extends _i3.HasResultSet, R>(
    _i3.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectOnly,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeJoinedSelectStatement_37<T, R>(
          this,
          Invocation.method(
            #selectOnly,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i3.JoinedSelectStatement<T, R>);

  @override
  _i3.BaseSelectStatement<_i3.TypedResult> selectExpressions(
          Iterable<_i3.Expression<Object>>? columns) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectExpressions,
          [columns],
        ),
        returnValue: _FakeBaseSelectStatement_38<_i3.TypedResult>(
          this,
          Invocation.method(
            #selectExpressions,
            [columns],
          ),
        ),
      ) as _i3.BaseSelectStatement<_i3.TypedResult>);

  @override
  _i3.DeleteStatement<T, D> delete<T extends _i3.Table, D>(
          _i3.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [table],
        ),
        returnValue: _FakeDeleteStatement_39<T, D>(
          this,
          Invocation.method(
            #delete,
            [table],
          ),
        ),
      ) as _i3.DeleteStatement<T, D>);

  @override
  _i5.Future<int> customUpdate(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i3.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customUpdate,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<int> customInsert(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customInsert,
          [query],
          {
            #variables: variables,
            #updates: updates,
          },
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<List<_i3.QueryRow>> customWriteReturning(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i3.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customWriteReturning,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i5.Future<List<_i3.QueryRow>>.value(<_i3.QueryRow>[]),
      ) as _i5.Future<List<_i3.QueryRow>>);

  @override
  _i3.Selectable<_i3.QueryRow> customSelect(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelect,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_40<_i3.QueryRow>(
          this,
          Invocation.method(
            #customSelect,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i3.Selectable<_i3.QueryRow>);

  @override
  _i3.Selectable<_i3.QueryRow> customSelectQuery(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelectQuery,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_40<_i3.QueryRow>(
          this,
          Invocation.method(
            #customSelectQuery,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i3.Selectable<_i3.QueryRow>);

  @override
  _i5.Future<void> customStatement(
    String? statement, [
    List<dynamic>? args,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #customStatement,
          [
            statement,
            args,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<T> transaction<T>(
    _i5.Future<T> Function()? action, {
    bool? requireNew = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #transaction,
          [action],
          {#requireNew: requireNew},
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #transaction,
                  [action],
                  {#requireNew: requireNew},
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #transaction,
                [action],
                {#requireNew: requireNew},
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i5.Future<T> exclusively<T>(_i5.Future<T> Function()? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #exclusively,
          [action],
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #exclusively,
                  [action],
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #exclusively,
                [action],
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i5.Future<void> batch(_i5.FutureOr<void> Function(_i3.Batch)? runInBatch) =>
      (super.noSuchMethod(
        Invocation.method(
          #batch,
          [runInBatch],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<T> runWithInterceptor<T>(
    _i5.Future<T> Function()? action, {
    required _i3.QueryInterceptor? interceptor,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #runWithInterceptor,
          [action],
          {#interceptor: interceptor},
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #runWithInterceptor,
                  [action],
                  {#interceptor: interceptor},
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #runWithInterceptor,
                [action],
                {#interceptor: interceptor},
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i3.GenerationContext $write(
    _i3.Component? component, {
    bool? hasMultipleTables,
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$write,
          [component],
          {
            #hasMultipleTables: hasMultipleTables,
            #startIndex: startIndex,
          },
        ),
        returnValue: _FakeGenerationContext_41(
          this,
          Invocation.method(
            #$write,
            [component],
            {
              #hasMultipleTables: hasMultipleTables,
              #startIndex: startIndex,
            },
          ),
        ),
      ) as _i3.GenerationContext);

  @override
  _i3.GenerationContext $writeInsertable(
    _i3.TableInfo<_i3.Table, dynamic>? table,
    _i3.Insertable<dynamic>? insertable, {
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$writeInsertable,
          [
            table,
            insertable,
          ],
          {#startIndex: startIndex},
        ),
        returnValue: _FakeGenerationContext_41(
          this,
          Invocation.method(
            #$writeInsertable,
            [
              table,
              insertable,
            ],
            {#startIndex: startIndex},
          ),
        ),
      ) as _i3.GenerationContext);

  @override
  String $expandVar(
    int? start,
    int? amount,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #$expandVar,
          [
            start,
            amount,
          ],
        ),
        returnValue: _i10.dummyValue<String>(
          this,
          Invocation.method(
            #$expandVar,
            [
              start,
              amount,
            ],
          ),
        ),
      ) as String);

  @override
  _i5.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
}

/// A class which mocks [ProfilePictureDao].
///
/// See the documentation for Mockito's code generation for more information.
class MockProfilePictureDao extends _i1.Mock implements _i2.ProfilePictureDao {
  MockProfilePictureDao() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AppDatabase get attachedDatabase => (super.noSuchMethod(
        Invocation.getter(#attachedDatabase),
        returnValue: _FakeAppDatabase_42(
          this,
          Invocation.getter(#attachedDatabase),
        ),
      ) as _i2.AppDatabase);

  @override
  _i3.DatabaseConnection get connection => (super.noSuchMethod(
        Invocation.getter(#connection),
        returnValue: _FakeDatabaseConnection_28(
          this,
          Invocation.getter(#connection),
        ),
      ) as _i3.DatabaseConnection);

  @override
  _i3.SqlTypes get typeMapping => (super.noSuchMethod(
        Invocation.getter(#typeMapping),
        returnValue: _i10.dummyValue<_i3.SqlTypes>(
          this,
          Invocation.getter(#typeMapping),
        ),
      ) as _i3.SqlTypes);

  @override
  _i3.DriftDatabaseOptions get options => (super.noSuchMethod(
        Invocation.getter(#options),
        returnValue: _FakeDriftDatabaseOptions_26(
          this,
          Invocation.getter(#options),
        ),
      ) as _i3.DriftDatabaseOptions);

  @override
  _i3.QueryExecutor get executor => (super.noSuchMethod(
        Invocation.getter(#executor),
        returnValue: _FakeQueryExecutor_29(
          this,
          Invocation.getter(#executor),
        ),
      ) as _i3.QueryExecutor);

  @override
  _i4.StreamQueryStore get streamQueries => (super.noSuchMethod(
        Invocation.getter(#streamQueries),
        returnValue: _FakeStreamQueryStore_30(
          this,
          Invocation.getter(#streamQueries),
        ),
      ) as _i4.StreamQueryStore);

  @override
  _i3.DatabaseConnectionUser get resolvedEngine => (super.noSuchMethod(
        Invocation.getter(#resolvedEngine),
        returnValue: _FakeDatabaseConnectionUser_31(
          this,
          Invocation.getter(#resolvedEngine),
        ),
      ) as _i3.DatabaseConnectionUser);

  @override
  _i2.$ProfilePicturesTable get profilePictures => (super.noSuchMethod(
        Invocation.getter(#profilePictures),
        returnValue: _Fake$ProfilePicturesTable_16(
          this,
          Invocation.getter(#profilePictures),
        ),
      ) as _i2.$ProfilePicturesTable);

  @override
  _i5.Future<_i2.DriftProfilePicture?> getProfilePicture(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getProfilePicture,
          [userId],
        ),
        returnValue: _i5.Future<_i2.DriftProfilePicture?>.value(),
      ) as _i5.Future<_i2.DriftProfilePicture?>);

  @override
  _i5.Future<void> saveProfilePicture(_i2.DriftProfilePicture? picture) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveProfilePicture,
          [picture],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> deleteProfilePicture(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #deleteProfilePicture,
          [userId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> updateLastAccessed(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #updateLastAccessed,
          [userId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i2.DriftProfilePicture>> getOldCacheEntries(Duration? age) =>
      (super.noSuchMethod(
        Invocation.method(
          #getOldCacheEntries,
          [age],
        ),
        returnValue: _i5.Future<List<_i2.DriftProfilePicture>>.value(
            <_i2.DriftProfilePicture>[]),
      ) as _i5.Future<List<_i2.DriftProfilePicture>>);

  @override
  _i5.Future<List<_i2.DriftProfilePicture>> getAllProfilePictures() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllProfilePictures,
          [],
        ),
        returnValue: _i5.Future<List<_i2.DriftProfilePicture>>.value(
            <_i2.DriftProfilePicture>[]),
      ) as _i5.Future<List<_i2.DriftProfilePicture>>);

  @override
  _i5.Future<int> getTotalCacheSize() => (super.noSuchMethod(
        Invocation.method(
          #getTotalCacheSize,
          [],
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<void> clearAllCache() => (super.noSuchMethod(
        Invocation.method(
          #clearAllCache,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Stream<T> createStream<T extends Object>(
          _i4.QueryStreamFetcher<T>? stmt) =>
      (super.noSuchMethod(
        Invocation.method(
          #createStream,
          [stmt],
        ),
        returnValue: _i5.Stream<T>.empty(),
      ) as _i5.Stream<T>);

  @override
  T alias<T, D>(
    _i3.ResultSetImplementation<T, D>? table,
    String? alias,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #alias,
          [
            table,
            alias,
          ],
        ),
        returnValue: _i10.dummyValue<T>(
          this,
          Invocation.method(
            #alias,
            [
              table,
              alias,
            ],
          ),
        ),
      ) as T);

  @override
  void markTablesUpdated(Iterable<_i3.TableInfo<_i3.Table, dynamic>>? tables) =>
      super.noSuchMethod(
        Invocation.method(
          #markTablesUpdated,
          [tables],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyUpdates(Set<_i3.TableUpdate>? updates) => super.noSuchMethod(
        Invocation.method(
          #notifyUpdates,
          [updates],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Stream<Set<_i3.TableUpdate>> tableUpdates(
          [_i3.TableUpdateQuery? query = const _i3.TableUpdateQuery.any()]) =>
      (super.noSuchMethod(
        Invocation.method(
          #tableUpdates,
          [query],
        ),
        returnValue: _i5.Stream<Set<_i3.TableUpdate>>.empty(),
      ) as _i5.Stream<Set<_i3.TableUpdate>>);

  @override
  _i5.Future<T> doWhenOpened<T>(
          _i5.FutureOr<T> Function(_i3.QueryExecutor)? fn) =>
      (super.noSuchMethod(
        Invocation.method(
          #doWhenOpened,
          [fn],
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #doWhenOpened,
                  [fn],
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #doWhenOpened,
                [fn],
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i3.InsertStatement<T, D> into<T extends _i3.Table, D>(
          _i3.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #into,
          [table],
        ),
        returnValue: _FakeInsertStatement_34<T, D>(
          this,
          Invocation.method(
            #into,
            [table],
          ),
        ),
      ) as _i3.InsertStatement<T, D>);

  @override
  _i3.UpdateStatement<Tbl, R> update<Tbl extends _i3.Table, R>(
          _i3.TableInfo<Tbl, R>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [table],
        ),
        returnValue: _FakeUpdateStatement_35<Tbl, R>(
          this,
          Invocation.method(
            #update,
            [table],
          ),
        ),
      ) as _i3.UpdateStatement<Tbl, R>);

  @override
  _i3.SimpleSelectStatement<T, R> select<T extends _i3.HasResultSet, R>(
    _i3.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #select,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeSimpleSelectStatement_36<T, R>(
          this,
          Invocation.method(
            #select,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i3.SimpleSelectStatement<T, R>);

  @override
  _i3.JoinedSelectStatement<T, R> selectOnly<T extends _i3.HasResultSet, R>(
    _i3.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectOnly,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeJoinedSelectStatement_37<T, R>(
          this,
          Invocation.method(
            #selectOnly,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i3.JoinedSelectStatement<T, R>);

  @override
  _i3.BaseSelectStatement<_i3.TypedResult> selectExpressions(
          Iterable<_i3.Expression<Object>>? columns) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectExpressions,
          [columns],
        ),
        returnValue: _FakeBaseSelectStatement_38<_i3.TypedResult>(
          this,
          Invocation.method(
            #selectExpressions,
            [columns],
          ),
        ),
      ) as _i3.BaseSelectStatement<_i3.TypedResult>);

  @override
  _i3.DeleteStatement<T, D> delete<T extends _i3.Table, D>(
          _i3.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [table],
        ),
        returnValue: _FakeDeleteStatement_39<T, D>(
          this,
          Invocation.method(
            #delete,
            [table],
          ),
        ),
      ) as _i3.DeleteStatement<T, D>);

  @override
  _i5.Future<int> customUpdate(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i3.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customUpdate,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<int> customInsert(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customInsert,
          [query],
          {
            #variables: variables,
            #updates: updates,
          },
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<List<_i3.QueryRow>> customWriteReturning(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i3.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customWriteReturning,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i5.Future<List<_i3.QueryRow>>.value(<_i3.QueryRow>[]),
      ) as _i5.Future<List<_i3.QueryRow>>);

  @override
  _i3.Selectable<_i3.QueryRow> customSelect(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelect,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_40<_i3.QueryRow>(
          this,
          Invocation.method(
            #customSelect,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i3.Selectable<_i3.QueryRow>);

  @override
  _i3.Selectable<_i3.QueryRow> customSelectQuery(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelectQuery,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_40<_i3.QueryRow>(
          this,
          Invocation.method(
            #customSelectQuery,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i3.Selectable<_i3.QueryRow>);

  @override
  _i5.Future<void> customStatement(
    String? statement, [
    List<dynamic>? args,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #customStatement,
          [
            statement,
            args,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<T> transaction<T>(
    _i5.Future<T> Function()? action, {
    bool? requireNew = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #transaction,
          [action],
          {#requireNew: requireNew},
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #transaction,
                  [action],
                  {#requireNew: requireNew},
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #transaction,
                [action],
                {#requireNew: requireNew},
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i5.Future<T> exclusively<T>(_i5.Future<T> Function()? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #exclusively,
          [action],
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #exclusively,
                  [action],
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #exclusively,
                [action],
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i5.Future<void> batch(_i5.FutureOr<void> Function(_i3.Batch)? runInBatch) =>
      (super.noSuchMethod(
        Invocation.method(
          #batch,
          [runInBatch],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<T> runWithInterceptor<T>(
    _i5.Future<T> Function()? action, {
    required _i3.QueryInterceptor? interceptor,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #runWithInterceptor,
          [action],
          {#interceptor: interceptor},
        ),
        returnValue: _i10.ifNotNull(
              _i10.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #runWithInterceptor,
                  [action],
                  {#interceptor: interceptor},
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_33<T>(
              this,
              Invocation.method(
                #runWithInterceptor,
                [action],
                {#interceptor: interceptor},
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i3.GenerationContext $write(
    _i3.Component? component, {
    bool? hasMultipleTables,
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$write,
          [component],
          {
            #hasMultipleTables: hasMultipleTables,
            #startIndex: startIndex,
          },
        ),
        returnValue: _FakeGenerationContext_41(
          this,
          Invocation.method(
            #$write,
            [component],
            {
              #hasMultipleTables: hasMultipleTables,
              #startIndex: startIndex,
            },
          ),
        ),
      ) as _i3.GenerationContext);

  @override
  _i3.GenerationContext $writeInsertable(
    _i3.TableInfo<_i3.Table, dynamic>? table,
    _i3.Insertable<dynamic>? insertable, {
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$writeInsertable,
          [
            table,
            insertable,
          ],
          {#startIndex: startIndex},
        ),
        returnValue: _FakeGenerationContext_41(
          this,
          Invocation.method(
            #$writeInsertable,
            [
              table,
              insertable,
            ],
            {#startIndex: startIndex},
          ),
        ),
      ) as _i3.GenerationContext);

  @override
  String $expandVar(
    int? start,
    int? amount,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #$expandVar,
          [
            start,
            amount,
          ],
        ),
        returnValue: _i10.dummyValue<String>(
          this,
          Invocation.method(
            #$expandVar,
            [
              start,
              amount,
            ],
          ),
        ),
      ) as String);

  @override
  _i5.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
}

// Mocks generated by <PERSON><PERSON>to 5.4.5 from annotations
// in hopen/test/unit_tests/statefulbusinesslogic/bloc/bubble/bubble_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:hopen/repositories/auth/auth_repository.dart' as _i8;
import 'package:hopen/repositories/bubble/bubble_repository.dart' as _i2;
import 'package:hopen/statefulbusinesslogic/core/error/result.dart' as _i4;
import 'package:hopen/statefulbusinesslogic/core/models/bubble_entity.dart'
    as _i5;
import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart' as _i7;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [BubbleRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockBubbleRepository extends _i1.Mock implements _i2.BubbleRepository {
  MockBubbleRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i4.Result<_i5.BubbleEntity>> createBubble({
    required dynamic name,
    required String? description,
    String? locationName,
    double? locationLat,
    double? locationLng,
    int? locationRadius,
    String? customImageUrl,
    String? colorTheme,
    bool? allowInvites,
    bool? requireApproval,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createBubble,
          [],
          {
            #name: name,
            #description: description,
            #locationName: locationName,
            #locationLat: locationLat,
            #locationLng: locationLng,
            #locationRadius: locationRadius,
            #customImageUrl: customImageUrl,
            #colorTheme: colorTheme,
            #allowInvites: allowInvites,
            #requireApproval: requireApproval,
          },
        ),
        returnValue: _i3.Future<_i4.Result<_i5.BubbleEntity>>.value(
            _i6.dummyValue<_i4.Result<_i5.BubbleEntity>>(
          this,
          Invocation.method(
            #createBubble,
            [],
            {
              #name: name,
              #description: description,
              #locationName: locationName,
              #locationLat: locationLat,
              #locationLng: locationLng,
              #locationRadius: locationRadius,
              #customImageUrl: customImageUrl,
              #colorTheme: colorTheme,
              #allowInvites: allowInvites,
              #requireApproval: requireApproval,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<_i5.BubbleEntity>>);

  @override
  _i3.Future<_i4.Result<_i5.BubbleEntity>> getBubble(dynamic bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubble,
          [bubbleId],
        ),
        returnValue: _i3.Future<_i4.Result<_i5.BubbleEntity>>.value(
            _i6.dummyValue<_i4.Result<_i5.BubbleEntity>>(
          this,
          Invocation.method(
            #getBubble,
            [bubbleId],
          ),
        )),
      ) as _i3.Future<_i4.Result<_i5.BubbleEntity>>);

  @override
  _i3.Future<_i4.Result<_i5.BubbleEntity>> getBubbleDetailsById(
          dynamic bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbleDetailsById,
          [bubbleId],
        ),
        returnValue: _i3.Future<_i4.Result<_i5.BubbleEntity>>.value(
            _i6.dummyValue<_i4.Result<_i5.BubbleEntity>>(
          this,
          Invocation.method(
            #getBubbleDetailsById,
            [bubbleId],
          ),
        )),
      ) as _i3.Future<_i4.Result<_i5.BubbleEntity>>);

  @override
  _i3.Future<_i4.Result<void>> updateBubble(
    dynamic bubbleId,
    Map<String, dynamic>? updates,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBubble,
          [
            bubbleId,
            updates,
          ],
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #updateBubble,
            [
              bubbleId,
              updates,
            ],
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<void>> deleteBubble(dynamic bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteBubble,
          [bubbleId],
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #deleteBubble,
            [bubbleId],
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<_i5.BubbleEntity>> joinBubble({
    required dynamic bubbleId,
    dynamic userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #joinBubble,
          [],
          {
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue: _i3.Future<_i4.Result<_i5.BubbleEntity>>.value(
            _i6.dummyValue<_i4.Result<_i5.BubbleEntity>>(
          this,
          Invocation.method(
            #joinBubble,
            [],
            {
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<_i5.BubbleEntity>>);

  @override
  _i3.Future<_i4.Result<void>> leaveBubble({
    required dynamic bubbleId,
    dynamic userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #leaveBubble,
          [],
          {
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #leaveBubble,
            [],
            {
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<List<_i7.UserModel>>> getBubbleMembers(
          dynamic bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbleMembers,
          [bubbleId],
        ),
        returnValue: _i3.Future<_i4.Result<List<_i7.UserModel>>>.value(
            _i6.dummyValue<_i4.Result<List<_i7.UserModel>>>(
          this,
          Invocation.method(
            #getBubbleMembers,
            [bubbleId],
          ),
        )),
      ) as _i3.Future<_i4.Result<List<_i7.UserModel>>>);

  @override
  _i3.Future<_i4.Result<List<_i5.BubbleEntity>>> getUserBubbles(
          dynamic userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserBubbles,
          [userId],
        ),
        returnValue: _i3.Future<_i4.Result<List<_i5.BubbleEntity>>>.value(
            _i6.dummyValue<_i4.Result<List<_i5.BubbleEntity>>>(
          this,
          Invocation.method(
            #getUserBubbles,
            [userId],
          ),
        )),
      ) as _i3.Future<_i4.Result<List<_i5.BubbleEntity>>>);

  @override
  _i3.Future<_i4.Result<List<_i5.BubbleEntity>>> getBubbles(dynamic userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbles,
          [userId],
        ),
        returnValue: _i3.Future<_i4.Result<List<_i5.BubbleEntity>>>.value(
            _i6.dummyValue<_i4.Result<List<_i5.BubbleEntity>>>(
          this,
          Invocation.method(
            #getBubbles,
            [userId],
          ),
        )),
      ) as _i3.Future<_i4.Result<List<_i5.BubbleEntity>>>);

  @override
  _i3.Future<_i4.Result<List<_i5.BubbleEntity>>> getNearbyBubbles({
    required double? latitude,
    required double? longitude,
    required double? radius,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNearbyBubbles,
          [],
          {
            #latitude: latitude,
            #longitude: longitude,
            #radius: radius,
          },
        ),
        returnValue: _i3.Future<_i4.Result<List<_i5.BubbleEntity>>>.value(
            _i6.dummyValue<_i4.Result<List<_i5.BubbleEntity>>>(
          this,
          Invocation.method(
            #getNearbyBubbles,
            [],
            {
              #latitude: latitude,
              #longitude: longitude,
              #radius: radius,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<List<_i5.BubbleEntity>>>);

  @override
  _i3.Future<_i4.Result<_i5.BubbleEntity>> updateMemberStatus({
    required dynamic bubbleId,
    required dynamic memberId,
    bool? isOnline,
    int? unreadMessageCount,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateMemberStatus,
          [],
          {
            #bubbleId: bubbleId,
            #memberId: memberId,
            #isOnline: isOnline,
            #unreadMessageCount: unreadMessageCount,
          },
        ),
        returnValue: _i3.Future<_i4.Result<_i5.BubbleEntity>>.value(
            _i6.dummyValue<_i4.Result<_i5.BubbleEntity>>(
          this,
          Invocation.method(
            #updateMemberStatus,
            [],
            {
              #bubbleId: bubbleId,
              #memberId: memberId,
              #isOnline: isOnline,
              #unreadMessageCount: unreadMessageCount,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<_i5.BubbleEntity>>);

  @override
  _i3.Future<_i4.Result<void>> inviteToBubble({
    required dynamic bubbleId,
    required dynamic inviterId,
    required List<dynamic>? inviteeIds,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #inviteToBubble,
          [],
          {
            #bubbleId: bubbleId,
            #inviterId: inviterId,
            #inviteeIds: inviteeIds,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #inviteToBubble,
            [],
            {
              #bubbleId: bubbleId,
              #inviterId: inviterId,
              #inviteeIds: inviteeIds,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<_i5.BubbleEntity>> voteToRemoveMember({
    required dynamic bubbleId,
    required dynamic voterId,
    required dynamic targetMemberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #voteToRemoveMember,
          [],
          {
            #bubbleId: bubbleId,
            #voterId: voterId,
            #targetMemberId: targetMemberId,
          },
        ),
        returnValue: _i3.Future<_i4.Result<_i5.BubbleEntity>>.value(
            _i6.dummyValue<_i4.Result<_i5.BubbleEntity>>(
          this,
          Invocation.method(
            #voteToRemoveMember,
            [],
            {
              #bubbleId: bubbleId,
              #voterId: voterId,
              #targetMemberId: targetMemberId,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<_i5.BubbleEntity>>);

  @override
  _i3.Future<_i4.Result<void>> removeFromBubble({
    required dynamic bubbleId,
    required dynamic memberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeFromBubble,
          [],
          {
            #bubbleId: bubbleId,
            #memberId: memberId,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #removeFromBubble,
            [],
            {
              #bubbleId: bubbleId,
              #memberId: memberId,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<_i5.BubbleEntity>> updateBubbleInfo({
    required dynamic bubbleId,
    dynamic name,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBubbleInfo,
          [],
          {
            #bubbleId: bubbleId,
            #name: name,
            #endDate: endDate,
          },
        ),
        returnValue: _i3.Future<_i4.Result<_i5.BubbleEntity>>.value(
            _i6.dummyValue<_i4.Result<_i5.BubbleEntity>>(
          this,
          Invocation.method(
            #updateBubbleInfo,
            [],
            {
              #bubbleId: bubbleId,
              #name: name,
              #endDate: endDate,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<_i5.BubbleEntity>>);

  @override
  _i3.Future<_i4.Result<void>> markAllMessagesRead({
    required dynamic bubbleId,
    required dynamic memberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #markAllMessagesRead,
          [],
          {
            #bubbleId: bubbleId,
            #memberId: memberId,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #markAllMessagesRead,
            [],
            {
              #bubbleId: bubbleId,
              #memberId: memberId,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<void>> proposeMember({
    required dynamic bubbleId,
    required String? memberName,
    required String? memberEmail,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #proposeMember,
          [],
          {
            #bubbleId: bubbleId,
            #memberName: memberName,
            #memberEmail: memberEmail,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #proposeMember,
            [],
            {
              #bubbleId: bubbleId,
              #memberName: memberName,
              #memberEmail: memberEmail,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<void>> startCall({
    required dynamic bubbleId,
    required String? callId,
    required List<dynamic>? participants,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #startCall,
          [],
          {
            #bubbleId: bubbleId,
            #callId: callId,
            #participants: participants,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #startCall,
            [],
            {
              #bubbleId: bubbleId,
              #callId: callId,
              #participants: participants,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<void>> endCall({
    required dynamic bubbleId,
    required String? callId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #endCall,
          [],
          {
            #bubbleId: bubbleId,
            #callId: callId,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #endCall,
            [],
            {
              #bubbleId: bubbleId,
              #callId: callId,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<dynamic>> createRequest({
    required String? bubbleId,
    required String? targetId,
    required String? type,
    String? message,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createRequest,
          [],
          {
            #bubbleId: bubbleId,
            #targetId: targetId,
            #type: type,
            #message: message,
          },
        ),
        returnValue: _i3.Future<_i4.Result<dynamic>>.value(
            _i6.dummyValue<_i4.Result<dynamic>>(
          this,
          Invocation.method(
            #createRequest,
            [],
            {
              #bubbleId: bubbleId,
              #targetId: targetId,
              #type: type,
              #message: message,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<dynamic>>);

  @override
  _i3.Future<_i4.Result<dynamic>> respondToRequest({
    required String? requestId,
    required String? status,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #respondToRequest,
          [],
          {
            #requestId: requestId,
            #status: status,
          },
        ),
        returnValue: _i3.Future<_i4.Result<dynamic>>.value(
            _i6.dummyValue<_i4.Result<dynamic>>(
          this,
          Invocation.method(
            #respondToRequest,
            [],
            {
              #requestId: requestId,
              #status: status,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<dynamic>>);

  @override
  _i3.Future<_i4.Result<List<dynamic>>> getPendingRequests() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPendingRequests,
          [],
        ),
        returnValue: _i3.Future<_i4.Result<List<dynamic>>>.value(
            _i6.dummyValue<_i4.Result<List<dynamic>>>(
          this,
          Invocation.method(
            #getPendingRequests,
            [],
          ),
        )),
      ) as _i3.Future<_i4.Result<List<dynamic>>>);

  @override
  _i3.Future<void> selectFriends({
    required String? bubbleId,
    required List<String>? selectedUserIds,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectFriends,
          [],
          {
            #bubbleId: bubbleId,
            #selectedUserIds: selectedUserIds,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<_i4.Result<void>> acceptBubbleRequest({
    required String? requestId,
    required String? bubbleId,
    required String? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptBubbleRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #acceptBubbleRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<void>> declineBubbleRequest({
    required String? requestId,
    required String? bubbleId,
    required String? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #declineBubbleRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #declineBubbleRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<void>> acceptBubbleKickoutRequest({
    required String? requestId,
    required String? bubbleId,
    required String? targetMemberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptBubbleKickoutRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #targetMemberId: targetMemberId,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #acceptBubbleKickoutRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #targetMemberId: targetMemberId,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);

  @override
  _i3.Future<_i4.Result<void>> declineBubbleKickoutRequest({
    required String? requestId,
    required String? bubbleId,
    required String? targetMemberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #declineBubbleKickoutRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #targetMemberId: targetMemberId,
          },
        ),
        returnValue:
            _i3.Future<_i4.Result<void>>.value(_i6.dummyValue<_i4.Result<void>>(
          this,
          Invocation.method(
            #declineBubbleKickoutRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #targetMemberId: targetMemberId,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<void>>);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i8.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isAuthenticated => (super.noSuchMethod(
        Invocation.getter(#isAuthenticated),
        returnValue: false,
      ) as bool);

  @override
  _i3.Future<_i4.Result<_i7.UserModel>> login({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue: _i3.Future<_i4.Result<_i7.UserModel>>.value(
            _i6.dummyValue<_i4.Result<_i7.UserModel>>(
          this,
          Invocation.method(
            #login,
            [],
            {
              #email: email,
              #password: password,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<_i7.UserModel>>);

  @override
  _i3.Future<_i4.Result<_i7.UserModel>> loginWithGoogle() =>
      (super.noSuchMethod(
        Invocation.method(
          #loginWithGoogle,
          [],
        ),
        returnValue: _i3.Future<_i4.Result<_i7.UserModel>>.value(
            _i6.dummyValue<_i4.Result<_i7.UserModel>>(
          this,
          Invocation.method(
            #loginWithGoogle,
            [],
          ),
        )),
      ) as _i3.Future<_i4.Result<_i7.UserModel>>);

  @override
  _i3.Future<_i4.Result<_i7.UserModel>> loginWithApple() => (super.noSuchMethod(
        Invocation.method(
          #loginWithApple,
          [],
        ),
        returnValue: _i3.Future<_i4.Result<_i7.UserModel>>.value(
            _i6.dummyValue<_i4.Result<_i7.UserModel>>(
          this,
          Invocation.method(
            #loginWithApple,
            [],
          ),
        )),
      ) as _i3.Future<_i4.Result<_i7.UserModel>>);

  @override
  _i3.Future<_i4.Result<_i7.UserModel>> signUp({
    required String? email,
    required String? password,
    required String? username,
    required String? firstName,
    required String? lastName,
    DateTime? birthday,
    String? profilePictureUrl,
    bool? notificationsEnabled = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signUp,
          [],
          {
            #email: email,
            #password: password,
            #username: username,
            #firstName: firstName,
            #lastName: lastName,
            #birthday: birthday,
            #profilePictureUrl: profilePictureUrl,
            #notificationsEnabled: notificationsEnabled,
          },
        ),
        returnValue: _i3.Future<_i4.Result<_i7.UserModel>>.value(
            _i6.dummyValue<_i4.Result<_i7.UserModel>>(
          this,
          Invocation.method(
            #signUp,
            [],
            {
              #email: email,
              #password: password,
              #username: username,
              #firstName: firstName,
              #lastName: lastName,
              #birthday: birthday,
              #profilePictureUrl: profilePictureUrl,
              #notificationsEnabled: notificationsEnabled,
            },
          ),
        )),
      ) as _i3.Future<_i4.Result<_i7.UserModel>>);

  @override
  _i3.Future<_i4.Result<_i7.UserModel>> getCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i3.Future<_i4.Result<_i7.UserModel>>.value(
            _i6.dummyValue<_i4.Result<_i7.UserModel>>(
          this,
          Invocation.method(
            #getCurrentUser,
            [],
          ),
        )),
      ) as _i3.Future<_i4.Result<_i7.UserModel>>);

  @override
  _i3.Future<_i4.Result<bool>> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue:
            _i3.Future<_i4.Result<bool>>.value(_i6.dummyValue<_i4.Result<bool>>(
          this,
          Invocation.method(
            #logout,
            [],
          ),
        )),
      ) as _i3.Future<_i4.Result<bool>>);

  @override
  _i3.Future<_i4.Result<bool>> updateOnboardingStatus(
          bool? hasCompletedOnboarding) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateOnboardingStatus,
          [hasCompletedOnboarding],
        ),
        returnValue:
            _i3.Future<_i4.Result<bool>>.value(_i6.dummyValue<_i4.Result<bool>>(
          this,
          Invocation.method(
            #updateOnboardingStatus,
            [hasCompletedOnboarding],
          ),
        )),
      ) as _i3.Future<_i4.Result<bool>>);
}

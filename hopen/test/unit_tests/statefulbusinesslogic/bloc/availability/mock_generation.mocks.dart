// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in hopen/test/unit_tests/statefulbusinesslogic/bloc/availability/mock_generation.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:hopen/repositories/availability/availability_repository.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [AvailabilityRepository].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockAvailabilityRepository extends _i1.Mock
    implements _i2.AvailabilityRepository {
  MockAvailabilityRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<dynamic> checkEmailAvailability(String? email) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkEmailAvailability,
          [email],
        ),
        returnValue: _i3.Future<dynamic>.value(),
      ) as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> checkUsernameAvailability(String? username) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkUsernameAvailability,
          [username],
        ),
        returnValue: _i3.Future<dynamic>.value(),
      ) as _i3.Future<dynamic>);

  @override
  _i3.Future<Map<String, dynamic>> checkBothAvailability({
    required String? email,
    required String? username,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkBothAvailability,
          [],
          {
            #email: email,
            #username: username,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);
}

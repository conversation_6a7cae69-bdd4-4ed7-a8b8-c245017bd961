// Mocks generated by Mockito 5.4.5 from annotations
// in hopen/test/integration_tests/contact_request_flow_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:hopen/repositories/contact/contact_request_repository.dart'
    as _i2;
import 'package:hopen/repositories/friendship/friend_request_repository.dart'
    as _i8;
import 'package:hopen/statefulbusinesslogic/core/error/result.dart' as _i5;
import 'package:hopen/statefulbusinesslogic/core/events/mqtt_event_interface.dart'
    as _i4;
import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart' as _i7;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ContactRequestRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockContactRequestRepository extends _i1.Mock
    implements _i2.ContactRequestRepository {
  MockContactRequestRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Stream<List<_i2.ContactRequest>> get pendingReceivedRequestsStream =>
      (super.noSuchMethod(
        Invocation.getter(#pendingReceivedRequestsStream),
        returnValue: _i3.Stream<List<_i2.ContactRequest>>.empty(),
      ) as _i3.Stream<List<_i2.ContactRequest>>);

  @override
  _i3.Stream<List<_i2.ContactRequest>> get pendingSentRequestsStream =>
      (super.noSuchMethod(
        Invocation.getter(#pendingSentRequestsStream),
        returnValue: _i3.Stream<List<_i2.ContactRequest>>.empty(),
      ) as _i3.Stream<List<_i2.ContactRequest>>);

  @override
  _i3.Stream<_i4.MqttEvent> get eventStream => (super.noSuchMethod(
        Invocation.getter(#eventStream),
        returnValue: _i3.Stream<_i4.MqttEvent>.empty(),
      ) as _i3.Stream<_i4.MqttEvent>);

  @override
  _i3.Future<bool> get hasCachedData => (super.noSuchMethod(
        Invocation.getter(#hasCachedData),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<_i5.Result<_i2.ContactRequest>> sendContactRequest(
    String? receiverId,
    String? message,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendContactRequest,
          [
            receiverId,
            message,
          ],
        ),
        returnValue: _i3.Future<_i5.Result<_i2.ContactRequest>>.value(
            _i6.dummyValue<_i5.Result<_i2.ContactRequest>>(
          this,
          Invocation.method(
            #sendContactRequest,
            [
              receiverId,
              message,
            ],
          ),
        )),
      ) as _i3.Future<_i5.Result<_i2.ContactRequest>>);

  @override
  _i3.Future<_i5.Result<List<_i2.ContactRequest>>>
      getPendingReceivedRequests() => (super.noSuchMethod(
            Invocation.method(
              #getPendingReceivedRequests,
              [],
            ),
            returnValue: _i3.Future<_i5.Result<List<_i2.ContactRequest>>>.value(
                _i6.dummyValue<_i5.Result<List<_i2.ContactRequest>>>(
              this,
              Invocation.method(
                #getPendingReceivedRequests,
                [],
              ),
            )),
          ) as _i3.Future<_i5.Result<List<_i2.ContactRequest>>>);

  @override
  _i3.Future<_i5.Result<List<_i2.ContactRequest>>> getPendingSentRequests() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPendingSentRequests,
          [],
        ),
        returnValue: _i3.Future<_i5.Result<List<_i2.ContactRequest>>>.value(
            _i6.dummyValue<_i5.Result<List<_i2.ContactRequest>>>(
          this,
          Invocation.method(
            #getPendingSentRequests,
            [],
          ),
        )),
      ) as _i3.Future<_i5.Result<List<_i2.ContactRequest>>>);

  @override
  _i3.Future<_i5.Result<void>> acceptContactRequest(String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptContactRequest,
          [requestId],
        ),
        returnValue:
            _i3.Future<_i5.Result<void>>.value(_i6.dummyValue<_i5.Result<void>>(
          this,
          Invocation.method(
            #acceptContactRequest,
            [requestId],
          ),
        )),
      ) as _i3.Future<_i5.Result<void>>);

  @override
  _i3.Future<_i5.Result<void>> rejectContactRequest(String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #rejectContactRequest,
          [requestId],
        ),
        returnValue:
            _i3.Future<_i5.Result<void>>.value(_i6.dummyValue<_i5.Result<void>>(
          this,
          Invocation.method(
            #rejectContactRequest,
            [requestId],
          ),
        )),
      ) as _i3.Future<_i5.Result<void>>);

  @override
  _i3.Future<_i5.Result<void>> cancelContactRequest(String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #cancelContactRequest,
          [requestId],
        ),
        returnValue:
            _i3.Future<_i5.Result<void>>.value(_i6.dummyValue<_i5.Result<void>>(
          this,
          Invocation.method(
            #cancelContactRequest,
            [requestId],
          ),
        )),
      ) as _i3.Future<_i5.Result<void>>);

  @override
  _i3.Future<_i5.Result<_i2.ContactRequest>> getContactRequest(
          String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getContactRequest,
          [requestId],
        ),
        returnValue: _i3.Future<_i5.Result<_i2.ContactRequest>>.value(
            _i6.dummyValue<_i5.Result<_i2.ContactRequest>>(
          this,
          Invocation.method(
            #getContactRequest,
            [requestId],
          ),
        )),
      ) as _i3.Future<_i5.Result<_i2.ContactRequest>>);

  @override
  _i3.Future<_i5.Result<bool>> hasPendingRequest(
    String? userId1,
    String? userId2,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #hasPendingRequest,
          [
            userId1,
            userId2,
          ],
        ),
        returnValue:
            _i3.Future<_i5.Result<bool>>.value(_i6.dummyValue<_i5.Result<bool>>(
          this,
          Invocation.method(
            #hasPendingRequest,
            [
              userId1,
              userId2,
            ],
          ),
        )),
      ) as _i3.Future<_i5.Result<bool>>);

  @override
  _i3.Future<_i5.Result<List<_i2.ContactRequest>>> getContactRequestHistory() =>
      (super.noSuchMethod(
        Invocation.method(
          #getContactRequestHistory,
          [],
        ),
        returnValue: _i3.Future<_i5.Result<List<_i2.ContactRequest>>>.value(
            _i6.dummyValue<_i5.Result<List<_i2.ContactRequest>>>(
          this,
          Invocation.method(
            #getContactRequestHistory,
            [],
          ),
        )),
      ) as _i3.Future<_i5.Result<List<_i2.ContactRequest>>>);

  @override
  _i3.Future<_i5.Result<List<_i7.UserModel>>> getMutualContacts(
          String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMutualContacts,
          [userId],
        ),
        returnValue: _i3.Future<_i5.Result<List<_i7.UserModel>>>.value(
            _i6.dummyValue<_i5.Result<List<_i7.UserModel>>>(
          this,
          Invocation.method(
            #getMutualContacts,
            [userId],
          ),
        )),
      ) as _i3.Future<_i5.Result<List<_i7.UserModel>>>);

  @override
  _i3.Future<_i5.Result<List<_i7.UserModel>>> searchUsers(String? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchUsers,
          [query],
        ),
        returnValue: _i3.Future<_i5.Result<List<_i7.UserModel>>>.value(
            _i6.dummyValue<_i5.Result<List<_i7.UserModel>>>(
          this,
          Invocation.method(
            #searchUsers,
            [query],
          ),
        )),
      ) as _i3.Future<_i5.Result<List<_i7.UserModel>>>);

  @override
  _i3.Future<_i5.Result<List<_i7.UserModel>>> getSuggestedContacts() =>
      (super.noSuchMethod(
        Invocation.method(
          #getSuggestedContacts,
          [],
        ),
        returnValue: _i3.Future<_i5.Result<List<_i7.UserModel>>>.value(
            _i6.dummyValue<_i5.Result<List<_i7.UserModel>>>(
          this,
          Invocation.method(
            #getSuggestedContacts,
            [],
          ),
        )),
      ) as _i3.Future<_i5.Result<List<_i7.UserModel>>>);

  @override
  _i3.Future<_i5.Result<void>> expireOldRequests() => (super.noSuchMethod(
        Invocation.method(
          #expireOldRequests,
          [],
        ),
        returnValue:
            _i3.Future<_i5.Result<void>>.value(_i6.dummyValue<_i5.Result<void>>(
          this,
          Invocation.method(
            #expireOldRequests,
            [],
          ),
        )),
      ) as _i3.Future<_i5.Result<void>>);

  @override
  _i3.Future<void> handleMqttEvent(_i4.MqttEvent? event) => (super.noSuchMethod(
        Invocation.method(
          #handleMqttEvent,
          [event],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> processContactRequestEvent(_i4.ContactRequestEvent? event) =>
      (super.noSuchMethod(
        Invocation.method(
          #processContactRequestEvent,
          [event],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> processRealTimeEvent(_i4.MqttEvent? event) =>
      (super.noSuchMethod(
        Invocation.method(
          #processRealTimeEvent,
          [event],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> refreshFromRemote() => (super.noSuchMethod(
        Invocation.method(
          #refreshFromRemote,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> clearCache() => (super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}

/// A class which mocks [FriendRequestRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockFriendRequestRepository extends _i1.Mock
    implements _i8.FriendRequestRepository {
  MockFriendRequestRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Stream<List<dynamic>> get pendingFriendRequestsStream =>
      (super.noSuchMethod(
        Invocation.getter(#pendingFriendRequestsStream),
        returnValue: _i3.Stream<List<dynamic>>.empty(),
      ) as _i3.Stream<List<dynamic>>);

  @override
  _i3.Stream<_i4.MqttEvent> get eventStream => (super.noSuchMethod(
        Invocation.getter(#eventStream),
        returnValue: _i3.Stream<_i4.MqttEvent>.empty(),
      ) as _i3.Stream<_i4.MqttEvent>);

  @override
  _i3.Future<bool> get hasCachedData => (super.noSuchMethod(
        Invocation.getter(#hasCachedData),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<_i5.Result<List<dynamic>>> getPendingFriendRequests() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPendingFriendRequests,
          [],
        ),
        returnValue: _i3.Future<_i5.Result<List<dynamic>>>.value(
            _i6.dummyValue<_i5.Result<List<dynamic>>>(
          this,
          Invocation.method(
            #getPendingFriendRequests,
            [],
          ),
        )),
      ) as _i3.Future<_i5.Result<List<dynamic>>>);

  @override
  _i3.Future<_i5.Result<void>> acceptFriendRequest(String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptFriendRequest,
          [requestId],
        ),
        returnValue:
            _i3.Future<_i5.Result<void>>.value(_i6.dummyValue<_i5.Result<void>>(
          this,
          Invocation.method(
            #acceptFriendRequest,
            [requestId],
          ),
        )),
      ) as _i3.Future<_i5.Result<void>>);

  @override
  _i3.Future<_i5.Result<void>> declineFriendRequest(String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #declineFriendRequest,
          [requestId],
        ),
        returnValue:
            _i3.Future<_i5.Result<void>>.value(_i6.dummyValue<_i5.Result<void>>(
          this,
          Invocation.method(
            #declineFriendRequest,
            [requestId],
          ),
        )),
      ) as _i3.Future<_i5.Result<void>>);

  @override
  _i3.Future<_i5.Result<dynamic>> getFriendRequest(String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getFriendRequest,
          [requestId],
        ),
        returnValue: _i3.Future<_i5.Result<dynamic>>.value(
            _i6.dummyValue<_i5.Result<dynamic>>(
          this,
          Invocation.method(
            #getFriendRequest,
            [requestId],
          ),
        )),
      ) as _i3.Future<_i5.Result<dynamic>>);

  @override
  _i3.Future<_i5.Result<List<String>>> getFriends() => (super.noSuchMethod(
        Invocation.method(
          #getFriends,
          [],
        ),
        returnValue: _i3.Future<_i5.Result<List<String>>>.value(
            _i6.dummyValue<_i5.Result<List<String>>>(
          this,
          Invocation.method(
            #getFriends,
            [],
          ),
        )),
      ) as _i3.Future<_i5.Result<List<String>>>);

  @override
  _i3.Future<void> handleMqttEvent(_i4.MqttEvent? event) => (super.noSuchMethod(
        Invocation.method(
          #handleMqttEvent,
          [event],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> processFriendRequestEvent(_i4.FriendRequestEvent? event) =>
      (super.noSuchMethod(
        Invocation.method(
          #processFriendRequestEvent,
          [event],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> processRealTimeEvent(_i4.MqttEvent? event) =>
      (super.noSuchMethod(
        Invocation.method(
          #processRealTimeEvent,
          [event],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> refreshFromRemote() => (super.noSuchMethod(
        Invocation.method(
          #refreshFromRemote,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> clearCache() => (super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}

import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart';
import 'package:hopen/statefulbusinesslogic/core/models/bubble_entity.dart';
import 'package:hopen/statefulbusinesslogic/core/models/message_model.dart';

/// Mock data generators for testing
class MockData {
  /// Generate mock users
  static List<UserModel> generateUsers(int count) {
    return List.generate(count, (index) => UserModel(
      id: 'user-$index',
      email: 'user$<EMAIL>',
      username: 'user$index',
      firstName: 'User',
      lastName: '$index',
      hasCompletedOnboarding: index % 2 == 0,
      profilePictureUrl: index % 3 == 0 ? 'https://example.com/avatar$index.jpg' : null,
      createdAt: DateTime.now().subtract(Duration(days: index)),
      updatedAt: DateTime.now().subtract(Duration(hours: index)),
    ));
  }

  /// Generate mock bubbles
  static List<BubbleEntity> generateBubbles(int count, {List<String>? creatorIds}) {
    final creators = creatorIds ?? ['creator-1', 'creator-2', 'creator-3'];

    return List.generate(count, (index) {
      final bubbleResult = BubbleEntity.create(
        id: 'bubble-$index',
        name: 'Bubble $index',
        capacity: 8 + (index % 4), // 8-11 members
        createdAt: DateTime.now().subtract(Duration(days: index * 2)),
        status: index % 4 != 0 ? BubbleLifecycleStatus.active : BubbleLifecycleStatus.expired,
      );

      if (bubbleResult.isFailure) {
        throw Exception('Failed to create mock bubble: ${bubbleResult.error}');
      }

      return bubbleResult.data;
    });
  }

  /// Generate mock messages
  static List<MessageModel> generateMessages(
    int count, {
    String? chatId,
    List<String>? senderIds,
    List<MessageType>? types,
  }) {
    final defaultSenderIds = senderIds ?? ['user-1', 'user-2', 'user-3'];
    final defaultTypes = types ?? [MessageType.text, MessageType.image, MessageType.video];
    
    return List.generate(count, (index) {
      final type = defaultTypes[index % defaultTypes.length];
      return MessageModel(
        id: 'message-$index',
        chatId: chatId ?? 'chat-1',
        senderId: defaultSenderIds[index % defaultSenderIds.length],
        content: _generateMessageContent(type, index),
        type: type,
        status: _generateMessageStatus(index),
        createdAt: DateTime.now().subtract(Duration(minutes: count - index)),
      );
    });
  }

  /// Generate mock chat data
  static Map<String, dynamic> generateChatData({
    String? chatId,
    String? chatName,
    List<String>? memberIds,
    int messageCount = 20,
  }) {
    final id = chatId ?? 'chat-${DateTime.now().millisecondsSinceEpoch}';
    final members = memberIds ?? ['user-1', 'user-2', 'user-3'];
    
    return {
      'id': id,
      'name': chatName ?? 'Test Chat',
      'type': 'group',
      'members': members,
      'messages': generateMessages(
        messageCount,
        chatId: id,
        senderIds: members,
      ),
      'lastActivity': DateTime.now().subtract(const Duration(minutes: 5)),
      'unreadCount': messageCount ~/ 4,
    };
  }

  /// Generate mock API responses
  static Map<String, dynamic> generateApiResponse({
    required String endpoint,
    bool success = true,
    Map<String, dynamic>? data,
  }) {
    if (!success) {
      return {
        'success': false,
        'error': {
          'code': 'API_ERROR',
          'message': 'Mock API error for $endpoint',
        },
        'timestamp': DateTime.now().toIso8601String(),
      };
    }

    switch (endpoint) {
      case 'auth/login':
        return {
          'success': true,
          'data': {
            'user': generateUsers(1).first.toJson(),
            'token': 'mock-auth-token-${DateTime.now().millisecondsSinceEpoch}',
            'refreshToken': 'mock-refresh-token-${DateTime.now().millisecondsSinceEpoch}',
            'expiresAt': DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
          },
          'timestamp': DateTime.now().toIso8601String(),
        };

      case 'auth/register':
        return {
          'success': true,
          'data': {
            'user': generateUsers(1).first.toJson(),
            'message': 'Registration successful',
          },
          'timestamp': DateTime.now().toIso8601String(),
        };

      case 'bubbles/list':
        return {
          'success': true,
          'data': {
            'bubbles': generateBubbles(5).map((b) => b.toJson()).toList(),
            'pagination': {
              'page': 1,
              'limit': 10,
              'total': 5,
              'hasMore': false,
            },
          },
          'timestamp': DateTime.now().toIso8601String(),
        };

      case 'bubbles/create':
        return {
          'success': true,
          'data': {
            'bubble': generateBubbles(1).first.toJson(),
            'message': 'Bubble created successfully',
          },
          'timestamp': DateTime.now().toIso8601String(),
        };

      case 'chat/messages':
        return {
          'success': true,
          'data': {
            'messages': generateMessages(10).map((m) => m.toJson()).toList(),
            'pagination': {
              'page': 1,
              'limit': 50,
              'total': 10,
              'hasMore': false,
            },
          },
          'timestamp': DateTime.now().toIso8601String(),
        };

      case 'chat/send':
        return {
          'success': true,
          'data': {
            'message': generateMessages(1).first.toJson(),
            'messageId': 'msg-${DateTime.now().millisecondsSinceEpoch}',
          },
          'timestamp': DateTime.now().toIso8601String(),
        };

      default:
        return {
          'success': true,
          'data': data ?? {},
          'timestamp': DateTime.now().toIso8601String(),
        };
    }
  }

  /// Generate mock WebRTC data
  static Map<String, dynamic> generateWebRTCData({
    required String type,
    Map<String, dynamic>? customData,
  }) {
    switch (type) {
      case 'call_offer':
        return {
          'type': 'offer',
          'callId': 'call-${DateTime.now().millisecondsSinceEpoch}',
          'callerId': 'user-1',
          'calleeId': 'user-2',
          'isVideoCall': true,
          'sdp': 'mock-sdp-offer-data',
          'timestamp': DateTime.now().toIso8601String(),
          ...?customData,
        };

      case 'call_answer':
        return {
          'type': 'answer',
          'callId': 'call-${DateTime.now().millisecondsSinceEpoch}',
          'callerId': 'user-1',
          'calleeId': 'user-2',
          'sdp': 'mock-sdp-answer-data',
          'timestamp': DateTime.now().toIso8601String(),
          ...?customData,
        };

      case 'ice_candidate':
        return {
          'type': 'ice-candidate',
          'callId': 'call-${DateTime.now().millisecondsSinceEpoch}',
          'candidate': 'mock-ice-candidate-data',
          'sdpMid': 'audio',
          'sdpMLineIndex': 0,
          'timestamp': DateTime.now().toIso8601String(),
          ...?customData,
        };

      case 'call_end':
        return {
          'type': 'call-end',
          'callId': 'call-${DateTime.now().millisecondsSinceEpoch}',
          'reason': 'user_hangup',
          'duration': 120, // 2 minutes
          'timestamp': DateTime.now().toIso8601String(),
          ...?customData,
        };

      default:
        return {
          'type': type,
          'timestamp': DateTime.now().toIso8601String(),
          ...?customData,
        };
    }
  }

  /// Generate mock MQTT messages
  static Map<String, dynamic> generateMQTTMessage({
    required String topic,
    Map<String, dynamic>? payload,
  }) {
    return {
      'topic': topic,
      'payload': payload ?? {'message': 'Mock MQTT message'},
      'qos': 1,
      'retain': false,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Generate mock notification data
  static Map<String, dynamic> generateNotificationData({
    required String type,
    Map<String, dynamic>? customData,
  }) {
    switch (type) {
      case 'message':
        return {
          'type': 'message',
          'title': 'New Message',
          'body': 'You have a new message from User 1',
          'data': {
            'chatId': 'chat-1',
            'senderId': 'user-1',
            'messageId': 'msg-${DateTime.now().millisecondsSinceEpoch}',
          },
          'timestamp': DateTime.now().toIso8601String(),
          ...?customData,
        };

      case 'call':
        return {
          'type': 'call',
          'title': 'Incoming Call',
          'body': 'User 1 is calling you',
          'data': {
            'callId': 'call-${DateTime.now().millisecondsSinceEpoch}',
            'callerId': 'user-1',
            'isVideoCall': true,
          },
          'timestamp': DateTime.now().toIso8601String(),
          ...?customData,
        };

      case 'bubble_invite':
        return {
          'type': 'bubble_invite',
          'title': 'Bubble Invitation',
          'body': 'You have been invited to join Bubble 1',
          'data': {
            'bubbleId': 'bubble-1',
            'inviterId': 'user-1',
            'inviteId': 'invite-${DateTime.now().millisecondsSinceEpoch}',
          },
          'timestamp': DateTime.now().toIso8601String(),
          ...?customData,
        };

      default:
        return {
          'type': type,
          'title': 'Notification',
          'body': 'Mock notification',
          'timestamp': DateTime.now().toIso8601String(),
          ...?customData,
        };
    }
  }

  // Private helper methods
  static List<String> _generateMemberIds(int index) {
    final memberCount = 2 + (index % 6); // 2-7 members
    return List.generate(memberCount, (i) => 'member-${index}-$i');
  }

  static String _generateMessageContent(MessageType type, int index) {
    switch (type) {
      case MessageType.text:
        return 'This is test message number $index';
      case MessageType.image:
        return 'https://storage.hopenapp.com/images/test-image-$index.jpg';
      case MessageType.video:
        return 'https://storage.hopenapp.com/videos/test-video-$index.mp4';
      case MessageType.audio:
        return 'https://storage.hopenapp.com/audio/test-audio-$index.mp3';
      case MessageType.file:
        return 'https://storage.hopenapp.com/files/test-file-$index.pdf';
      default:
        return 'Unknown message type';
    }
  }

  static MessageStatus _generateMessageStatus(int index) {
    final statuses = [MessageStatus.sent, MessageStatus.delivered, MessageStatus.read];
    return statuses[index % statuses.length];
  }
}

/// Mock data constants
class MockDataConstants {
  static const String defaultUserId = 'test-user-id';
  static const String defaultUserEmail = '<EMAIL>';
  static const String defaultBubbleId = 'test-bubble-id';
  static const String defaultChatId = 'test-chat-id';
  static const String defaultMessageId = 'test-message-id';
  
  static const String mockAuthToken = 'mock-auth-token-12345';
  static const String mockRefreshToken = 'mock-refresh-token-12345';
  
  static const String mockApiBaseUrl = 'https://api.test.hopenapp.com';
  static const String mockWebSocketUrl = 'wss://ws.test.hopenapp.com';
  static const String mockMqttBrokerUrl = 'mqtt://mqtt.test.hopenapp.com';
  
  static const Duration mockApiTimeout = Duration(seconds: 5);
  static const Duration mockConnectionTimeout = Duration(seconds: 3);
}

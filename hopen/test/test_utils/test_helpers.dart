import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:hopen/config/app_config.dart';
import 'package:hopen/di/injection_container_refactored.dart' as di;
import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart';
import 'package:hopen/statefulbusinesslogic/core/models/bubble_entity.dart';
import 'package:hopen/statefulbusinesslogic/core/models/message_model.dart';
import 'package:hopen/statefulbusinesslogic/core/error/result.dart';

/// Test utilities and helpers for the Hopen app
class TestHelpers {
  /// Initialize test environment
  static Future<void> initializeTestEnvironment() async {
    AppConfig.setTestEnvironment();
    await di.init(isTestEnvironment: true);
  }

  /// Clean up test environment
  static Future<void> cleanupTestEnvironment() async {
    await di.sl.reset();
    AppConfig.resetTestEnvironment();
  }

  /// Create a test widget with necessary providers
  static Widget createTestWidget({
    required Widget child,
    List<BlocProvider>? providers,
    NavigatorObserver? navigatorObserver,
  }) {
    return MaterialApp(
      home: MultiBlocProvider(
        providers: providers ?? [],
        child: child,
      ),
      navigatorObservers: navigatorObserver != null ? [navigatorObserver] : [],
    );
  }

  /// Create a test user model
  static UserModel createTestUser({
    String? id,
    String? email,
    String? username,
    String? firstName,
    String? lastName,
    bool hasCompletedOnboarding = false,
    String? profilePictureUrl,
  }) {
    return UserModel(
      id: id ?? 'test-user-${DateTime.now().millisecondsSinceEpoch}',
      email: email ?? '<EMAIL>',
      username: username ?? 'testuser',
      firstName: firstName ?? 'Test',
      lastName: lastName ?? 'User',
      hasCompletedOnboarding: hasCompletedOnboarding,
      profilePictureUrl: profilePictureUrl,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Create a test bubble entity
  static BubbleEntity createTestBubble({
    String? id,
    String? name,
    String? description,
    String? creatorId,
    List<String>? memberIds,
    bool isActive = true,
    bool isPublic = false,
    int maxMembers = 8,
  }) {
    final bubbleResult = BubbleEntity.create(
      id: id ?? 'test-bubble-${DateTime.now().millisecondsSinceEpoch}',
      name: name ?? 'Test Bubble',
      capacity: maxMembers,
      createdAt: DateTime.now(),
      status: isActive ? BubbleLifecycleStatus.active : BubbleLifecycleStatus.expired,
    );

    if (bubbleResult.isFailure) {
      throw Exception('Failed to create test bubble: ${bubbleResult.error}');
    }

    return bubbleResult.data;
  }

  /// Create a test message model
  static MessageModel createTestMessage({
    String? id,
    String? chatId,
    String? senderId,
    String? content,
    MessageType type = MessageType.text,
    MessageStatus status = MessageStatus.sent,
    bool isRead = false,
  }) {
    return MessageModel(
      id: id ?? 'test-message-${DateTime.now().millisecondsSinceEpoch}',
      chatId: chatId ?? 'test-chat-id',
      senderId: senderId ?? 'test-sender-id',
      content: content ?? 'Test message content',
      type: type,
      status: status,
      createdAt: DateTime.now(),
    );
  }

  /// Create a list of test messages
  static List<MessageModel> createTestMessages({
    int count = 5,
    String? chatId,
    List<String>? senderIds,
  }) {
    final messages = <MessageModel>[];
    final defaultSenderIds = senderIds ?? ['user1', 'user2'];
    
    for (int i = 0; i < count; i++) {
      messages.add(createTestMessage(
        id: 'message-$i',
        chatId: chatId ?? 'test-chat-id',
        senderId: defaultSenderIds[i % defaultSenderIds.length],
        content: 'Test message $i',
      ));
    }
    
    return messages;
  }

  /// Wait for a specific condition to be true
  static Future<void> waitForCondition(
    bool Function() condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    final stopwatch = Stopwatch()..start();
    
    while (!condition() && stopwatch.elapsed < timeout) {
      await Future.delayed(interval);
    }
    
    if (!condition()) {
      throw TimeoutException('Condition not met within timeout', timeout);
    }
  }

  /// Pump and settle with custom duration
  static Future<void> pumpAndSettleWithTimeout(
    WidgetTester tester, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    await tester.pumpAndSettle(timeout);
  }

  /// Find widget by type and verify it exists
  static T findWidgetByType<T extends Widget>(WidgetTester tester) {
    final finder = find.byType(T);
    expect(finder, findsOneWidget);
    return tester.widget<T>(finder);
  }

  /// Find widget by key and verify it exists
  static T findWidgetByKey<T extends Widget>(WidgetTester tester, Key key) {
    final finder = find.byKey(key);
    expect(finder, findsOneWidget);
    return tester.widget<T>(finder);
  }

  /// Tap widget and pump
  static Future<void> tapAndPump(
    WidgetTester tester,
    Finder finder, {
    Duration? pumpDuration,
  }) async {
    await tester.tap(finder);
    if (pumpDuration != null) {
      await tester.pump(pumpDuration);
    } else {
      await tester.pump();
    }
  }

  /// Enter text and pump
  static Future<void> enterTextAndPump(
    WidgetTester tester,
    Finder finder,
    String text, {
    Duration? pumpDuration,
  }) async {
    await tester.enterText(finder, text);
    if (pumpDuration != null) {
      await tester.pump(pumpDuration);
    } else {
      await tester.pump();
    }
  }

  /// Verify that a specific error is shown
  static void verifyErrorMessage(WidgetTester tester, String expectedError) {
    expect(find.text(expectedError), findsOneWidget);
  }

  /// Verify that a loading indicator is shown
  static void verifyLoadingIndicator(WidgetTester tester) {
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  }

  /// Verify that no loading indicator is shown
  static void verifyNoLoadingIndicator(WidgetTester tester) {
    expect(find.byType(CircularProgressIndicator), findsNothing);
  }

  /// Create a mock success result
  static Result<T> createSuccessResult<T>(T data) {
    return Result.success(data);
  }

  /// Create a mock failure result
  static Result<T> createFailureResult<T>(String message) {
    return Result.failure(NetworkError(message: message));
  }

  /// Verify that a mock was called with specific parameters
  static void verifyMockCall<T>(
    Mock mock,
    Function method,
    List<dynamic> parameters, {
    int times = 1,
  }) {
    verify(method).called(times);
  }

  /// Setup mock to return success result
  static void setupMockSuccess<T>(
    Mock mock,
    Function method,
    T data,
  ) {
    when(method).thenAnswer((_) async => Result.success(data));
  }

  /// Setup mock to return failure result
  static void setupMockFailure<T>(
    Mock mock,
    Function method,
    String errorMessage,
  ) {
    when(method).thenAnswer(
      (_) async => Result.failure(NetworkError(message: errorMessage)),
    );
  }

  /// Create test data for different scenarios
  static Map<String, dynamic> createTestData({
    required String scenario,
  }) {
    switch (scenario) {
      case 'auth_success':
        return {
          'user': createTestUser(),
          'token': 'test-auth-token',
          'refreshToken': 'test-refresh-token',
        };
      case 'auth_failure':
        return {
          'error': 'Invalid credentials',
          'code': 'AUTH_FAILED',
        };
      case 'bubble_data':
        return {
          'bubble': createTestBubble(),
          'members': [createTestUser(), createTestUser()],
        };
      case 'chat_data':
        return {
          'messages': createTestMessages(),
          'chatInfo': {
            'id': 'test-chat-id',
            'name': 'Test Chat',
            'type': 'group',
          },
        };
      default:
        return {};
    }
  }

  /// Generate test IDs
  static String generateTestId([String? prefix]) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix ?? 'test'}-$timestamp';
  }

  /// Create test date time
  static DateTime createTestDateTime({int? daysAgo, int? hoursAgo}) {
    var dateTime = DateTime.now();
    if (daysAgo != null) {
      dateTime = dateTime.subtract(Duration(days: daysAgo));
    }
    if (hoursAgo != null) {
      dateTime = dateTime.subtract(Duration(hours: hoursAgo));
    }
    return dateTime;
  }
}

/// Custom matchers for testing
class TestMatchers {
  /// Matcher for Result success
  static Matcher isSuccessResult<T>([T? expectedData]) {
    return predicate<Result<T>>((result) {
      if (!result.isSuccess) return false;
      if (expectedData != null) {
        return result.data == expectedData;
      }
      return true;
    }, 'is a success result${expectedData != null ? ' with data $expectedData' : ''}');
  }

  /// Matcher for Result failure
  static Matcher isFailureResult([String? expectedMessage]) {
    return predicate<Result>((result) {
      if (!result.isFailure) return false;
      if (expectedMessage != null) {
        return result.error?.message == expectedMessage;
      }
      return true;
    }, 'is a failure result${expectedMessage != null ? ' with message $expectedMessage' : ''}');
  }

  /// Matcher for UserModel
  static Matcher isUserModel({
    String? id,
    String? email,
    String? username,
  }) {
    return predicate<UserModel>((user) {
      if (id != null && user.id != id) return false;
      if (email != null && user.email != email) return false;
      if (username != null && user.username != username) return false;
      return true;
    }, 'is a UserModel with specified properties');
  }

  /// Matcher for BubbleEntity
  static Matcher isBubbleEntity({
    String? id,
    String? name,
  }) {
    return predicate<BubbleEntity>((bubble) {
      if (id != null && bubble.id.value != id) return false;
      if (name != null && bubble.name.value != name) return false;
      return true;
    }, 'is a BubbleEntity with specified properties');
  }
}

/// Exception for test timeouts
class TimeoutException implements Exception {
  const TimeoutException(this.message, this.timeout);
  
  final String message;
  final Duration timeout;
  
  @override
  String toString() => 'TimeoutException: $message (timeout: $timeout)';
}

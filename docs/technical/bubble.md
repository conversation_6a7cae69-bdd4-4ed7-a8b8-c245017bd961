# Bubble Feature Documentation

## Overview

The Bubble feature is the core social interaction mechanism in Hopen, allowing users to create and participate in time-bounded social groups. Each bubble has a defined lifecycle and member limit, encouraging dynamic social interactions. The system is built with clean architecture principles (BLoC pattern on the client) and communicates with the new Go micro-services backend (Gin + PostgreSQL + ArangoDB) over REST. Real-time updates are delivered through EMQX (MQTT 5) and critical domain events are published on NATS JetStream.

**Recent Major Updates (January 2025):**
- **Complete Model Refactoring** – migrated from `BubbleModel` to `BubbleEntity` with rich domain logic
- **Backend Migration** – backend migrated to **Go 1.23 micro-services (Gin)** with PostgreSQL, ArangoDB & Cassandra
- **Clean Architecture Implementation** – added proper DTO-to-Entity mapping with ByteByteGo best practices
- **Enhanced Error Handling** – comprehensive `Result<T>` pattern implementation
- **Value Objects** – type-safe identifiers and validation with `B<PERSON>bleId`, `UserId`, `<PERSON><PERSON>bleName`
- **Domain-Driven Design** – rich business logic in domain entities with proper validation
- **Repository Pattern** – clean separation between interfaces and implementations

## Core Domain Models

### BubbleEntity (Domain Layer)

The core domain entity representing a bubble with comprehensive business logic:

```dart
class BubbleEntity extends Equatable {
  static const int maxMembers = 5;
  static const int minActiveMembers = 2;

  final BubbleId id;
  final BubbleName name;
  final MemberCapacity capacity;
  final List<BubbleMemberEntity> members;
  final DateTime createdAt;
  final DateTime? endDate;
  final BubbleCallEntity? activeCall;
  final BubbleLifecycleStatus status;
  final List<RemovalVote> activeVotes;

  const BubbleEntity({
    required this.id,
    required this.name,
    required this.capacity,
    required this.members,
    required this.createdAt,
    this.endDate,
    this.activeCall,
    this.status = BubbleLifecycleStatus.active,
    this.activeVotes = const [],
  });

  /// Create a bubble with comprehensive validation
  static Result<BubbleEntity> create({
    required String id,
    required String name,
    int? capacity,
    List<BubbleMemberEntity> members = const [],
    required DateTime createdAt,
    DateTime? endDate,
    BubbleCallEntity? activeCall,
    BubbleLifecycleStatus status = BubbleLifecycleStatus.creating,
    List<RemovalVote> activeVotes = const [],
  }) {
    // Comprehensive validation logic
    // Returns Result<BubbleEntity> for proper error handling
  }

  // Rich business logic methods
  bool get isActive => status == BubbleLifecycleStatus.active && !isExpired;
  bool get isExpired => endDate != null && DateTime.now().isAfter(endDate!);
  bool get isAtCapacity => activeMembersCount >= capacity.value;
  bool get shouldDissolve => activeMembersCount < minActiveMembers && activeMembersCount > 0;
  bool get hasActiveCall => activeCall?.isActive == true;
  
  int get activeMembersCount => members.where((m) => m.isActive).length;
  int get onlineMembersCount => members.where((m) => m.isOnline).length;
  int get totalUnreadMessages => members.fold(0, (sum, m) => sum + m.unreadMessageCount);

  // Member management
  bool isMember(String userId) => members.any((m) => m.id.value == userId);
  bool isActiveMember(String userId) => members.any((m) => m.id.value == userId && m.isActive);
  BubbleMemberEntity? getMember(String userId) => members.firstWhereOrNull((m) => m.id.value == userId);

  // Business rule validation
  Result<void> validateJoin(String userId) { /* validation logic */ }
  Result<void> validateLeave(String userId) { /* validation logic */ }
  Result<void> validateInvite(String inviterId, String inviteeId) { /* validation logic */ }
  Result<void> validateVote(String voterId, String targetId) { /* validation logic */ }
}
```

### BubbleMemberEntity (Enhanced Member Management)

Enhanced member entity with comprehensive status tracking:

```dart
class BubbleMemberEntity extends Equatable {
  final UserId id;
  final String name;
  final String? avatarUrl;
  final DateTime joinedAt;
  final bool isOnline;
  final BubbleMemberStatus status;
  final int unreadMessageCount;
  final String? color;
  final DateTime? leftAt;
  final LeaveReason? leaveReason;

  const BubbleMemberEntity({
    required this.id,
    required this.name,
    this.avatarUrl,
    required this.joinedAt,
    this.isOnline = false,
    this.status = BubbleMemberStatus.active,
    this.unreadMessageCount = 0,
    this.color,
    this.leftAt,
    this.leaveReason,
  });

  /// Create a member with validation
  static Result<BubbleMemberEntity> create({
    required String id,
    required String name,
    String? avatarUrl,
    required DateTime joinedAt,
    bool isOnline = false,
    BubbleMemberStatus status = BubbleMemberStatus.active,
    int unreadMessageCount = 0,
    String? color,
    DateTime? leftAt,
    LeaveReason? leaveReason,
  }) {
    // Validation logic with Result<T> pattern
  }

  // Business logic properties - UPDATED FOR BACKEND ALIGNMENT
  bool get isActive => status == BubbleMemberStatus.active;
  bool get hasLeft => status == BubbleMemberStatus.left || status == BubbleMemberStatus.removed;
  bool get wasKickedOut => status == BubbleMemberStatus.removed;
  bool get leftVoluntarily => status == BubbleMemberStatus.left && leaveReason == LeaveReason.voluntary;

  String? get leaveReasonDescription {
    switch (leaveReason) {
      case LeaveReason.voluntary:
        return 'Left voluntarily';
      case LeaveReason.kickedOut:
        return 'Removed by vote';
      case null:
        return null;
    }
  }
}
```

### Value Objects (Type Safety)

Core value objects ensuring type safety and validation:

```dart
// Bubble identification with validation
class BubbleId extends ValueObject<String> {
  const BubbleId._(String value) : super(value);
  
  static Result<BubbleId> create(String value) {
    if (value.trim().isEmpty) {
      return Result.failure(ValidationError(
        field: 'bubbleId',
        message: 'Bubble ID cannot be empty',
      ));
    }
    return Result.success(BubbleId._(value.trim()));
  }
}

// Bubble naming with validation
class BubbleName extends ValueObject<String> {
  static const int minLength = 1;
  static const int maxLength = 50;
  
  const BubbleName._(String value) : super(value);
  
  static Result<BubbleName> create(String value) {
    final trimmed = value.trim();
    if (trimmed.isEmpty) {
      return Result.failure(ValidationError(
        field: 'name',
        message: 'Bubble name cannot be empty',
      ));
    }
    if (trimmed.length > maxLength) {
      return Result.failure(ValidationError(
        field: 'name',
        message: 'Bubble name cannot exceed $maxLength characters',
      ));
    }
    return Result.success(BubbleName._(trimmed));
  }
}

// Member capacity management
class MemberCapacity extends ValueObject<int> {
  static const int minCapacity = 2;
  static const int maxCapacity = 5;
  
  const MemberCapacity._(int value) : super(value);
  
  static Result<MemberCapacity> create(int value) {
    if (value < minCapacity) {
      return Result.failure(ValidationError(
        field: 'capacity',
        message: 'Bubble capacity must be at least $minCapacity',
      ));
    }
    if (value > maxCapacity) {
      return Result.failure(ValidationError(
        field: 'capacity',
        message: 'Bubble capacity cannot exceed $maxCapacity',
      ));
    }
    return Result.success(MemberCapacity._(value));
  }
}
```

### Enums and Status Types

```dart
enum BubbleLifecycleStatus {
  creating,
  active,
  expired,
  archived,
  dissolved,
}

enum BubbleMemberStatus {
  active,     // User is an active member ('active')
  left,       // User voluntarily left ('left')
  removed,    // User was removed/kicked out ('removed')
  pending,    // User has pending request ('pending')
  accepted,   // Request was accepted - transitional state ('accepted')
  declined,   // Request was declined - transitional state ('declined')
}

enum LeaveReason {
  voluntary,
  kickedOut,
}
```

## Clean Architecture Implementation

### Repository Pattern

Clean separation between interfaces and implementations:

```dart
// Repository Interface (Domain Layer)
abstract class BubbleRepository {
  /// Get bubble by ID with comprehensive error handling  
  Future<Result<BubbleEntity>> getBubbleById(BubbleId bubbleId);

  /// Create a new bubble with validation
  Future<Result<BubbleEntity>> createBubble({
    required BubbleName name,
    required MemberCapacity capacity,
    required DateTime createdAt,
    DateTime? endDate,
    List<UserId> invitedMemberIds = const [],
  });

  /// Join an existing bubble
  Future<Result<BubbleEntity>> joinBubble({
    required BubbleId bubbleId,
    required UserId userId,
  });

  /// Leave a bubble
  Future<Result<void>> leaveBubble({
    required BubbleId bubbleId,
    required UserId userId,
  });

  /// Update member status (online/offline, unread count)
  Future<Result<BubbleEntity>> updateMemberStatus({
    required BubbleId bubbleId,
    required UserId memberId,
    bool? isOnline,
    int? unreadMessageCount,
  });
}
```

### Repository Implementation

Concrete implementation with proper error handling:

```dart
// Repository Implementation (Infrastructure Layer)
class BubbleRepositoryImpl implements BubbleRepository {
  // REST data source that talks to the Go micro-services backend
  final HopenApiDataSource _remoteDataSource;
  
  BubbleRepositoryImpl({
    required HopenApiDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;

  @override
  Future<Result<BubbleEntity>> getBubbleById(BubbleId bubbleId) async {
    try {
      final apiBubble = await _remoteDataSource.getBubble(bubbleId.value);
      return apiBubble.toDomain(); // Using clean architecture mapper
    } catch (e) {
      return Result.failure(UnexpectedError(message: 'Failed to get bubble: ${e.toString()}'));
    }
  }

  @override
  Future<Result<BubbleEntity>> createBubble({
    required BubbleName name,
    required MemberCapacity capacity,
    required DateTime createdAt,
    DateTime? endDate,
    List<UserId> invitedMemberIds = const [],
  }) async {
    try {
      final createData = {
        'name': name.value,
        'max_members': capacity.value,
        'created_at': createdAt.toIso8601String(),
        if (endDate != null) 'expires_at': endDate.toIso8601String(),
        'invited_members': invitedMemberIds.map((id) => id.value).toList(),
      };
      
      final apiBubble = await _remoteDataSource.createBubble(createData);
      return apiBubble.toDomain();
    } catch (e) {
      return Result.failure(UnexpectedError(message: 'Failed to create bubble: ${e.toString()}'));
    }
  }
}
```

### Clean Architecture Mappers

DTO-to-Entity conversion following ByteByteGo best practices:

```dart
// Mapper Extensions (Infrastructure Layer)
extension ApiBubbleMapper on ApiBubble {
  /// Convert API DTO to Domain Entity with validation
  Result<BubbleEntity> toDomain() {
    try {
      return BubbleEntity.create(
        id: id,
        name: name,
        capacity: maxMembers ?? 5,
        members: const [], // ApiBubble doesn't include members
        createdAt: createdAt ?? DateTime.now(),
        endDate: expiresAt,
        status: _mapLifecycleStatus(status),
      );
    } catch (e) {
      return Result.failure(ValidationError(
        field: 'bubble',
        message: 'Failed to map API bubble to domain: $e',
      ));
    }
  }

  BubbleLifecycleStatus _mapLifecycleStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'creating':
        return BubbleLifecycleStatus.creating;
      case 'active':
        return BubbleLifecycleStatus.active;
      case 'expired':
        return BubbleLifecycleStatus.expired;
      case 'archived':
        return BubbleLifecycleStatus.archived;
      case 'dissolved':
        return BubbleLifecycleStatus.dissolved;
      default:
        return BubbleLifecycleStatus.active;
    }
  }
}

extension BubbleEntityMapper on BubbleEntity {
  /// Convert Domain Entity to API DTO
  ApiBubble toDto() {
    return ApiBubble(
      id: id.value,
      name: name.value,
      maxMembers: capacity.value,
      currentMembers: activeMembersCount,
      status: _mapStatusToString(status),
      createdAt: createdAt,
      expiresAt: endDate,
    );
  }

  String _mapStatusToString(BubbleLifecycleStatus status) {
    switch (status) {
      // Note: 'creating' status removed - bubbles are created as 'active'
      case BubbleLifecycleStatus.active:
        return 'active';
      case BubbleLifecycleStatus.expired:
        return 'expired';
      case BubbleLifecycleStatus.archived:
        return 'archived';
      case BubbleLifecycleStatus.dissolved:
        return 'dissolved';
    }
  }
}
```

## State Management (BLoC Pattern)

### BubbleBloc

Enhanced bubble state management with proper error handling:

```dart
// Enhanced bubble states with better error handling and type safety
sealed class BubbleState extends Equatable {
  const BubbleState();
}

final class BubbleInitial extends BubbleState {
  const BubbleInitial();
}

final class BubbleLoading extends BubbleState {
  final String operation;
  final BubbleEntity? currentBubble;

  const BubbleLoading({
    required this.operation,
    this.currentBubble,
  });
}

final class BubbleLoaded extends BubbleState {
  final BubbleEntity bubble;

  const BubbleLoaded(this.bubble);

  // Convenience methods
  bool isMember(String userId) => bubble.isMember(userId);
  bool isActiveMember(String userId) => bubble.isActiveMember(userId);
  bool get hasActiveCall => bubble.hasActiveCall;
  BubbleMemberEntity? getMember(String userId) => bubble.getMember(userId);
}

final class BubbleError extends BubbleState {
  final AppError error;
  final BubbleEntity? previousBubble;
  final String? operation;

  const BubbleError({
    required this.error,
    this.previousBubble,
    this.operation,
  });

  String get userMessage => error.userMessage;
  String get technicalMessage => error.technicalMessage;
  bool get isRecoverable => previousBubble != null;
}
```

### BubbleBloc Implementation

```dart
class BubbleBloc extends Bloc<BubbleEvent, BubbleState> {
  final BubbleRepository _bubbleRepository;

  BubbleBloc({
    required BubbleRepository bubbleRepository,
  }) : _bubbleRepository = bubbleRepository,
       super(const BubbleInitial()) {
    
    on<LoadBubble>(_onLoadBubble);
    on<CreateBubble>(_onCreateBubble);
    on<JoinBubble>(_onJoinBubble);
    on<LeaveBubble>(_onLeaveBubble);
  }

  Future<void> _onLoadBubble(LoadBubble event, Emitter<BubbleState> emit) async {
    emit(BubbleLoading(operation: 'Loading bubble'));
    
    final result = await _bubbleRepository.getBubbleById(event.bubbleId);
    
    result.fold(
      onSuccess: (bubble) => emit(BubbleLoaded(bubble)),
      onFailure: (error) => emit(BubbleError(
        error: error,
        operation: 'Loading bubble',
      )),
    );
  }

  Future<void> _onCreateBubble(CreateBubble event, Emitter<BubbleState> emit) async {
    emit(BubbleLoading(operation: 'Creating bubble'));
    
    final result = await _bubbleRepository.createBubble(
      name: event.name,
      capacity: event.capacity,
      createdAt: DateTime.now(),
      endDate: event.endDate,
    );
    
    result.fold(
      onSuccess: (bubble) => emit(BubbleLoaded(bubble)),
      onFailure: (error) => emit(BubbleError(
        error: error,
        operation: 'Creating bubble',
      )),
    );
  }
}
```

## Business Logic & Use Cases

### Core Use Cases

1. **LoadUserBubbleUseCase**: Load user's current bubble with validation
2. **CreateBubbleUseCase**: Create new bubble with comprehensive validation
3. **JoinBubbleUseCase**: Join existing bubble with capacity and permission checks
4. **LeaveBubbleUseCase**: Leave bubble with proper cleanup
5. **InviteToBubbleUseCase**: Invite users with permission validation
6. **UpdateMemberStatusUseCase**: Update member online status and unread counts

### Auto-Dissolution Logic

Bubbles automatically dissolve when they have fewer than 2 active members:

```dart
// In BubbleEntity
bool get shouldDissolve => activeMembersCount < minActiveMembers && activeMembersCount > 0;

// Repository handles dissolution
if (bubble.shouldDissolve) {
  await _dissolveBubble(bubble.id);
  // Notify all members via MQTT
  await _notificationService.notifyBubbleDissolved(bubble);
}
```

### Validation System

Comprehensive business rule validation:

```dart
// Example: Join validation
Result<void> validateJoin(String userId) {
  if (isAtCapacity) {
    return Result.failure(BubbleCapacityExceededError(
      bubbleId: id.value,
      currentCapacity: capacity.value,
    ));
  }

  if (isMember(userId)) {
    return Result.failure(ValidationError(
      field: 'userId',
      message: 'User is already a member of this bubble',
    ));
  }

  if (status != BubbleLifecycleStatus.active && status != BubbleLifecycleStatus.creating) {
    return Result.failure(BubbleNotActiveError(
      bubbleId: id.value,
      currentStatus: status.toString(),
    ));
  }

  return Result.success(null);
}
```

## Error Handling

### Specific Error Types

```dart
// Bubble-specific errors
class BubbleNotFoundError extends AppError {
  final String bubbleId;
  
  BubbleNotFoundError({required this.bubbleId})
      : super(
          code: 'BUBBLE_NOT_FOUND',
          userMessage: 'Bubble not found',
          technicalMessage: 'Bubble with ID $bubbleId was not found',
        );
}

class BubbleCapacityExceededError extends AppError {
  final String bubbleId;
  final int currentCapacity;
  
  BubbleCapacityExceededError({
    required this.bubbleId,
    required this.currentCapacity,
  }) : super(
          code: 'BUBBLE_CAPACITY_EXCEEDED',
          userMessage: 'Bubble is full',
          technicalMessage: 'Bubble $bubbleId has reached capacity of $currentCapacity',
        );
}

class BubbleAccessDeniedError extends AppError {
  final String bubbleId;
  final String operation;
  
  BubbleAccessDeniedError({
    required this.bubbleId,
    required this.operation,
  }) : super(
          code: 'BUBBLE_ACCESS_DENIED',
          userMessage: 'Access denied',
          technicalMessage: 'Access denied for operation $operation on bubble $bubbleId',
        );
}
```

## Backend Integration (Go micro-services)

### Bubble Service REST API (Gin)

The Go `bubble` micro-service exposes REST endpoints consumed by the Flutter app:

```http
GET    /api/v1/bubbles/:id          # Fetch bubble details
POST   /api/v1/bubbles              # Create new bubble
POST   /api/v1/bubbles/:id/join     # Join existing bubble (+30-day extension)
POST   /api/v1/bubbles/:id/leave    # Leave bubble
GET    /api/v1/bubbles/:id/members  # List members
```

Example handler signature in Go:

```go
// POST /api/v1/bubbles
func (h *Handler) Create(c *gin.Context) {
    var req struct {
        Name       string `json:"name" binding:"required,min=1,max=50"`
        MaxMembers int    `json:"max_members" binding:"required,min=2,max=5"`
    }
    if err := c.ShouldBindJSON(&req); err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    bubble, err := h.service.CreateBubble(c.Request.Context(), req.Name, req.MaxMembers)
    if err != nil {
        c.AbortWithError(http.StatusInternalServerError, err)
        return
    }

    c.JSON(http.StatusCreated, bubble)
}
```

### Real-time Updates

MQTT integration for real-time bubble updates:

```dart
// MQTT topic structure (EMQX)

// Subscribe to bubble updates
await mqttService.subscribe('bubbles/${bubble.id.value}/+/+');
```

## Testing Strategy

### Unit Tests

```dart
// Domain entity tests
group('BubbleEntity Tests', () {
  test('should create valid bubble entity', () {
    final result = BubbleEntity.create(
      id: 'test-bubble-id',
      name: 'Test Bubble',
      capacity: 5,
      members: [],
      createdAt: DateTime.now(),
    );

    expect(result.isSuccess, isTrue);
    expect(result.data.id.value, equals('test-bubble-id'));
  });

  test('should fail validation with invalid name', () {
    final result = BubbleEntity.create(
      id: 'test-id',
      name: '', // Invalid empty name
      capacity: 5,
      members: [],
      createdAt: DateTime.now(),
    );

    expect(result.isFailure, isTrue);
    expect(result.error, isA<ValidationError>());
  });
});
```

### Integration Tests

```dart
// Repository tests
group('BubbleRepositoryImpl Tests', () {
  test('should get bubble by ID', () async {
    final bubbleId = BubbleId.create('test-bubble-id').data;
    final result = await repository.getBubbleById(bubbleId);

    expect(result.isSuccess, isTrue);
    expect(result.data.id, equals(bubbleId));
  });
});
```

### Widget Tests

```dart
// BLoC tests
group('BubbleBloc Tests', () {
  blocTest<BubbleBloc, BubbleState>(
    'emits [BubbleLoading, BubbleLoaded] when LoadBubble is added',
    build: () => BubbleBloc(bubbleRepository: mockRepository),
    act: (bloc) => bloc.add(LoadBubble(bubbleId: testBubbleId)),
    expect: () => [
      BubbleLoading(operation: 'Loading bubble'),
      BubbleLoaded(testBubble),
    ],
  );
});
```

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Load bubble details only when needed
2. **Caching**: Cache frequently accessed bubbles locally
3. **Pagination**: Paginate member lists for large bubbles
4. **Real-time Updates**: Use MQTT for efficient real-time synchronization
5. **Image Optimization**: Optimize member avatar loading

### Memory Management

- Use `const` constructors for immutable entities
- Implement proper disposal in BLoCs
- Cache management for bubble data
- Efficient list operations for members

This comprehensive bubble system provides a robust, scalable, and maintainable foundation for the core social interaction feature in Hopen, following clean architecture principles and modern Flutter development best practices.

## Backend Alignment (2025 refresh)

This document now aligns with `docs/backend/architecture_new.md`:

* **Lifecycle states** – `active → expired/dissolved → archived` (bubbles created directly as active).
* **Capacity rules** – bubbles hold **2–5** members. Attempts to exceed capacity are rejected by the backend and validated in `MemberCapacity.create`.
* **Expiry logic** – bubbles expire **90 days** after creation. Each new member extends expiry by **+30 days**, capped at the original 90-day window. Backend emits `events.bubble.expired` on NATS when archiving.
* **Friendship generation** – when a bubble expires, the `friendship` service auto-creates bidirectional friend-requests for every member pair. No manual friend requests exist.
* **Event flow** – critical bubble events published on `events.bubble.*` (JetStream stream `events`). Clients receive real-time updates via MQTT topics `bubble/{id}`.

```mermaid
sequenceDiagram
  participant Client
  participant BubbleSvc as Bubble Service
  participant NATS
  participant FriendshipSvc as Friendship Service
  Client->>BubbleSvc: Join bubble
  BubbleSvc->>BubbleSvc: extend expiry (+30 days, cap 90)
  Note over BubbleSvc: On timer / expiry checker
  BubbleSvc->>BubbleSvc: archive bubble
  BubbleSvc-->>NATS: publish events.bubble.expired
  FriendshipSvc-->>NATS: subscribed
  NATS-->>FriendshipSvc: events.bubble.expired
  FriendshipSvc->>FriendshipSvc: generate friend requests
```
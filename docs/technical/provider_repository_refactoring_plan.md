# Provider & Repository Layer Refactoring Plan

## 🎯 **Objective**
Align all provider and repository layers with the PostgreSQL backend schema, eliminate outdated models, and ensure proper four-layer architecture compliance.

## 🔍 **Critical Issues Identified**

### **1. Missing Backend Tables in Frontend**

#### **PostgreSQL Tables Missing:**
- ❌ **`media_files`** - No API model or repository
- ❌ **`call_sessions`** - No API model or repository
- ❌ **`bubble_requests`** - No API model or repository
- ❌ **`request_votes`** - No API model or repository
- ❌ **`user_relationships`** - No API model or repository
- ❌ **`roles`** and **`user_roles`** - No API models or repositories

#### **Cassandra Tables Missing:**
- ❌ **`messages`** - No API model for bubble chat messages
- ❌ **`conversations`** - No API model for direct message conversations
- ❌ **`conversation_messages`** - No API model for DM content
- ❌ **`user_conversations`** - No API model for user's conversation list
- ❌ **`bubble_message_counts`** - No API model for message counters
- ❌ **`conversation_unread_counts`** - No API model for unread counters

### **2. Outdated/Misaligned API Models**
- 🔄 **`ApiContact`** - Uses old contact system, should use `user_relationships`
- 🔄 **`ApiBubble`** - Missing `member_count`, `expires_at`, wrong field names
- 🔄 **`ApiUserProfile`** - Missing `notification_settings`, wrong field mappings
- 🔄 **`ApiChatMessage`** - May need alignment with backend message system

### **3. Missing ENUMs in Frontend**
- ❌ **`BubbleRequestType`** - ('invite', 'join', 'kick', 'start')
- ❌ **`RequestStatus`** - ('pending', 'approved', 'rejected', 'expired')  
- ❌ **`VoteType`** - ('approve', 'reject')
- ❌ **`FriendRequestStatus`** - ('pending', 'accepted', 'declined', 'expired')
- ❌ **`ContactRequestStatus`** - ('pending', 'accepted', 'declined', 'expired')
- ❌ **`NotificationType`** - All notification types from backend

### **4. Repository Layer Issues**
- 🔄 **Contact repositories** - Should use `user_relationships` instead of old contact system
- ❌ **Missing repositories** - For media, calls, requests, votes, roles
- 🔄 **Bubble repositories** - Need alignment with new request system

## 🔧 **Refactoring Actions**

### **Phase 1: Create Missing ENUMs**

#### **1.1 Add Backend ENUMs to Domain Models**
```dart
// Add to bubble_entity.dart or create separate enums file
enum BubbleRequestType {
  invite,  // 'invite'
  join,    // 'join'
  kick,    // 'kick'
  start,   // 'start'
}

enum RequestStatus {
  pending,   // 'pending'
  approved,  // 'approved'
  rejected,  // 'rejected'
  expired,   // 'expired'
}

enum VoteType {
  approve,  // 'approve'
  reject,   // 'reject'
}

enum FriendRequestStatus {
  pending,   // 'pending'
  accepted,  // 'accepted'
  declined,  // 'declined'
  expired,   // 'expired'
}

enum ContactRequestStatus {
  pending,   // 'pending'
  accepted,  // 'accepted'
  declined,  // 'declined'
  expired,   // 'expired'
}

enum NotificationType {
  contactRequestReceived,
  contactRequestAccepted,
  contactRequestDeclined,
  friendRequestReceived,
  friendRequestAccepted,
  friendRequestDeclined,
  bubbleInviteReceived,
  bubbleInviteAccepted,
  bubbleInviteDeclined,
  bubbleJoinRequestReceived,
  bubbleJoinRequestAccepted,
  bubbleJoinRequestDeclined,
  bubbleKickoutRequestReceived,
  bubbleKickoutRequestAccepted,
  bubbleKickoutRequestDeclined,
  bubbleStartRequestReceived,
  bubbleStartRequestAccepted,
  bubbleStartRequestDeclined,
  bubbleExpired,
  bubbleDissolved,
  bubbleCallStarted,
  bubbleCallEnded,
  bubbleCallMissed,
  systemNotification,
}
```

### **Phase 2: Create Missing API Models**

#### **2.1 Media Files API Model**
```dart
@JsonSerializable()
class ApiMediaFile {
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  final String filename;
  @JsonKey(name: 'original_filename')
  final String originalFilename;
  @JsonKey(name: 'content_type')
  final String contentType;
  @JsonKey(name: 'file_size')
  final int fileSize;
  @JsonKey(name: 'bucket_name')
  final String bucketName;
  @JsonKey(name: 'object_key')
  final String objectKey;
  final Map<String, dynamic>? metadata;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
}
```

#### **2.2 Call Sessions API Model**
```dart
@JsonSerializable()
class ApiCallSession {
  final String id;
  @JsonKey(name: 'bubble_id')
  final String? bubbleId;
  @JsonKey(name: 'initiator_id')
  final String initiatorId;
  final List<String> participants;
  @JsonKey(name: 'call_type')
  final String callType;
  @JsonKey(name: 'started_at')
  final DateTime startedAt;
  @JsonKey(name: 'ended_at')
  final DateTime? endedAt;
  @JsonKey(name: 'duration_seconds')
  final int durationSeconds;
  final Map<String, dynamic>? metadata;
}
```

#### **2.3 Bubble Requests API Model**
```dart
@JsonSerializable()
class ApiBubbleRequest {
  final String id;
  @JsonKey(name: 'request_type')
  final String requestType; // BubbleRequestType
  @JsonKey(name: 'bubble_id')
  final String? bubbleId;
  @JsonKey(name: 'requester_id')
  final String requesterId;
  @JsonKey(name: 'target_user_id')
  final String? targetUserId;
  final String status; // RequestStatus
  @JsonKey(name: 'requires_unanimous')
  final bool requiresUnanimous;
  @JsonKey(name: 'expires_at')
  final DateTime expiresAt;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  @JsonKey(name: 'completed_at')
  final DateTime? completedAt;
}
```

#### **2.4 Request Votes API Model**
```dart
@JsonSerializable()
class ApiRequestVote {
  final String id;
  @JsonKey(name: 'request_id')
  final String requestId;
  @JsonKey(name: 'voter_id')
  final String voterId;
  final String vote; // VoteType
  @JsonKey(name: 'voted_at')
  final DateTime votedAt;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
}
```

#### **2.5 User Relationships API Model**
```dart
@JsonSerializable()
class ApiUserRelationship {
  final String id;
  @JsonKey(name: 'from_user_id')
  final String fromUserId;
  @JsonKey(name: 'to_user_id')
  final String toUserId;
  @JsonKey(name: 'relationship_type')
  final String relationshipType; // 'contact', 'friend', 'block', etc.
  final String status; // 'active', 'inactive', 'expired'
  @JsonKey(name: 'created_by')
  final String? createdBy;
  final String? reason;
  final Map<String, dynamic>? metadata;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  @JsonKey(name: 'expires_at')
  final DateTime? expiresAt;
}
```

### **Phase 3: Update Existing API Models**

#### **3.1 Fix ApiBubble**
```dart
@JsonSerializable()
class ApiBubble {
  final String id;
  @JsonKey(name: 'creator_id')
  final String? creatorId;
  final String name;
  final int capacity;
  @JsonKey(name: 'member_count')
  final int memberCount;
  final String status; // BubbleLifecycleStatus
  @JsonKey(name: 'friend_request_on_expire')
  final bool friendRequestOnExpire;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  @JsonKey(name: 'expires_at')
  final DateTime? expiresAt;
}
```

#### **3.2 Fix ApiUserProfile**
```dart
@JsonSerializable()
class ApiUserProfile {
  final String id;
  final String username;
  final String email;
  @JsonKey(name: 'first_name')
  final String firstName;
  @JsonKey(name: 'last_name')
  final String lastName;
  final DateTime? birthday;
  @JsonKey(name: 'avatar_url')
  final String? avatarUrl;
  @JsonKey(name: 'is_private')
  final bool isPrivate;
  @JsonKey(name: 'notification_settings')
  final Map<String, dynamic> notificationSettings;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
}
```

### **Phase 4: Create Missing Repositories**

#### **4.1 Media Repository**
```dart
abstract class MediaRepository {
  Future<Result<String>> uploadMedia(File file, String contentType);
  Future<Result<String>> getSignedUrl(String objectKey);
  Future<Result<void>> deleteMedia(String mediaId);
}
```

#### **4.2 Call Repository**
```dart
abstract class CallRepository {
  Future<Result<String>> startCall(String bubbleId, String callType);
  Future<Result<void>> endCall(String callId);
  Future<Result<List<ApiCallSession>>> getCallHistory(String userId);
}
```

#### **4.3 Request Repository**
```dart
abstract class RequestRepository {
  Future<Result<String>> createBubbleRequest(BubbleRequestType type, String bubbleId, String? targetUserId);
  Future<Result<void>> voteOnRequest(String requestId, VoteType vote);
  Future<Result<List<ApiBubbleRequest>>> getPendingRequests(String userId);
}
```

#### **4.4 User Relationship Repository**
```dart
abstract class UserRelationshipRepository {
  Future<Result<void>> createRelationship(String toUserId, String relationshipType);
  Future<Result<void>> updateRelationship(String relationshipId, String newType);
  Future<Result<List<ApiUserRelationship>>> getRelationships(String userId);
  Future<Result<void>> blockUser(String userId);
}
```

## 🎯 **Implementation Priority**

### **High Priority (Critical for Core Functionality)**
1. ✅ **ENUMs** - Add all missing backend ENUMs
2. ✅ **ApiBubble** - Fix to match backend schema
3. ✅ **ApiUserProfile** - Fix to match backend schema
4. ✅ **User Relationships** - Replace old contact system

### **Medium Priority (Important for Features)**
5. ✅ **Bubble Requests** - Core request system
6. ✅ **Request Votes** - Voting functionality
7. ✅ **Media Files** - File upload/download

### **Low Priority (Nice to Have)**
8. ✅ **Call Sessions** - Call history
9. ✅ **Roles** - User role management

## 📋 **Validation Steps**
1. ✅ All backend tables have corresponding API models
2. ✅ All backend ENUMs have corresponding frontend ENUMs  
3. ✅ All API models match backend field names exactly
4. ✅ All repositories follow clean architecture principles
5. ✅ No duplicate or outdated models remain
6. ✅ Flutter app compiles without errors
7. ✅ Backend-frontend communication works correctly

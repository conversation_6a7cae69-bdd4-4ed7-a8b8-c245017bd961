# 🏁 Hopen Backend — Production Environment

This document captures the definitive configuration of the **production** backend that powers `hopenapp.com`.

> **Never commit real credentials here** — only variable names and architectural notes.

---

## 1. Core Domains

| Function | FQDN | Protocols |
|----------|------|-----------|
| API Gateway | `api.hopenapp.com` | HTTP/3 (UDP 443), HTTP/2 (TLS 443) |
| WebSocket (signaling) | `ws.hopenapp.com` | `wss` on 443 |
| OAuth2/OIDC | `oauth.hopenapp.com` | 443 |
| Identity (Kratos) | `auth.hopenapp.com` | 443 |
| Object Storage | `storage.hopenapp.com` | S3-compatible, HTTPS |
| MQTT 5 Broker | `mqtt.hopenapp.com` | TLS 8883, WS 443 `/mqtt` |
| TURN / STUN | `turn.hopenapp.com` | 3478 UDP/TCP, 443 TLS (`turns:`) |

DNS is managed via **OVHcloud**; A & AAAA records point to the public load balancer.

---

## 2. Infrastructure Overview

```mermaid
flowchart TD
  LB(OVH Load Balancer) --443/80--> Gateway(Encore API Gateway)
  Gateway --GRPC--> Services[Encore Micro-services]
  Services --> DB[(PostgreSQL HA)]
  Services --> Cache[(Valkey Redis)]
  Services --> S3[(MinIO Object Storage)]
  MQTT(mqtt.hopenapp.com) --> Services
  TURN(turn.hopenapp.com) --> WebRTCClients
```

* K8s cluster: OVH Managed Kubernetes (3× d2-8 nodes, autoscale 3-10).  
* Persistent storage: Cinder-backed PVCs (Postgres, MinIO).  
* Ingress: NGINX-HTTP/3 with QUIC + automatic fallback.

---

## 3. Security & Compliance

| Area | Mechanism |
|------|-----------|
| **TLS** | Let's Encrypt wildcard certs, auto-renew cron via cert-manager. |
| **Certificate Pinning** | Mobile apps verify SHA-256 SPKI hashes (update on every cert rotation). |
| **JWT** | 24 h access tokens, 7 d refresh, rotated, stored in Valkey. |
| **Rate-Limiting** | Token-bucket in Valkey: 1000 req/min/IP, 200 req/min/user. |
| **DDoS** | OVHcloud network-layer protection + HAProxy rate limiting. |
| **Backups** | pgBackRest hourly (24 h), daily (30 d); MinIO object-lifecycle to cold tier. |
| **Image Scanning** | GitHub Action (Trivy) blocks on Critical vulns. |
| **Secrets** | OVH Vault → sealed-secrets / Kubernetes secrets. |

---

## 4. CI/CD Pipeline Highlights

1. Push `main` → GitHub Actions `backend-ci-cd.yml` runs tests, builds multi-arch image, scans with Trivy, pushes to `ghcr.io/hopen/backend`.
2. ArgoCD watches `main` Helm chart; performs canary 10 % traffic for 15 min → full rollout.
3. Post-deploy smoke tests (`/healthz`, login flow, WebRTC call) executed via k6.

---

## 5. SLO Metrics

| Metric | Target | Alert |
|--------|--------|-------|
| HTTP p95 latency | < 200 ms | > 400 ms 5 min |
| WebRTC call setup success | ≥ 98 % | < 97 % 10 min |
| MQTT connect success | ≥ 99.5 % | < 99 % 10 min |
| 5xx error rate | < 0.1 % | > 0.3 % 5 min |

Grafana dashboards live at `grafana.hopenapp.com` (SSO).

---

## 6. Disaster-Recovery Playbook (Summary)

1. Major outage detection via PagerDuty.  
2. T-0: Fail traffic to standby LB region.  
3. Restore most-recent pgBackRest base + WAL to new cluster.  
4. Run `scripts/db-rollback.sh prod <timestamp>` if point-in-time rollback needed.  
5. Verify smoke tests, then re-enable production traffic.

---

## 7. Contact Matrix

| Role | Name | Slack | Phone |
|------|------|-------|-------|
| SRE On-call | Rotating | `#hopen-sre` | +33 6 xx xx xx xx |
| Database | Alice | `@alice` | +33 6 xx xx xx yy |
| Security | Bob | `@bob` | +33 6 xx yy yy yy |

---

© 2025 Hopen Inc.  **CONFIDENTIAL** — production operations guide. 
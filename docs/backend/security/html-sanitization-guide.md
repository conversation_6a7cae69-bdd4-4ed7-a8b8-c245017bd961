# HTML Sanitization Guide

## Overview

This guide explains how to properly sanitize HTML content in the Hopen backend using the `bluemonday` library through our utility package.

## Why HTML Sanitization is Critical

- **XSS Prevention**: User-generated HTML can contain malicious scripts
- **Data Integrity**: Ensures only safe HTML reaches the client
- **Compliance**: Meets security standards and best practices

## Available Sanitization Levels

### 1. Strict Sanitization (`SanitizeStrict`)
Removes ALL HTML tags and attributes, leaving only plain text.

**Use for:**
- Usernames
- Passwords
- Email addresses
- Any field that should never contain HTML

```go
import "hopenbackend/pkg/utils"

// Example: Sanitizing a username
username := utils.SanitizeStrict(userInput)
```

### 2. Relaxed Sanitization (`SanitizeRelaxed`)
Allows safe HTML tags and attributes for user-generated content.

**Use for:**
- Chat messages
- Comments
- User profiles
- Any content that may contain basic formatting

```go
// Example: Sanitizing a chat message
message := utils.SanitizeRelaxed(userInput)
```

### 3. Text-Only Conversion (`SanitizeTextOnly`)
Converts HTML to plain text while preserving structure.

**Use for:**
- Search indexing
- Plain text exports
- Preview generation

```go
// Example: Creating a plain text preview
preview := utils.SanitizeTextOnly(htmlContent)
```

## Implementation Examples

### Chat Message Processing

```go
type ChatMessage struct {
    ID      string `json:"id"`
    UserID  string `json:"user_id"`
    Content string `json:"content"`
    Time    time.Time `json:"time"`
}

func (s *ChatService) SendMessage(ctx context.Context, userID, content string) (*ChatMessage, error) {
    // Sanitize the message content before storing
    sanitizedContent := utils.SanitizeRelaxed(content)
    
    message := &ChatMessage{
        ID:      uuid.New().String(),
        UserID:  userID,
        Content: sanitizedContent, // Safe to store and display
        Time:    time.Now(),
    }
    
    // Store in database...
    return message, nil
}
```

### User Profile Updates

```go
type UserProfile struct {
    ID          string `json:"id"`
    DisplayName string `json:"display_name"`
    Bio         string `json:"bio"`
    Location    string `json:"location"`
}

func (s *UserService) UpdateProfile(ctx context.Context, userID string, profile *UserProfile) error {
    // Strict sanitization for display name (no HTML allowed)
    profile.DisplayName = utils.SanitizeStrict(profile.DisplayName)
    
    // Relaxed sanitization for bio (allows basic formatting)
    profile.Bio = utils.SanitizeRelaxed(profile.Bio)
    
    // Strict sanitization for location (no HTML allowed)
    profile.Location = utils.SanitizeStrict(profile.Location)
    
    // Update in database...
    return nil
}
```

### Search Indexing

```go
func (s *SearchService) IndexContent(content string) string {
    // Convert HTML to plain text for search indexing
    return utils.SanitizeTextOnly(content)
}
```

## Security Best Practices

### 1. Always Sanitize User Input
```go
// ❌ DON'T: Trust user input
func badExample(userInput string) {
    // This is dangerous!
    response := gin.H{"content": userInput}
}

// ✅ DO: Always sanitize
func goodExample(userInput string) {
    sanitized := utils.SanitizeRelaxed(userInput)
    response := gin.H{"content": sanitized}
}
```

### 2. Choose the Right Sanitization Level
```go
// For usernames - strict sanitization
username := utils.SanitizeStrict(userInput)

// For chat messages - relaxed sanitization
message := utils.SanitizeRelaxed(userInput)

// For search - text only
searchText := utils.SanitizeTextOnly(userInput)
```

### 3. Sanitize Before Database Storage
```go
// Always sanitize before storing in database
func StoreUserContent(content string) error {
    sanitizedContent := utils.SanitizeRelaxed(content)
    
    // Store sanitized content
    return db.Store(sanitizedContent)
}
```

### 4. Sanitize Before API Responses
```go
// Sanitize before sending to client
func GetUserProfile(userID string) (*UserProfile, error) {
    profile, err := db.GetProfile(userID)
    if err != nil {
        return nil, err
    }
    
    // Ensure content is safe before sending to client
    profile.Bio = utils.SanitizeRelaxed(profile.Bio)
    profile.DisplayName = utils.SanitizeStrict(profile.DisplayName)
    
    return profile, nil
}
```

## Migration from Old Pattern Matching

### Before (Ineffective)
```go
// ❌ Old approach - easily bypassed
func oldSanitize(input string) string {
    patterns := []string{"<script", "javascript:", "onload="}
    for _, pattern := range patterns {
        input = strings.ReplaceAll(input, pattern, "")
    }
    return input
}
```

### After (Secure)
```go
// ✅ New approach - uses proven library
func newSanitize(input string) string {
    return utils.SanitizeRelaxed(input)
}
```

## Testing HTML Sanitization

```go
func TestHTMLSanitization(t *testing.T) {
    tests := []struct {
        name     string
        input    string
        expected string
        sanitizer func(string) string
    }{
        {
            name:     "Remove script tags",
            input:    "<script>alert('xss')</script>Hello",
            expected: "Hello",
            sanitizer: utils.SanitizeStrict,
        },
        {
            name:     "Allow safe HTML",
            input:    "<strong>Bold</strong> and <em>italic</em>",
            expected: "<strong>Bold</strong> and <em>italic</em>",
            sanitizer: utils.SanitizeRelaxed,
        },
        {
            name:     "Remove unsafe attributes",
            input:    "<div onclick=\"alert('xss')\">Click me</div>",
            expected: "<div>Click me</div>",
            sanitizer: utils.SanitizeRelaxed,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := tt.sanitizer(tt.input)
            if result != tt.expected {
                t.Errorf("got %q, want %q", result, tt.expected)
            }
        })
    }
}
```

## Integration with Existing Services

When updating existing services to use HTML sanitization:

1. **Identify user input fields** that may contain HTML
2. **Choose appropriate sanitization level** for each field
3. **Apply sanitization** before database storage
4. **Apply sanitization** before API responses
5. **Update tests** to verify sanitization behavior

## Monitoring and Metrics

The security middleware includes metrics for monitoring:
- `hopen_security_blocked_requests_total`
- `hopen_security_violations_total`
- `hopen_security_processing_duration_seconds`

Monitor these metrics to ensure the security layer is working correctly.

## Infrastructure Security

Remember that HTML sanitization is just one layer of security:

1. **WAF (Web Application Firewall)**: Use AWS WAF, Cloudflare, or NGINX with ModSecurity
2. **HTTPS**: Always use TLS/SSL
3. **Input Validation**: Validate data types and formats
4. **Output Encoding**: Encode output appropriately for the context
5. **Content Security Policy**: Use CSP headers (already implemented in security middleware)

## Conclusion

HTML sanitization is essential for preventing XSS attacks. Use the appropriate sanitization level for each use case and always sanitize user input before processing or storing it. 
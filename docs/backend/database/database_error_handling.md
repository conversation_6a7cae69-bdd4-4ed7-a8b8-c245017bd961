# Structured Error Handling for Database Operations

This package provides a comprehensive structured error handling system for database operations following Go best practices and RFC 7807 Problem Details for HTTP APIs.

## Overview

The structured error handling system addresses the following problems:
- Generic `fmt.Errorf` errors make it difficult for API handlers to return specific HTTP status codes
- Inconsistent error handling across different DAL methods
- Poor error messages for end users
- Difficulty in distinguishing between different types of database errors

## Key Features

### 1. Behavior-Based Error Checking (Go Best Practice)

Instead of using sentinel errors (which create tight coupling), we use interface-based error checking:

```go
// Check error types using behavior
if IsNotFound(err) {
    // Handle not found error
}

if IsDuplicate(err) {
    // Handle duplicate record error
}

if IsBubbleLimit(err) {
    // Handle bubble limit exceeded error
}
```

### 2. RFC 7807 Problem Details Compliance

HTTP responses follow the RFC 7807 standard for consistent API error responses:

```json
{
  "type": "https://example.com/probs/user-not-found",
  "title": "User Not Found",
  "status": 404,
  "detail": "The requested user could not be found"
}
```

### 3. Automatic PostgreSQL Error Translation

The system automatically translates PostgreSQL error codes to meaningful business errors:

- `23505` (Unique Violation) → `DuplicateError`
- `23503` (Foreign Key Violation) → `ConstraintError`
- `pgx.ErrNoRows` → `NotFoundError`
- Custom trigger errors → `BubbleLimitError`

## Usage

### 1. In DAL Methods

Replace generic error handling:

```go
// OLD: Generic error handling
if err != nil {
    return fmt.Errorf("failed to create user: %w", err)
}

// NEW: Structured error handling
if err != nil {
    return fmt.Errorf("failed to create user: %w", HandlePgxError(err, "users"))
}
```

### 2. In Service Handlers

Use specialized error handlers for different domains:

```go
// User operations
func (s *UserService) CreateUser(c *gin.Context) {
    err := s.userRepo.CreateUser(ctx, user)
    if err != nil {
        HandleUserError(c, err, "create user")
        return
    }
    // Success response...
}

// Bubble operations
func (s *BubbleService) CreateBubble(c *gin.Context) {
    err := s.bubbleRepo.CreateBubble(ctx, bubble)
    if err != nil {
        HandleBubbleError(c, err, "create bubble")
        return
    }
    // Success response...
}
```

### 3. Error Type Checking

Check for specific error types in business logic:

```go
user, err := userRepo.GetUserByID(ctx, userID)
if err != nil {
    if IsNotFound(err) {
        // User doesn't exist, create a new one
        return createNewUser(ctx, userID)
    }
    return err // Other error types
}
```

## Error Types and HTTP Status Codes

| Error Type | HTTP Status | Example Use Case |
|------------|-------------|------------------|
| `NotFoundError` | 404 Not Found | User, bubble, or resource not found |
| `DuplicateError` | 409 Conflict | Email or username already exists |
| `BubbleLimitError` | 403 Forbidden | User reached bubble limit |
| `ConstraintError` | 400 Bad Request | Invalid data, constraint violations |
| Generic Database Error | 500 Internal Server Error | Unexpected database errors |

## PostgreSQL Error Code Mapping

| PostgreSQL Code | Error Type | Description |
|-----------------|------------|-------------|
| `23505` | `DuplicateError` | Unique constraint violation |
| `23503` | `ConstraintError` | Foreign key constraint violation |
| `23514` | `ConstraintError` | Check constraint violation |
| `23502` | `ConstraintError` | Not null constraint violation |
| `P0001` | `BubbleLimitError` | Custom trigger: bubble limit exceeded |

## Best Practices

### 1. Always Use HandlePgxError

```go
// In all DAL methods
if err != nil {
    return fmt.Errorf("operation context: %w", HandlePgxError(err, "table_name"))
}
```

### 2. Use Domain-Specific Error Handlers

```go
// In HTTP handlers
HandleUserError(c, err, "operation")     // For user operations
HandleBubbleError(c, err, "operation")   // For bubble operations
HandleHTTPError(c, err, "operation")     // For generic operations
```

### 3. Provide Context in Error Messages

```go
// Good: Provides context
return fmt.Errorf("failed to create user: %w", HandlePgxError(err, "users"))

// Bad: No context
return HandlePgxError(err, "users")
```

### 4. Handle Errors Once

```go
// Good: Handle error once at the boundary
func (s *Service) CreateUser(c *gin.Context) {
    err := s.repo.CreateUser(ctx, user)
    if err != nil {
        HandleUserError(c, err, "create user") // Handle once
        return
    }
}

// Bad: Log and return (handles twice)
func (r *Repository) CreateUser(ctx context.Context, user *User) error {
    _, err := r.pool.Exec(ctx, query, args...)
    if err != nil {
        r.logger.Error("Failed to create user", zap.Error(err)) // Log
        return HandlePgxError(err, "users") // And return
    }
}
```

## Migration Guide

### Step 1: Update DAL Methods

Replace all `fmt.Errorf` calls with `HandlePgxError`:

```go
// Before
if err != nil {
    return fmt.Errorf("failed to get user: %w", err)
}

// After
if err != nil {
    return fmt.Errorf("failed to get user: %w", HandlePgxError(err, "users"))
}
```

### Step 2: Update Service Handlers

Replace manual error handling with structured handlers:

```go
// Before
if err != nil {
    if err == pgx.ErrNoRows {
        c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
        return
    }
    c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal error"})
    return
}

// After
if err != nil {
    HandleUserError(c, err, "get user")
    return
}
```

### Step 3: Update Error Checking

Replace string-based error checking with behavior-based checking:

```go
// Before
if strings.Contains(err.Error(), "not found") {
    // Handle not found
}

// After
if IsNotFound(err) {
    // Handle not found
}
```

## Testing

The structured error system makes testing easier:

```go
func TestCreateUser_DuplicateEmail(t *testing.T) {
    err := userRepo.CreateUser(ctx, userWithDuplicateEmail)
    
    assert.True(t, IsDuplicate(err))
    assert.Contains(t, err.Error(), "email already exists")
}
```

## Examples

See `example_dal.go` and `example_service.go` for complete implementation examples showing:
- Proper DAL method error handling
- HTTP service handler implementation
- RFC 7807 compliant error responses
- Behavior-based error checking

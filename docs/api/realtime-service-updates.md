# Realtime Service API Updates

## Critical Changes for Cassandra Optimization

### Overview
The realtime service has been updated to use optimized Cassandra operations that require full primary key components. This eliminates expensive read-before-write operations and dramatically improves performance.

### Breaking Changes

#### 1. Edit Message API
**Endpoint:** `PUT /api/realtime/bubbles/{bubbleId}/messages/{messageId}/edit`

**Previous Request Body:**
```json
{
  "content": "Updated message content"
}
```

**New Request Body (REQUIRED):**
```json
{
  "content": "Updated message content",
  "created_at": "2024-01-15T10:30:00Z"
}
```

**Client Requirements:**
- Flutter client MUST store the `created_at` timestamp when receiving messages
- When editing a message, client MUST send the original `created_at` timestamp
- The `created_at` field is now **required** for all edit operations

#### 2. Delete Message API
**Endpoint:** `DELETE /api/realtime/bubbles/{bubbleId}/messages/{messageId}`

**Previous Request:** No body required

**New Request Body (REQUIRED):**
```json
{
  "created_at": "2024-01-15T10:30:00Z"
}
```

**Client Requirements:**
- Flutter client MUST send the original `created_at` timestamp in request body
- The `created_at` field is now **required** for all delete operations

#### 3. Mark Message as Read API
**Endpoint:** `PUT /api/realtime/conversations/{conversationId}/messages/{messageId}/read`

**Previous Request:** No body required

**New Request Body (REQUIRED):**
```json
{
  "created_at": "2024-01-15T10:30:00Z"
}
```

**Client Requirements:**
- Flutter client MUST send the original `created_at` timestamp in request body
- The `created_at` field is now **required** for mark-as-read operations

### Performance Benefits

#### Before Optimization:
```
1. Client sends edit request with messageId
2. Server performs SELECT to find created_at timestamp
3. Server performs UPDATE with full primary key
Total: 2 database operations
```

#### After Optimization:
```
1. Client sends edit request with messageId + created_at
2. Server performs UPDATE directly with full primary key
Total: 1 database operation (50% reduction)
```

### Client Implementation Guide

#### Flutter State Management
```dart
class Message {
  final String messageId;
  final String bubbleId;
  final String senderId;
  final String content;
  final DateTime createdAt; // CRITICAL: Store this timestamp
  final DateTime updatedAt;
  final bool isEdited;
  final bool isDeleted;
  
  // Constructor and other methods...
}
```

#### Edit Message Implementation
```dart
Future<void> editMessage(Message message, String newContent) async {
  final response = await http.put(
    Uri.parse('/api/realtime/bubbles/${message.bubbleId}/messages/${message.messageId}/edit'),
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode({
      'content': newContent,
      'created_at': message.createdAt.toIso8601String(), // REQUIRED
    }),
  );
  
  if (response.statusCode != 200) {
    throw Exception('Failed to edit message');
  }
}
```

#### Delete Message Implementation
```dart
Future<void> deleteMessage(Message message) async {
  final response = await http.delete(
    Uri.parse('/api/realtime/bubbles/${message.bubbleId}/messages/${message.messageId}'),
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode({
      'created_at': message.createdAt.toIso8601String(), // REQUIRED
    }),
  );
  
  if (response.statusCode != 200) {
    throw Exception('Failed to delete message');
  }
}
```

### Error Handling

#### New Error Responses
```json
{
  "error": "created_at timestamp is required"
}
```

```json
{
  "error": "Invalid bubble ID format"
}
```

```json
{
  "error": "Invalid message ID format"
}
```

### Migration Strategy

#### Phase 1: Update Client
1. Update Flutter app to store `created_at` timestamps
2. Update edit/delete functions to send `created_at`
3. Test with development environment

#### Phase 2: Deploy Backend
1. Deploy updated realtime service
2. Monitor for errors and performance improvements
3. Verify 50% reduction in database operations

#### Phase 3: Validate Performance
1. Monitor Cassandra query performance
2. Verify elimination of read-before-write operations
3. Measure latency improvements

### Backward Compatibility
⚠️ **Breaking Change**: These updates are **NOT backward compatible**. 

The old API endpoints that don't require `created_at` will return `400 Bad Request` errors. All clients must be updated before deploying the new backend version.

### Testing Checklist
- [ ] Client stores `created_at` timestamps for all messages
- [ ] Edit message sends `created_at` in request body
- [ ] Delete message sends `created_at` in request body
- [ ] Mark as read sends `created_at` in request body
- [ ] Error handling for missing `created_at` field
- [ ] Error handling for invalid UUID formats
- [ ] Performance monitoring shows reduced database operations

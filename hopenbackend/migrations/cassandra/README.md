# Cassandra Migrations

## Overview

This directory contains Cassandra schema migrations for the Hopen chat system. Currently, the schema is hardcoded in the Go application (`pkg/database/cassandra.go`), but this directory provides the foundation for a proper migration system.

## Current State

- **Schema Location**: Currently hardcoded in `InitializeKeyspace()` function
- **Migration Files**: Available in this directory for reference and future migration system
- **Tool**: No migration tool currently implemented

## Recommended Migration Tool

Use **cql-migrate** for Cassandra migrations:

```bash
# Install cql-migrate
go install github.com/mattes/migrate/v4/cmd/migrate@latest

# Or use the dedicated Cassandra migration tool
# https://github.com/hailocab/go-cassandra-migrate
```

## Migration Files

### 001_initial_schema.cql
Contains the complete initial schema including:
- Keyspace creation with NetworkTopologyStrategy
- Messages table for bubble messages
- Conversations table for direct messages
- Conversation messages table
- User conversations table for user's conversation list
- Counter tables for efficient counting

## Future Implementation

To implement proper migrations:

1. **Install Migration Tool**:
   ```bash
   go get github.com/hailocab/go-cassandra-migrate
   ```

2. **Refactor InitializeKeyspace()**:
   Replace the hardcoded schema with migration runner:
   ```go
   func (c *CassandraClient) InitializeKeyspace(ctx context.Context) error {
       migrator := migrate.NewMigrator(c.Session, "migrations/cassandra")
       return migrator.Up()
   }
   ```

3. **Benefits**:
   - Version-controlled schema changes
   - Easy rollbacks
   - Deployment flexibility
   - Schema history tracking

## Schema Design Principles

The current schema follows Cassandra best practices:

- **Denormalization**: Multiple tables serve different query patterns
- **Counter Tables**: Efficient counting without full table scans
- **Clustering Order**: Optimized for time-series queries
- **NetworkTopologyStrategy**: Production-ready replication
- **TTL Support**: Automatic cleanup for soft deletes

## Notes

- The current implementation in `cassandra.go` is functional but not ideal for production
- Migration files are provided for reference and future implementation
- Schema changes currently require application redeployment

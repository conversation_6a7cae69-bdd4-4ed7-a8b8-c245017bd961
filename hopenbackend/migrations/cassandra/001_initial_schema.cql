-- Initial Cassandra schema for Hopen chat system
-- This migration creates the keyspace and all initial tables

-- Create keyspace with NetworkTopologyStrategy for production
-- CRITICAL: Use NetworkTopologyStrategy for production multi-datacenter deployments
CREATE KEYSPACE IF NOT EXISTS hopen
WITH REPLICATION = {
    'class': 'NetworkTopologyStrategy',
    'datacenter1': 3
};

USE hopen;

-- Create messages table for bubble messages
CREATE TABLE IF NOT EXISTS messages (
    bubble_id UUID,
    message_id UUID,
    sender_id UUID,
    content TEXT,
    message_type TEXT,
    media_url TEXT,
    reply_to_id UUID,
    is_edited BOOLEAN,
    is_deleted BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    PRIMARY KEY (bubble_id, created_at, message_id)
) WITH CLUSTERING ORDER BY (created_at DESC);

-- Create conversations table for direct messages
CREATE TABLE IF NOT EXISTS conversations (
    conversation_id UUID,
    participant1_id UUID,
    participant2_id UUID,
    last_message_id UUID,
    last_message_at TIMESTAMP,
    created_at TIMESTAMP,
    PRIMARY KEY (conversation_id)
);

-- Create conversation_messages table for direct messages
CREATE TABLE IF NOT EXISTS conversation_messages (
    conversation_id UUID,
    message_id UUID,
    sender_id UUID,
    recipient_id UUID,
    content TEXT,
    message_type TEXT,
    media_url TEXT,
    reply_to_id UUID,
    is_deleted BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    PRIMARY KEY (conversation_id, created_at, message_id)
) WITH CLUSTERING ORDER BY (created_at DESC);

-- Create user_conversations table for user's conversation list
CREATE TABLE IF NOT EXISTS user_conversations (
    user_id UUID,
    conversation_id UUID,
    other_user_id UUID,
    last_message_at TIMESTAMP,
    unread_count INT,
    is_archived BOOLEAN,
    created_at TIMESTAMP,
    PRIMARY KEY (user_id, last_message_at, conversation_id)
) WITH CLUSTERING ORDER BY (last_message_at DESC);

-- CRITICAL: Create counter tables for efficient counting
-- Create bubble message counts table
CREATE TABLE IF NOT EXISTS bubble_message_counts (
    bubble_id UUID PRIMARY KEY,
    total_messages counter
);

-- Create conversation unread counts table
CREATE TABLE IF NOT EXISTS conversation_unread_counts (
    conversation_id UUID,
    user_id UUID,
    unread_count counter,
    PRIMARY KEY (conversation_id, user_id)
);

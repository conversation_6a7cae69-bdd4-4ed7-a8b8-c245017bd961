-- Migration: Change presence_status VARCHAR to is_present BOOLEAN
-- This migration converts the presence_status field from a VARCHAR to a more efficient BOOLEAN field

BEGIN;

-- Add the new is_present column as BOOLEAN with default false
ALTER TABLE users ADD COLUMN is_present BO<PERSON><PERSON>N DEFAULT false;

-- Convert existing presence_status values to is_present
-- Consider 'online', 'active', 'available' as present (true)
-- Consider 'offline', 'away', 'busy', 'invisible', null as not present (false)
UPDATE users
SET is_present = CASE
    WHEN presence_status IN ('online', 'active', 'available') THEN true
    ELSE false
END;

-- Make is_present NOT NULL since we've set all values
ALTER TABLE users ALTER COLUMN is_present SET NOT NULL;

-- Drop the old presence_status column
ALTER TABLE users DROP COLUMN presence_status;

COMMIT;
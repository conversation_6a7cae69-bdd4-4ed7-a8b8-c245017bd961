-- Migration Rollback: Change is_present BOOLEAN back to presence_status VARCHAR
-- This migration reverses the change from is_present BOOLEAN to presence_status VARCHAR

BEGIN;

-- Add back the presence_status column as VARCHAR
ALTER TABLE users ADD COLUMN presence_status VARCHAR(20) DEFAULT 'offline';

-- Convert is_present values back to presence_status
-- true -> 'online', false -> 'offline'
UPDATE users
SET presence_status = CASE
    WHEN is_present = true THEN 'online'
    ELSE 'offline'
END;

-- Drop the is_present column
ALTER TABLE users DROP COLUMN is_present;

COMMIT;
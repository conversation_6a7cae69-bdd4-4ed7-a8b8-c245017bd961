-- Migration: Initial Schema
-- Version: 000001
-- Description: Create initial database schema with proper indexes for contact search

BEGIN;

-- 1. EXTENSIONS AND TYPES
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For full-text search optimization

-- Create ENUMs
DO $$ BEGIN CREATE TYPE bubble_status AS ENUM ('active', 'expired', 'dissolved', 'archived'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE bubble_member_status AS ENUM ('active', 'left', 'removed', 'pending', 'accepted', 'declined'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE bubble_request_type AS ENUM ('invite', 'join', 'kick', 'start'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE request_status AS ENUM ('pending', 'approved', 'rejected', 'expired'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE vote_type AS ENUM ('approve', 'reject'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE friend_request_status AS ENUM ('pending', 'accepted', 'declined', 'expired'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE contact_request_status AS ENUM ('pending', 'accepted', 'declined', 'expired'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE notification_type AS ENUM (
    'contactRequestReceived',
    'contactRequestAccepted',
    'contactRequestDeclined',
    'friendRequestReceived',
    'friendRequestAccepted',
    'friendRequestDeclined',
    'bubbleInviteReceived',
    'bubbleInviteAccepted',
    'bubbleInviteDeclined',
    'bubbleJoinRequestReceived',
    'bubbleJoinRequestAccepted',
    'bubbleJoinRequestDeclined',
    'bubbleKickoutRequestReceived',
    'bubbleKickoutRequestAccepted',
    'bubbleKickoutRequestDeclined',
    'bubbleStartRequestReceived',
    'bubbleStartRequestAccepted',
    'bubbleStartRequestDeclined',
    'bubbleExpired',
    'bubbleDissolved',
    'bubbleCallStarted',
    'bubbleCallEnded',
    'bubbleCallMissed',
    'birthday',
    'systemNotification'
); EXCEPTION WHEN duplicate_object THEN null; END $$;

-- 2. CORE TABLES
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(40) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    avatar_bucket_name VARCHAR(100),
    avatar_object_key VARCHAR(500),
    avatar_url VARCHAR(500), -- For backward compatibility
    date_of_birth DATE NOT NULL,
    is_premium BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    is_private BOOLEAN DEFAULT false,
    is_banned BOOLEAN DEFAULT false,
    banned_at TIMESTAMP WITH TIME ZONE,
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    presence_status VARCHAR(20) DEFAULT 'offline',
    notification_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS bubbles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_id UUID REFERENCES users(id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL,
    capacity INTEGER NOT NULL DEFAULT 5 CHECK (capacity >= 2 AND capacity <= 5),
    member_count INTEGER NOT NULL DEFAULT 0 CHECK (member_count >= 0),
    status bubble_status DEFAULT 'active',
    expires_at TIMESTAMP WITH TIME ZONE,
    friend_request_on_expire BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS bubble_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bubble_id UUID NOT NULL REFERENCES bubbles(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status bubble_member_status DEFAULT 'active',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(bubble_id, user_id, status)
);

-- User relationships table with enforced single active relationship rule
CREATE TABLE IF NOT EXISTS user_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    to_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL, -- 'none', 'contact', 'bubbler', 'maybefriend', 'friend', 'block'
    status VARCHAR(20) DEFAULT 'active',     -- 'active', 'inactive', 'expired'
    created_by UUID REFERENCES users(id),
    reason TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT user_relationships_different_users CHECK (from_user_id != to_user_id),
    CONSTRAINT user_relationships_unique_active UNIQUE (from_user_id, to_user_id, status) DEFERRABLE INITIALLY DEFERRED
);

-- 3. OPTIMIZED INDEXES FOR CONTACT SEARCH

-- User search indexes (critical for contacts page performance)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_search_text 
ON users USING gin((first_name || ' ' || last_name || ' ' || username || ' ' || display_name) gin_trgm_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_presence_status 
ON users (presence_status) WHERE is_active = true AND is_banned = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_last_active 
ON users (last_active_at DESC) WHERE is_active = true AND is_banned = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active_not_banned 
ON users (is_active, is_banned, created_at DESC);

-- Bubble membership indexes (for bubble status filtering)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bubble_members_user_active 
ON bubble_members (user_id, status) WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bubble_members_bubble_active 
ON bubble_members (bubble_id, status) WHERE status = 'active';

-- User relationships indexes (for contact filtering)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_relationships_from_user 
ON user_relationships (from_user_id, relationship_type, status) WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_relationships_to_user 
ON user_relationships (to_user_id, relationship_type, status) WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_relationships_bidirectional 
ON user_relationships (from_user_id, to_user_id, relationship_type, status);

-- Composite index for contact search optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_contact_search_composite 
ON users (is_active, is_banned, presence_status, last_active_at DESC) 
WHERE is_active = true AND is_banned = false;

-- Index for birthday queries optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_birthday_month_day 
ON users (EXTRACT(MONTH FROM date_of_birth), EXTRACT(DAY FROM date_of_birth), is_active) 
WHERE is_active = true AND date_of_birth IS NOT NULL;

-- 4. FUNCTIONS FOR CONTACT SEARCH OPTIMIZATION

-- Function to get user's current bubble status
CREATE OR REPLACE FUNCTION get_user_bubble_status(user_uuid UUID)
RETURNS TEXT AS $$
DECLARE
    bubble_count INTEGER;
    bubble_capacity INTEGER;
    bubble_member_count INTEGER;
BEGIN
    -- Check if user is in any active bubble
    SELECT COUNT(*), b.capacity, b.member_count
    INTO bubble_count, bubble_capacity, bubble_member_count
    FROM bubble_members bm
    JOIN bubbles b ON bm.bubble_id = b.id
    WHERE bm.user_id = user_uuid 
    AND bm.status = 'active' 
    AND b.status = 'active';
    
    IF bubble_count = 0 THEN
        RETURN 'no_bubble';
    ELSIF bubble_member_count >= bubble_capacity THEN
        RETURN 'full_bubble';
    ELSE
        RETURN 'not_full_bubble';
    END IF;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get relationship between two users
CREATE OR REPLACE FUNCTION get_user_relationship(user1_uuid UUID, user2_uuid UUID)
RETURNS TEXT AS $$
DECLARE
    rel_type TEXT;
BEGIN
    -- Check bidirectional relationship
    SELECT relationship_type INTO rel_type
    FROM user_relationships
    WHERE ((from_user_id = user1_uuid AND to_user_id = user2_uuid) 
           OR (from_user_id = user2_uuid AND to_user_id = user1_uuid))
    AND status = 'active'
    ORDER BY created_at DESC
    LIMIT 1;
    
    RETURN COALESCE(rel_type, 'none');
END;
$$ LANGUAGE plpgsql STABLE;

COMMIT;

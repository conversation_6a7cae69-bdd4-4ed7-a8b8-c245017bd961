-- Migration: Initial Schema Rollback
-- Version: 000001
-- Description: Rollback initial database schema

BEGIN;

-- Drop functions
DROP FUNCTION IF EXISTS get_user_relationship(UUID, UUID);
DROP FUNCTION IF EXISTS get_user_bubble_status(UUID);

-- Drop indexes
DROP INDEX CONCURRENTLY IF EXISTS idx_users_contact_search_composite;
DROP INDEX CONCURRENTLY IF EXISTS idx_users_birthday_month_day;
DROP INDEX CONCURRENTLY IF EXISTS idx_user_relationships_bidirectional;
DROP INDEX CONCURRENTLY IF EXISTS idx_user_relationships_to_user;
DROP INDEX CONCURRENTLY IF EXISTS idx_user_relationships_from_user;
DROP INDEX CONCURRENTLY IF EXISTS idx_bubble_members_bubble_active;
DROP INDEX CONCURRENTLY IF EXISTS idx_bubble_members_user_active;
DROP INDEX CONCURRENTLY IF EXISTS idx_users_active_not_banned;
DROP INDEX CONCURRENTLY IF EXISTS idx_users_last_active;
DROP INDEX CONCURRENTLY IF EXISTS idx_users_presence_status;
DROP INDEX CONCURRENTLY IF EXISTS idx_users_search_text;

-- Drop tables (in reverse dependency order)
DROP TABLE IF EXISTS user_relationships;
DROP TABLE IF EXISTS bubble_members;
DROP TABLE IF EXISTS bubbles;
DROP TABLE IF EXISTS users;

-- Drop types
DROP TYPE IF EXISTS notification_type;
DROP TYPE IF EXISTS contact_request_status;
DROP TYPE IF EXISTS friend_request_status;
DROP TYPE IF EXISTS vote_type;
DROP TYPE IF EXISTS request_status;
DROP TYPE IF EXISTS bubble_request_type;
DROP TYPE IF EXISTS bubble_member_status;
DROP TYPE IF EXISTS bubble_status;

-- Drop extensions (be careful with this in production)
-- DROP EXTENSION IF EXISTS "pg_trgm";
-- DROP EXTENSION IF EXISTS "uuid-ossp";

COMMIT;

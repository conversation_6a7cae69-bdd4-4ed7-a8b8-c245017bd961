package database

import (
	"context"
	"fmt"
	"time"

github.com/jackc/pgx/v5
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// userColumns is the canonical list of columns for the users table.
// Using a constant prevents errors from mismatched column order or count.
const userColumns = `
	id, username, email, first_name, last_name, display_name,
	avatar_bucket_name, avatar_object_key, avatar_url, date_of_birth,
	is_premium, is_active, is_private, is_banned, banned_at,
	last_active_at, is_present, notification_settings, created_at, updated_at
`

// PostgreSQLClient wraps pgxpool.Pool with additional functionality
type PostgreSQLClient struct {
	Pool   *pgxpool.Pool
	logger *zap.Logger
	config *config.PostgreSQLConfig
}

// NewPostgreSQLClient creates a new PostgreSQL client
func NewPostgreSQLClient(cfg *config.PostgreSQLConfig, logger *zap.Logger) (*PostgreSQLClient, error) {
	// Configure connection pool
	poolConfig, err := pgxpool.ParseConfig(cfg.GetDSN())
	if err != nil {
		return nil, fmt.Errorf("failed to parse PostgreSQL config: %w", err)
	}

	// Set pool configuration
	poolConfig.MaxConns = cfg.MaxConnections
	poolConfig.MinConns = cfg.MinConnections
	poolConfig.MaxConnLifetime = cfg.MaxConnectionLifetime
	poolConfig.MaxConnIdleTime = cfg.MaxConnectionIdleTime
	poolConfig.HealthCheckPeriod = cfg.HealthCheckPeriod

	// Create connection pool
	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create PostgreSQL pool: %w", err)
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping PostgreSQL: %w", err)
	}

	logger.Info("PostgreSQL connection established",
		zap.String("host", cfg.Host),
		zap.Int("port", cfg.Port),
		zap.String("database", cfg.Database),
		zap.Int32("max_connections", cfg.MaxConnections),
	)

	return &PostgreSQLClient{
		Pool:   pool,
		logger: logger,
		config: cfg,
	}, nil

}

// Close closes the PostgreSQL connection pool
func (c *PostgreSQLClient) Close() {
	if c.Pool != nil {
		c.Pool.Close()
		c.logger.Info("PostgreSQL connection pool closed")
	}
}

// Health checks the health of the PostgreSQL connection
func (c *PostgreSQLClient) Health(ctx context.Context) error {
	return c.Pool.Ping(ctx)
}

// Stats returns connection pool statistics
func (c *PostgreSQLClient) Stats() *pgxpool.Stat {
	return c.Pool.Stat()
}

// GetUserByID retrieves a user by ID
func (c *PostgreSQLClient) GetUserByID(ctx context.Context, userID string) (*User, error) {
	query := `SELECT id, username, email, first_name, last_name, display_name, avatar_bucket_name, avatar_object_key, avatar_url, date_of_birth, is_premium, is_active, is_private, is_banned, banned_at, last_active_at, is_present, notification_settings, created_at, updated_at FROM users WHERE id = $1`

	var user User
	err := c.Pool.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
		&user.DisplayName, &user.AvatarBucketName, &user.AvatarObjectKey, &user.AvatarURL,
		&user.DateOfBirth, &user.IsPremium, &user.IsActive, &user.IsPrivate,
		&user.IsBanned, &user.BannedAt, &user.LastActiveAt, &user.IsPresent,
		&user.NotificationSettings, &user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user by ID: %w", HandlePgxError(err, "users"))
	}

	return &user, nil
}

// CreateUser creates a new user
func (c *PostgreSQLClient) CreateUser(ctx context.Context, user *User) error {
	query := `INSERT INTO users (id, username, email, first_name, last_name, avatar_url, date_of_birth, is_private, notification_settings) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING created_at, updated_at`

	err := c.Pool.QueryRow(ctx, query,
		user.ID, user.Username, user.Email, user.FirstName, user.LastName,
		user.AvatarURL, user.DateOfBirth, user.IsPrivate, user.NotificationSettings,
	).Scan(&user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create user: %w", HandlePgxError(err, "users"))
	}

	return nil

}

// SearchUsers searches for users by username, first name, or last name
func (c *PostgreSQLClient) SearchUsers(ctx context.Context, query string, limit int) ([]*User, error) {
	searchQuery := `SELECT id, username, email, first_name, last_name, avatar_url, date_of_birth, is_active, is_private, is_banned, banned_at, is_premium, notification_settings, created_at, updated_at FROM users WHERE is_active = true AND is_private = false AND (username ILIKE $1 OR first_name ILIKE $1 OR last_name ILIKE $1) ORDER BY CASE WHEN username ILIKE $1 THEN 1 WHEN first_name ILIKE $1 THEN 2 WHEN last_name ILIKE $1 THEN 3 ELSE 4 END, username LIMIT $2`

	searchPattern := "%" + query + "%"
	rows, err := c.Pool.Query(ctx, searchQuery, searchPattern, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to search users: %w", HandlePgxError(err, "users"))
	}
	defer rows.Close()

	var users []*User
	for rows.Next() {
		var user User
		err := rows.Scan(
			&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
			&user.AvatarURL, &user.DateOfBirth, &user.IsActive, &user.IsPrivate,
			&user.IsBanned, &user.BannedAt, &user.IsPremium, &user.NotificationSettings, &user.CreatedAt, &user.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %w", HandlePgxError(err, "users"))
		}
		users = append(users, &user)
	}

	return users, nil

}

// UpdateUser updates an existing user
func (c *PostgreSQLClient) UpdateUser(ctx context.Context, user *User) error {
	query := `UPDATE users SET username = $2, first_name = $3, last_name = $4, avatar_url = $5, date_of_birth = $6, is_private = $7, notification_settings = $8, updated_at = NOW() WHERE id = $1 RETURNING updated_at`

	err := c.Pool.QueryRow(ctx, query,
		user.ID, user.Username, user.FirstName, user.LastName,
		user.AvatarURL, user.DateOfBirth, user.IsPrivate, user.NotificationSettings,
	).Scan(&user.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to update user: %w", HandlePgxError(err, "users"))
	}

	return nil

}

// SoftDeleteUser soft deletes a user by setting is_active to false
func (c *PostgreSQLClient) SoftDeleteUser(ctx context.Context, userID string) error {
	query := `UPDATE users SET is_active = false, updated_at = NOW() WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to soft delete user: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to soft delete user: %w", NewNotFoundError("users", "user not found"))
	}

	return nil

}

// BanUser bans a user by setting is_banned to true
func (c *PostgreSQLClient) BanUser(ctx context.Context, userID string) error {
	query := `UPDATE users SET is_banned = true, banned_at = NOW(), updated_at = NOW() WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to ban user: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to ban user: %w", NewNotFoundError("users", "user not found"))
	}

	return nil

}

// UnbanUser unbans a user by setting is_banned to false
func (c *PostgreSQLClient) UnbanUser(ctx context.Context, userID string) error {
	query := `UPDATE users SET is_banned = false, banned_at = NULL, updated_at = NOW() WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to unban user: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to unban user: %w", NewNotFoundError("users", "user not found"))
	}

	return nil

}

// ===== USER STATUS DAL METHODS =====

// SetUserPremiumStatus updates a user's premium status.
// This should be called by your payment/subscription service after a successful transaction.
func (c *PostgreSQLClient) SetUserPremiumStatus(ctx context.Context, userID string, isPremium bool) error {
	query := `
		UPDATE users
		SET is_premium = $2, updated_at = NOW()
		WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID, isPremium)
	if err != nil {
		return fmt.Errorf("failed to set user premium status: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to set user premium status: %w", NewNotFoundError("users", "user not found"))
	}

	return nil
}

// GetUserByEmail retrieves a user by email address
func (c *PostgreSQLClient) GetUserByEmail(ctx context.Context, email string) (*User, error) {
	query := `SELECT id, username, email, first_name, last_name, avatar_url, date_of_birth, is_active, is_private, is_banned, banned_at, is_premium, notification_settings, created_at, updated_at FROM users WHERE email = $1 AND is_active = true`

	var user User
	err := c.Pool.QueryRow(ctx, query, email).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.FirstName,
		&user.LastName,
		&user.AvatarURL,
		&user.DateOfBirth,
		&user.IsActive,
		&user.IsPrivate,
		&user.IsBanned,
		&user.BannedAt,
		&user.IsPremium,
		&user.NotificationSettings,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user by email: %w", HandlePgxError(err, "users"))
	}

	return &user, nil

}

// GetUserByUsername retrieves a user by username
func (c *PostgreSQLClient) GetUserByUsername(ctx context.Context, username string) (*User, error) {
	query := `SELECT id, username, email, first_name, last_name, avatar_url, date_of_birth, is_active, is_private, is_banned, banned_at, is_premium, notification_settings, created_at, updated_at FROM users WHERE username = $1 AND is_active = true`

	var user User
	err := c.Pool.QueryRow(ctx, query, username).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.FirstName,
		&user.LastName,
		&user.AvatarURL,
		&user.DateOfBirth,
		&user.IsActive,
		&user.IsPrivate,
		&user.IsBanned,
		&user.BannedAt,
		&user.IsPremium,
		&user.NotificationSettings,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user by username: %w", HandlePgxError(err, "users"))
	}

	return &user, nil

}

// User represents the canonical user entity for database operations
// This is the single source of truth for User struct definition
type User struct {
	ID                   string                 `json:"id"`
	Username             *string                `json:"username"`
	Email                string                 `json:"email"`
	FirstName            *string                `json:"first_name"`
	LastName             *string                `json:"last_name"`
	DisplayName          *string                `json:"display_name"`
	AvatarBucketName     *string                `json:"avatar_bucket_name"`
	AvatarObjectKey      *string                `json:"avatar_object_key"`
	AvatarURL            *string                `json:"avatar_url"` // For backward compatibility
	DateOfBirth          *time.Time             `json:"date_of_birth"`
	IsPremium            bool                   `json:"is_premium"`
	IsActive             bool                   `json:"is_active"`
	IsPrivate            bool                   `json:"is_private"`
	IsBanned             bool                   `json:"is_banned"`
	BannedAt             *time.Time             `json:"banned_at"`
	LastActiveAt         *time.Time             `json:"last_active_at"`
	IsPresent            bool                   `json:"is_present"`
	NotificationSettings map[string]interface{} `json:"notification_settings"`
	CreatedAt            time.Time              `json:"created_at"`
	UpdatedAt            time.Time              `json:"updated_at"`
}

// BubbleRequest represents a bubble-related request (invite, join, kick, start)
type BubbleRequest struct {
	ID                string     `json:"id"`
	RequestType       string     `json:"request_type"` // 'invite', 'join', 'kick', 'start'
	BubbleID          string     `json:"bubble_id"`
	RequesterID       string     `json:"requester_id"`       // User who initiated the request
	TargetUserID      *string    `json:"target_user_id"`     // User being invited/kicked (null for join)
	Status            string     `json:"status"`             // 'pending', 'approved', 'rejected', 'expired'
	RequiresUnanimous bool       `json:"requires_unanimous"` // Whether all members must approve
	ExpiresAt         time.Time  `json:"expires_at"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
	CompletedAt       *time.Time `json:"completed_at"`
}

// RequestVote represents an individual member's vote on a request
type RequestVote struct {
	ID        string    `json:"id"`
	RequestID string    `json:"request_id"`
	VoterID   string    `json:"voter_id"` // Member who is voting
	Vote      string    `json:"vote"`     // 'approve', 'reject'
	VotedAt   time.Time `json:"voted_at"`
	CreatedAt time.Time `json:"created_at"`
}

// ContactRequest represents a manual contact request between users
type ContactRequest struct {
	ID          string    `json:"id"`
	RequesterID string    `json:"requester_id"`
	RecipientID string    `json:"recipient_id"`
	Status      string    `json:"status"` // 'pending', 'accepted', 'declined', 'expired'
	ExpiresAt   time.Time `json:"expires_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// FriendRequest represents an auto-generated friend request from expired bubbles
// When a bubble expires, all former bubblers get 'maybefriend' relationship status
// and friend requests are created. If both accept -> 'friend', if one declines -> 'contact'
type FriendRequest struct {
	ID             string    `json:"id"`
	RequesterID    string    `json:"requester_id"`
	RecipientID    string    `json:"recipient_id"`
	SourceBubbleID string    `json:"source_bubble_id"` // Always present for auto-generated requests
	AutoGenerated  bool      `json:"auto_generated"`   // Always true
	Status         string    `json:"status"`           // 'pending', 'accepted', 'declined', 'expired'
	ExpiresAt      time.Time `json:"expires_at"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// ===== CONTACT REQUEST DAL METHODS =====
// CreateContactRequest creates a new contact request
func (c *PostgreSQLClient) CreateContactRequest(ctx context.Context, contactRequest *ContactRequest) error {
	query := `INSERT INTO contact_requests (id, requester_id, recipient_id, status, expires_at) VALUES ($1, $2, $3, $4, $5) RETURNING created_at, updated_at`

	err := c.Pool.QueryRow(ctx, query,
		contactRequest.ID, contactRequest.RequesterID, contactRequest.RecipientID,
		contactRequest.Status, contactRequest.ExpiresAt,
	).Scan(&contactRequest.CreatedAt, &contactRequest.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create contact request: %w", HandlePgxError(err, "contact_requests"))
	}

	return nil

}

// GetContactRequestsByUser retrieves contact requests for a user (sent or received)
func (c *PostgreSQLClient) GetContactRequestsByUser(ctx context.Context, userID string, requestType string) ([]*ContactRequest, error) {
	var query string
	switch requestType {
	case "sent":
		query = `SELECT id, requester_id, recipient_id, status, expires_at, created_at, updated_at FROM contact_requests WHERE requester_id = $1 ORDER BY created_at DESC`
	case "received":
		query = `SELECT id, requester_id, recipient_id, status, expires_at, created_at, updated_at FROM contact_requests WHERE recipient_id = $1 ORDER BY created_at DESC`
	default:
		return nil, fmt.Errorf("invalid request type: %s. Must be 'sent' or 'received'", requestType)
	}

	rows, err := c.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get contact requests: %w", HandlePgxError(err, "contact_requests"))
	}
	defer rows.Close()

	var requests []*ContactRequest
	for rows.Next() {
		var request ContactRequest
		err := rows.Scan(
			&request.ID, &request.RequesterID, &request.RecipientID,
			&request.Status, &request.ExpiresAt,
			&request.CreatedAt, &request.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan contact request: %w", HandlePgxError(err, "contact_requests"))
		}
		requests = append(requests, &request)
	}

	return requests, nil

}

// UpdateContactRequestStatus updates the status of a contact request
func (c *PostgreSQLClient) UpdateContactRequestStatus(ctx context.Context, requestID string, status string) error {
	query := `UPDATE contact_requests SET status = $2, updated_at = NOW() WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, requestID, status)
	if err != nil {
		return fmt.Errorf("failed to update contact request status: %w", HandlePgxError(err, "contact_requests"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("contact request not found: %s", requestID)
	}

	return nil

}

// GetPendingContactRequestsCount returns the count of pending contact requests for a user
func (c *PostgreSQLClient) GetPendingContactRequestsCount(ctx context.Context, userID string, requestType string) (int, error) {
	var query string
	switch requestType {
	case "sent":
		query = `SELECT COUNT(*) FROM contact_requests WHERE requester_id = $1 AND status = 'pending'`
	case "received":
		query = `SELECT COUNT(*) FROM contact_requests WHERE recipient_id = $1 AND status = 'pending'`
	default:
		return 0, fmt.Errorf("invalid request type: %s. Must be 'sent' or 'received'", requestType)
	}

	var count int
	err := c.Pool.QueryRow(ctx, query, userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get pending contact requests count: %w", HandlePgxError(err, "contact_requests"))
	}

	return count, nil

}

// ===== BUBBLE REQUEST DAL METHODS =====
// GetBubbleRequestsByUser retrieves bubble requests for a user (sent or received)
func (c *PostgreSQLClient) GetBubbleRequestsByUser(ctx context.Context, userID string, requestType string) ([]*BubbleRequest, error) {
	var query string
	switch requestType {
	case "sent":
		query = `SELECT id, request_type, bubble_id, requester_id, target_user_id, status, requires_unanimous, expires_at, created_at, updated_at, completed_at FROM bubble_requests WHERE requester_id = $1 ORDER BY created_at DESC`
	case "received":
		query = `SELECT id, request_type, bubble_id, requester_id, target_user_id, status, requires_unanimous, expires_at, created_at, updated_at, completed_at FROM bubble_requests WHERE target_user_id = $1 ORDER BY created_at DESC`
	default:
		return nil, fmt.Errorf("invalid request type: %s. Must be 'sent' or 'received'", requestType)
	}

	rows, err := c.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get bubble requests: %w", HandlePgxError(err, "bubble_requests"))
	}
	defer rows.Close()

	var requests []*BubbleRequest
	for rows.Next() {
		var request BubbleRequest
		err := rows.Scan(
			&request.ID, &request.RequestType, &request.BubbleID, &request.RequesterID,
			&request.TargetUserID, &request.Status, &request.RequiresUnanimous,
			&request.ExpiresAt, &request.CreatedAt, &request.UpdatedAt, &request.CompletedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan bubble request: %w", HandlePgxError(err, "bubble_requests"))
		}
		requests = append(requests, &request)
	}

	return requests, nil

}

// GetPendingBubbleRequestsCount returns the count of pending bubble requests for a user
func (c *PostgreSQLClient) GetPendingBubbleRequestsCount(ctx context.Context, userID string, requestType string) (int, error) {
	var query string
	switch requestType {
	case "sent":
		query = `SELECT COUNT(*) FROM bubble_requests WHERE requester_id = $1 AND status = 'pending'`
	case "received":
		query = `SELECT COUNT(*) FROM bubble_requests WHERE target_user_id = $1 AND status = 'pending'`
	default:
		return 0, fmt.Errorf("invalid request type: %s. Must be 'sent' or 'received'", requestType)
	}

	var count int
	err := c.Pool.QueryRow(ctx, query, userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get pending bubble requests count: %w", HandlePgxError(err, "bubble_requests"))
	}

	return count, nil

}

// ===== FRIEND REQUEST DAL METHODS =====
// CreateFriendRequest creates a new auto-generated friend request
func (c *PostgreSQLClient) CreateFriendRequest(ctx context.Context, friendRequest *FriendRequest) error {
	query := `INSERT INTO friend_requests (id, requester_id, recipient_id, source_bubble_id, auto_generated, status, expires_at) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING created_at, updated_at`

	err := c.Pool.QueryRow(ctx, query,
		friendRequest.ID, friendRequest.RequesterID, friendRequest.RecipientID,
		friendRequest.SourceBubbleID, friendRequest.AutoGenerated, friendRequest.Status,
		friendRequest.ExpiresAt,
	).Scan(&friendRequest.CreatedAt, &friendRequest.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create friend request: %w", HandlePgxError(err, "friend_requests"))
	}

	return nil

}

// GetFriendRequestsByUser retrieves friend requests for a user (sent or received)
func (c *PostgreSQLClient) GetFriendRequestsByUser(ctx context.Context, userID string, requestType string) ([]*FriendRequest, error) {
	var query string
	switch requestType {
	case "sent":
		query = `SELECT id, requester_id, recipient_id, source_bubble_id, auto_generated, status, expires_at, created_at, updated_at FROM friend_requests WHERE requester_id = $1 ORDER BY created_at DESC`
	case "received":
		query = `SELECT id, requester_id, recipient_id, source_bubble_id, auto_generated, status, expires_at, created_at, updated_at FROM friend_requests WHERE recipient_id = $1 ORDER BY created_at DESC`
	default:
		return nil, fmt.Errorf("invalid request type: %s. Must be 'sent' or 'received'", requestType)
	}

	rows, err := c.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get friend requests: %w", HandlePgxError(err, "friend_requests"))
	}
	defer rows.Close()

	var requests []*FriendRequest
	for rows.Next() {
		var request FriendRequest
		err := rows.Scan(
			&request.ID, &request.RequesterID, &request.RecipientID, &request.SourceBubbleID,
			&request.AutoGenerated, &request.Status, &request.ExpiresAt,
			&request.CreatedAt, &request.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan friend request: %w", HandlePgxError(err, "friend_requests"))
		}
		requests = append(requests, &request)
	}

	return requests, nil

}

// UpdateFriendRequestStatus updates the status of a friend request
func (c *PostgreSQLClient) UpdateFriendRequestStatus(ctx context.Context, requestID string, status string) error {
	query := `UPDATE friend_requests SET status = $2, updated_at = NOW() WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, requestID, status)
	if err != nil {
		return fmt.Errorf("failed to update friend request status: %w", HandlePgxError(err, "friend_requests"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("friend request not found: %s", requestID)
	}

	return nil

}

// ===== USER RELATIONSHIP DAL METHODS =====
// CreateUserRelationship creates a new user relationship
func (c *PostgreSQLClient) CreateUserRelationship(ctx context.Context, fromUserID, toUserID, relationshipType string, createdBy *string, reason *string) error {
	// First, expire any existing active relationship between these users
	_, err := c.Pool.Exec(ctx, `UPDATE user_relationships SET status = 'expired', updated_at = NOW() WHERE ((from_user_id = $1 AND to_user_id = $2) OR (from_user_id = $2 AND to_user_id = $1)) AND status = 'active'`,
		fromUserID, toUserID)
	if err != nil {
		return fmt.Errorf("failed to expire existing relationships: %w", HandlePgxError(err, "user_relationships"))
	}

	// Create the new relationship (bidirectional)
	query := `
		INSERT INTO user_relationships (from_user_id, to_user_id, relationship_type, created_by, reason)
		VALUES ($1, $2, $3, $4, $5), ($2, $1, $3, $4, $5)`

	_, err = c.Pool.Exec(ctx, query, fromUserID, toUserID, relationshipType, createdBy, reason)
	if err != nil {
		return fmt.Errorf("failed to create user relationship: %w", HandlePgxError(err, "user_relationships"))
	}

	return nil
}

// GetUserRelationships retrieves all active relationships for a user
func (c *PostgreSQLClient) GetUserRelationships(ctx context.Context, userID string, relationshipType *string) (map[string][]string, error) {
	query := `SELECT to_user_id, relationship_type FROM user_relationships WHERE from_user_id = $1 AND status = 'active'`

	args := []interface{}{userID}

	if relationshipType != nil {
		query += ` AND relationship_type = $2`
		args = append(args, *relationshipType)
	}

	query += ` ORDER BY created_at DESC`

	rows, err := c.Pool.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get user relationships: %w", HandlePgxError(err, "user_relationships"))
	}
	defer rows.Close()

	relationships := make(map[string][]string)
	for rows.Next() {
		var toUserID, relType string
		err := rows.Scan(&toUserID, &relType)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user relationship: %w", HandlePgxError(err, "user_relationships"))
		}

		relationships[relType] = append(relationships[relType], toUserID)
	}

	return relationships, nil
}

// ===== NEW PENDING REQUEST DAL METHODS =====

// GetPendingRequestsForUser retrieves all pending requests for a user (sent and received)
func (c *PostgreSQLClient) GetPendingRequestsForUser(ctx context.Context, userID string) (*PendingRequestsSummary, error) {
	// Get pending contact requests
	sentContactRequests, err := c.GetContactRequestsByUser(ctx, userID, "sent")
	if err != nil {
		return nil, fmt.Errorf("failed to get sent contact requests: %w", err)
	}

	receivedContactRequests, err := c.GetContactRequestsByUser(ctx, userID, "received")
	if err != nil {
		return nil, fmt.Errorf("failed to get received contact requests: %w", err)
	}

	// Get pending bubble requests
	sentBubbleRequests, err := c.GetBubbleRequestsByUser(ctx, userID, "sent")
	if err != nil {
		return nil, fmt.Errorf("failed to get sent bubble requests: %w", err)
	}

	receivedBubbleRequests, err := c.GetBubbleRequestsByUser(ctx, userID, "received")
	if err != nil {
		return nil, fmt.Errorf("failed to get received bubble requests: %w", err)
	}

	// Get pending friend requests
	sentFriendRequests, err := c.GetFriendRequestsByUser(ctx, userID, "sent")
	if err != nil {
		return nil, fmt.Errorf("failed to get sent friend requests: %w", err)
	}

	receivedFriendRequests, err := c.GetFriendRequestsByUser(ctx, userID, "received")
	if err != nil {
		return nil, fmt.Errorf("failed to get received friend requests: %w", err)
	}

	// Filter to only pending requests
	var pendingSentContactRequests, pendingReceivedContactRequests []*ContactRequest
	var pendingSentBubbleRequests, pendingReceivedBubbleRequests []*BubbleRequest
	var pendingSentFriendRequests, pendingReceivedFriendRequests []*FriendRequest

	for _, req := range sentContactRequests {
		if req.Status == "pending" {
			pendingSentContactRequests = append(pendingSentContactRequests, req)
		}
	}

	for _, req := range receivedContactRequests {
		if req.Status == "pending" {
			pendingReceivedContactRequests = append(pendingReceivedContactRequests, req)
		}
	}

	for _, req := range sentBubbleRequests {
		if req.Status == "pending" {
			pendingSentBubbleRequests = append(pendingSentBubbleRequests, req)
		}
	}

	for _, req := range receivedBubbleRequests {
		if req.Status == "pending" {
			pendingReceivedBubbleRequests = append(pendingReceivedBubbleRequests, req)
		}
	}

	for _, req := range sentFriendRequests {
		if req.Status == "pending" {
			pendingSentFriendRequests = append(pendingSentFriendRequests, req)
		}
	}

	for _, req := range receivedFriendRequests {
		if req.Status == "pending" {
			pendingReceivedFriendRequests = append(pendingReceivedFriendRequests, req)
		}
	}

	return &PendingRequestsSummary{
		SentContactRequests:     pendingSentContactRequests,
		ReceivedContactRequests: pendingReceivedContactRequests,
		SentBubbleRequests:      pendingSentBubbleRequests,
		ReceivedBubbleRequests:  pendingReceivedBubbleRequests,
		SentFriendRequests:      pendingSentFriendRequests,
		ReceivedFriendRequests:  pendingReceivedFriendRequests,
	}, nil
}

// PendingRequestsSummary represents all pending requests for a user
type PendingRequestsSummary struct {
	SentContactRequests     []*ContactRequest `json:"sent_contact_requests"`
	ReceivedContactRequests []*ContactRequest `json:"received_contact_requests"`
	SentBubbleRequests      []*BubbleRequest  `json:"sent_bubble_requests"`
	ReceivedBubbleRequests  []*BubbleRequest  `json:"received_bubble_requests"`
	SentFriendRequests      []*FriendRequest  `json:"sent_friend_requests"`
	ReceivedFriendRequests  []*FriendRequest  `json:"received_friend_requests"`
}

// GetPendingRequestsCount returns the count of all pending requests for a user
func (c *PostgreSQLClient) GetPendingRequestsCount(ctx context.Context, userID string) (*PendingRequestsCount, error) {
	// Get counts for each type of request
	sentContactCount, err := c.GetPendingContactRequestsCount(ctx, userID, "sent")
	if err != nil {
		return nil, fmt.Errorf("failed to get sent contact requests count: %w", err)
	}

	receivedContactCount, err := c.GetPendingContactRequestsCount(ctx, userID, "received")
	if err != nil {
		return nil, fmt.Errorf("failed to get received contact requests count: %w", err)
	}

	sentBubbleCount, err := c.GetPendingBubbleRequestsCount(ctx, userID, "sent")
	if err != nil {
		return nil, fmt.Errorf("failed to get sent bubble requests count: %w", err)
	}

	receivedBubbleCount, err := c.GetPendingBubbleRequestsCount(ctx, userID, "received")
	if err != nil {
		return nil, fmt.Errorf("failed to get received bubble requests count: %w", err)
	}

	// Get friend request counts
	var sentFriendCount, receivedFriendCount int
	err = c.Pool.QueryRow(ctx, `SELECT COUNT(*) FROM friend_requests WHERE requester_id = $1 AND status = 'pending'`, userID).Scan(&sentFriendCount)
	if err != nil {
		return nil, fmt.Errorf("failed to get sent friend requests count: %w", err)
	}

	err = c.Pool.QueryRow(ctx, `SELECT COUNT(*) FROM friend_requests WHERE recipient_id = $1 AND status = 'pending'`, userID).Scan(&receivedFriendCount)
	if err != nil {
		return nil, fmt.Errorf("failed to get received friend requests count: %w", err)
	}

	return &PendingRequestsCount{
		SentContactRequests:     sentContactCount,
		ReceivedContactRequests: receivedContactCount,
		SentBubbleRequests:      sentBubbleCount,
		ReceivedBubbleRequests:  receivedBubbleCount,
		SentFriendRequests:      sentFriendCount,
		ReceivedFriendRequests:  receivedFriendCount,
		Total:                   sentContactCount + receivedContactCount + sentBubbleCount + receivedBubbleCount + sentFriendCount + receivedFriendCount,
	}, nil
}

// PendingRequestsCount represents the count of all pending requests for a user
type PendingRequestsCount struct {
	SentContactRequests     int `json:"sent_contact_requests"`
	ReceivedContactRequests int `json:"received_contact_requests"`
	SentBubbleRequests      int `json:"sent_bubble_requests"`
	ReceivedBubbleRequests  int `json:"received_bubble_requests"`
	SentFriendRequests      int `json:"sent_friend_requests"`
	ReceivedFriendRequests  int `json:"received_friend_requests"`
	Total                   int `json:"total"`
}

// GetBubbleBadgeStatusForUser gets the bubble badge status for a user
func (c *PostgreSQLClient) GetBubbleBadgeStatusForUser(ctx context.Context, userID string) (string, error) {
	query := `
		SELECT
			CASE
				WHEN COUNT(bm.id) = 0 THEN 'no_bubble'
				WHEN COUNT(bm.id) = 1 THEN 'in_bubble'
				ELSE 'multiple_bubbles'
			END as status
		FROM bubble_members bm
		JOIN bubbles b ON bm.bubble_id = b.id
		WHERE bm.user_id = $1 AND bm.status = 'active' AND b.status = 'active'`

	var status string
	err := c.Pool.QueryRow(ctx, query, userID).Scan(&status)
	if err != nil {
		return "", fmt.Errorf("failed to get bubble badge status: %w", err)
	}

	return status, nil
}

// CheckAndDissolveBubble checks if a bubble has less than 2 active members and dissolves it
func (c *PostgreSQLClient) CheckAndDissolveBubble(ctx context.Context, bubbleID string) error {
	// Count active members
	countQuery := `
		SELECT COUNT(*)
		FROM bubble_members
		WHERE bubble_id = $1 AND status = 'active'`

	var activeCount int
	err := c.Pool.QueryRow(ctx, countQuery, bubbleID).Scan(&activeCount)
	if err != nil {
		return fmt.Errorf("failed to count active members: %w", err)
	}

	// If less than 2 active members, dissolve the bubble
	if activeCount < 2 {
		return c.DissolveBubble(ctx, bubbleID)
	}

	return nil
}

// DissolveBubble dissolves a bubble and then archives it
func (c *PostgreSQLClient) DissolveBubble(ctx context.Context, bubbleID string) error {
	tx, err := c.Pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// First, set bubble status to 'dissolved'
	dissolveQuery := `
		UPDATE bubbles
		SET status = 'dissolved', updated_at = NOW()
		WHERE id = $1 AND status = 'active'`

	result, err := tx.Exec(ctx, dissolveQuery, bubbleID)
	if err != nil {
		return fmt.Errorf("failed to dissolve bubble: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		// Bubble doesn't exist or is already in a final state
		return nil
	}

	// Then, archive the dissolved bubble
	archiveQuery := `
		UPDATE bubbles
		SET status = 'archived', updated_at = NOW()
		WHERE id = $1 AND status = 'dissolved'`

	_, err = tx.Exec(ctx, archiveQuery, bubbleID)
	if err != nil {
		return fmt.Errorf("failed to archive dissolved bubble: %w", err)
	}

	return tx.Commit(ctx)
}

// CheckAllBubblesForDissolution checks all active bubbles for automatic dissolution
func (c *PostgreSQLClient) CheckAllBubblesForDissolution(ctx context.Context) error {
	// Get all bubbles that could potentially need dissolution
	query := `
		SELECT b.id
		FROM bubbles b
		WHERE b.status = 'active'`

	rows, err := c.Pool.Query(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to query bubbles for dissolution check: %w", err)
	}
	defer rows.Close()

	var bubbleIDs []string
	for rows.Next() {
		var bubbleID string
		if err := rows.Scan(&bubbleID); err != nil {
			continue // Skip this bubble and continue
		}
		bubbleIDs = append(bubbleIDs, bubbleID)
	}

	// Check each bubble for dissolution
	for _, bubbleID := range bubbleIDs {
		if err := c.CheckAndDissolveBubble(ctx, bubbleID); err != nil {
			// Log error but continue with other bubbles
			// This prevents one failed bubble from stopping the entire process
			continue
		}
	}

	return nil
}

package database

import (
	"context"
	"fmt"
	"time"

	"github.com/gocql/gocql"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// CassandraClient wraps gocql.Session with additional functionality
type CassandraClient struct {
	Session *gocql.Session
	logger  *zap.Logger
	config  *config.CassandraConfig
}

// NewCassandraClient creates a new Cassandra client
func NewCassandraClient(cfg *config.CassandraConfig, logger *zap.Logger) (*CassandraClient, error) {
	// Create cluster configuration
	cluster := gocql.NewCluster(cfg.Hosts...)
	cluster.Keyspace = cfg.Keyspace
	cluster.Timeout = cfg.Timeout
	cluster.ConnectTimeout = cfg.ConnectTimeout
	cluster.NumConns = cfg.NumConnections

	// Set consistency level
	switch cfg.Consistency {
	case "one":
		cluster.Consistency = gocql.One
	case "quorum":
		cluster.Consistency = gocql.Quorum
	case "all":
		cluster.Consistency = gocql.All
	default:
		cluster.Consistency = gocql.Quorum
	}

	// Create session
	session, err := cluster.CreateSession()
	if err != nil {
		return nil, fmt.Errorf("failed to create Cassandra session: %w", err)
	}

	logger.Info("Cassandra connection established",
		zap.Strings("hosts", cfg.Hosts),
		zap.String("keyspace", cfg.Keyspace),
		zap.String("consistency", cfg.Consistency),
	)

	return &CassandraClient{
		Session: session,
		logger:  logger,
		config:  cfg,
	}, nil
}

// Close closes the Cassandra session
func (c *CassandraClient) Close() {
	if c.Session != nil {
		c.Session.Close()
		c.logger.Info("Cassandra session closed")
	}
}

// Health checks the health of the Cassandra connection
func (c *CassandraClient) Health(ctx context.Context) error {
	// Simple query to test connection
	var count int
	err := c.Session.Query("SELECT COUNT(*) FROM system.local").Scan(&count)
	return err
}

// InitializeKeyspace initializes the keyspace and tables
func (c *CassandraClient) InitializeKeyspace(ctx context.Context) error {
	// Create keyspace if it doesn't exist
	// CRITICAL FIX: Use NetworkTopologyStrategy for production multi-datacenter deployments
	createKeyspace := fmt.Sprintf(`
		CREATE KEYSPACE IF NOT EXISTS %s
		WITH REPLICATION = {
			'class': 'NetworkTopologyStrategy',
			'datacenter1': 3
		}`, c.config.Keyspace)

	if err := c.Session.Query(createKeyspace).Exec(); err != nil {
		return fmt.Errorf("failed to create keyspace: %w", err)
	}

	// Use the keyspace
	c.Session.Query(fmt.Sprintf("USE %s", c.config.Keyspace)).Exec()

	// Create messages table
	createMessagesTable := `
		CREATE TABLE IF NOT EXISTS messages (
			bubble_id UUID,
			message_id UUID,
			sender_id UUID,
			content TEXT,
			message_type TEXT,
			media_url TEXT,
			reply_to_id UUID,
			is_edited BOOLEAN,
			is_deleted BOOLEAN,
			created_at TIMESTAMP,
			updated_at TIMESTAMP,
			PRIMARY KEY (bubble_id, created_at, message_id)
		) WITH CLUSTERING ORDER BY (created_at DESC)`

	if err := c.Session.Query(createMessagesTable).Exec(); err != nil {
		return fmt.Errorf("failed to create messages table: %w", err)
	}

	// Create conversations table for direct messages
	createConversationsTable := `
		CREATE TABLE IF NOT EXISTS conversations (
			conversation_id UUID,
			participant1_id UUID,
			participant2_id UUID,
			last_message_id UUID,
			last_message_at TIMESTAMP,
			created_at TIMESTAMP,
			PRIMARY KEY (conversation_id)
		)`

	if err := c.Session.Query(createConversationsTable).Exec(); err != nil {
		return fmt.Errorf("failed to create conversations table: %w", err)
	}

	// Create conversation_messages table for direct messages
	createConversationMessagesTable := `
		CREATE TABLE IF NOT EXISTS conversation_messages (
			conversation_id UUID,
			message_id UUID,
			sender_id UUID,
			recipient_id UUID,
			content TEXT,
			message_type TEXT,
			media_url TEXT,
			reply_to_id UUID,
			is_edited BOOLEAN,
			is_deleted BOOLEAN,
			is_read BOOLEAN,
			created_at TIMESTAMP,
			updated_at TIMESTAMP,
			PRIMARY KEY (conversation_id, created_at, message_id)
		) WITH CLUSTERING ORDER BY (created_at DESC)`

	if err := c.Session.Query(createConversationMessagesTable).Exec(); err != nil {
		return fmt.Errorf("failed to create conversation_messages table: %w", err)
	}

	// Create user_conversations table for user's conversation list
	createUserConversationsTable := `
		CREATE TABLE IF NOT EXISTS user_conversations (
			user_id UUID,
			conversation_id UUID,
			other_user_id UUID,
			last_message_at TIMESTAMP,
			unread_count INT,
			is_archived BOOLEAN,
			created_at TIMESTAMP,
			PRIMARY KEY (user_id, last_message_at, conversation_id)
		) WITH CLUSTERING ORDER BY (last_message_at DESC)`

	if err := c.Session.Query(createUserConversationsTable).Exec(); err != nil {
		return fmt.Errorf("failed to create user_conversations table: %w", err)
	}

	// CRITICAL FIX: Create counter tables for efficient counting
	// Create bubble message counts table
	createBubbleMessageCountsTable := `
		CREATE TABLE IF NOT EXISTS bubble_message_counts (
			bubble_id UUID PRIMARY KEY,
			total_messages counter
		)`

	if err := c.Session.Query(createBubbleMessageCountsTable).Exec(); err != nil {
		return fmt.Errorf("failed to create bubble_message_counts table: %w", err)
	}

	// Create conversation unread counts table
	createConversationUnreadCountsTable := `
		CREATE TABLE IF NOT EXISTS conversation_unread_counts (
			conversation_id UUID,
			user_id UUID,
			unread_count counter,
			PRIMARY KEY (conversation_id, user_id)
		)`

	if err := c.Session.Query(createConversationUnreadCountsTable).Exec(); err != nil {
		return fmt.Errorf("failed to create conversation_unread_counts table: %w", err)
	}

	c.logger.Info("Cassandra keyspace and tables initialized successfully")
	return nil
}

// Message represents a chat message
type Message struct {
	BubbleID    gocql.UUID `json:"bubble_id"`
	MessageID   gocql.UUID `json:"message_id"`
	SenderID    gocql.UUID `json:"sender_id"`
	Content     string     `json:"content"`
	MessageType string     `json:"message_type"` // text, image, video, audio, file
	MediaURL    *string    `json:"media_url,omitempty"`
	ReplyToID   *gocql.UUID `json:"reply_to_id,omitempty"`
	IsEdited    bool       `json:"is_edited"`
	IsDeleted   bool       `json:"is_deleted"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// ConversationMessage represents a direct message between users
type ConversationMessage struct {
	ConversationID gocql.UUID  `json:"conversation_id"`
	MessageID      gocql.UUID  `json:"message_id"`
	SenderID       gocql.UUID  `json:"sender_id"`
	RecipientID    gocql.UUID  `json:"recipient_id"`
	Content        string      `json:"content"`
	MessageType    string      `json:"message_type"`
	MediaURL       *string     `json:"media_url,omitempty"`
	ReplyToID      *gocql.UUID `json:"reply_to_id,omitempty"`
	IsEdited       bool        `json:"is_edited"`
	IsDeleted      bool        `json:"is_deleted"`
	IsRead         bool        `json:"is_read"`
	CreatedAt      time.Time   `json:"created_at"`
	UpdatedAt      time.Time   `json:"updated_at"`
}

// Conversation represents a conversation between two users
type Conversation struct {
	ConversationID gocql.UUID  `json:"conversation_id"`
	Participant1ID gocql.UUID  `json:"participant1_id"`
	Participant2ID gocql.UUID  `json:"participant2_id"`
	LastMessageID  *gocql.UUID `json:"last_message_id,omitempty"`
	LastMessageAt  time.Time   `json:"last_message_at"`
	CreatedAt      time.Time   `json:"created_at"`
}

// CreateMessage creates a new message in a bubble
func (c *CassandraClient) CreateMessage(ctx context.Context, message *Message) error {
	// Use IF NOT EXISTS for idempotency
	query := `
		INSERT INTO messages (bubble_id, message_id, sender_id, content, message_type,
							 media_url, reply_to_id, is_edited, is_deleted, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		IF NOT EXISTS`

	err := c.Session.Query(query,
		message.BubbleID, message.MessageID, message.SenderID, message.Content,
		message.MessageType, message.MediaURL, message.ReplyToID, message.IsEdited,
		message.IsDeleted, message.CreatedAt, message.UpdatedAt,
	).Exec()

	if err != nil {
		return fmt.Errorf("failed to create message: %w", err)
	}

	// CRITICAL FIX: Atomically increment the counter for efficient counting
	incrementQuery := `UPDATE bubble_message_counts SET total_messages = total_messages + 1 WHERE bubble_id = ?`
	err = c.Session.Query(incrementQuery, message.BubbleID).Exec()
	if err != nil {
		return fmt.Errorf("failed to increment message count: %w", err)
	}

	return nil
}

// GetMessages retrieves messages for a bubble with pagination
func (c *CassandraClient) GetMessages(ctx context.Context, bubbleID gocql.UUID, limit int, pageState []byte) ([]*Message, []byte, error) {
	// CRITICAL FIX: Filter out deleted messages in the query
	query := `
		SELECT bubble_id, message_id, sender_id, content, message_type, media_url,
			   reply_to_id, is_edited, is_deleted, created_at, updated_at
		FROM messages
		WHERE bubble_id = ? AND is_deleted = false
		ORDER BY created_at DESC
		LIMIT ?`

	iter := c.Session.Query(query, bubbleID, limit).PageState(pageState).Iter()
	defer iter.Close()

	var messages []*Message
	var message Message

	for iter.Scan(&message.BubbleID, &message.MessageID, &message.SenderID,
		&message.Content, &message.MessageType, &message.MediaURL,
		&message.ReplyToID, &message.IsEdited, &message.IsDeleted,
		&message.CreatedAt, &message.UpdatedAt) {

		messages = append(messages, &Message{
			BubbleID:    message.BubbleID,
			MessageID:   message.MessageID,
			SenderID:    message.SenderID,
			Content:     message.Content,
			MessageType: message.MessageType,
			MediaURL:    message.MediaURL,
			ReplyToID:   message.ReplyToID,
			IsEdited:    message.IsEdited,
			IsDeleted:   message.IsDeleted,
			CreatedAt:   message.CreatedAt,
			UpdatedAt:   message.UpdatedAt,
		})
	}

	if err := iter.Close(); err != nil {
		return nil, nil, fmt.Errorf("failed to get messages: %w", err)
	}

	return messages, iter.PageState(), nil
}

// CreateConversationMessage creates a new direct message
func (c *CassandraClient) CreateConversationMessage(ctx context.Context, message *ConversationMessage) error {
	// Use IF NOT EXISTS for idempotency
	query := `
		INSERT INTO conversation_messages (conversation_id, message_id, sender_id, recipient_id,
										  content, message_type, media_url, reply_to_id, is_edited,
										  is_deleted, is_read, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		IF NOT EXISTS`

	err := c.Session.Query(query,
		message.ConversationID, message.MessageID, message.SenderID, message.RecipientID,
		message.Content, message.MessageType, message.MediaURL, message.ReplyToID,
		message.IsEdited, message.IsDeleted, message.IsRead, message.CreatedAt, message.UpdatedAt,
	).Exec()

	if err != nil {
		return fmt.Errorf("failed to create conversation message: %w", err)
	}

	// CRITICAL FIX: Increment unread counter for recipient if message is unread
	if !message.IsRead {
		incrementQuery := `UPDATE conversation_unread_counts SET unread_count = unread_count + 1 WHERE conversation_id = ? AND user_id = ?`
		err = c.Session.Query(incrementQuery, message.ConversationID, message.RecipientID).Exec()
		if err != nil {
			return fmt.Errorf("failed to increment unread count: %w", err)
		}
	}

	return nil
}

// GetConversationMessages retrieves messages for a conversation with pagination
func (c *CassandraClient) GetConversationMessages(ctx context.Context, conversationID gocql.UUID, limit int, pageState []byte) ([]*ConversationMessage, []byte, error) {
	// CRITICAL FIX: Filter out deleted messages in the query
	query := `
		SELECT conversation_id, message_id, sender_id, recipient_id, content, message_type,
			   media_url, reply_to_id, is_edited, is_deleted, is_read, created_at, updated_at
		FROM conversation_messages
		WHERE conversation_id = ? AND is_deleted = false
		ORDER BY created_at DESC
		LIMIT ?`

	iter := c.Session.Query(query, conversationID, limit).PageState(pageState).Iter()
	defer iter.Close()

	var messages []*ConversationMessage
	var message ConversationMessage

	for iter.Scan(&message.ConversationID, &message.MessageID, &message.SenderID,
		&message.RecipientID, &message.Content, &message.MessageType, &message.MediaURL,
		&message.ReplyToID, &message.IsEdited, &message.IsDeleted, &message.IsRead,
		&message.CreatedAt, &message.UpdatedAt) {

		messages = append(messages, &ConversationMessage{
			ConversationID: message.ConversationID,
			MessageID:      message.MessageID,
			SenderID:       message.SenderID,
			RecipientID:    message.RecipientID,
			Content:        message.Content,
			MessageType:    message.MessageType,
			MediaURL:       message.MediaURL,
			ReplyToID:      message.ReplyToID,
			IsEdited:       message.IsEdited,
			IsDeleted:      message.IsDeleted,
			IsRead:         message.IsRead,
			CreatedAt:      message.CreatedAt,
			UpdatedAt:      message.UpdatedAt,
		})
	}

	if err := iter.Close(); err != nil {
		return nil, nil, fmt.Errorf("failed to get conversation messages: %w", err)
	}

	return messages, iter.PageState(), nil
}

// UpdateMessage updates a message (for editing)
// CRITICAL FIX: Now accepts full primary key components to eliminate read-before-write anti-pattern
func (c *CassandraClient) UpdateMessage(ctx context.Context, bubbleID gocql.UUID, createdAt time.Time, messageID gocql.UUID, newContent string) error {
	query := `
		UPDATE messages
		SET content = ?, is_edited = true, updated_at = ?
		WHERE bubble_id = ? AND created_at = ? AND message_id = ?`

	// No preliminary SELECT needed! Direct update with full primary key
	err := c.Session.Query(query, newContent, time.Now(), bubbleID, createdAt, messageID).Exec()
	if err != nil {
		return fmt.Errorf("failed to update message: %w", err)
	}

	return nil
}

// DeleteMessage soft deletes a message with TTL for automatic cleanup
// CRITICAL FIX: Now accepts full primary key components and implements TTL
func (c *CassandraClient) DeleteMessage(ctx context.Context, bubbleID gocql.UUID, createdAt time.Time, messageID gocql.UUID) error {
	// TTL of 30 days (2592000 seconds) for automatic cleanup
	query := `
		UPDATE messages USING TTL 2592000
		SET is_deleted = true, content = 'This message was deleted.', updated_at = ?
		WHERE bubble_id = ? AND created_at = ? AND message_id = ?`

	// No preliminary SELECT needed! Direct update with full primary key
	err := c.Session.Query(query, time.Now(), bubbleID, createdAt, messageID).Exec()
	if err != nil {
		return fmt.Errorf("failed to delete message: %w", err)
	}

	return nil
}

// DeleteConversationMessage soft deletes a conversation message with TTL for automatic cleanup
// CRITICAL FIX: Accepts full primary key components and implements TTL
func (c *CassandraClient) DeleteConversationMessage(ctx context.Context, conversationID gocql.UUID, createdAt time.Time, messageID gocql.UUID) error {
	// TTL of 30 days (2592000 seconds) for automatic cleanup
	query := `
		UPDATE conversation_messages USING TTL 2592000
		SET is_deleted = true, content = 'This message was deleted.', updated_at = ?
		WHERE conversation_id = ? AND created_at = ? AND message_id = ?`

	// No preliminary SELECT needed! Direct update with full primary key
	err := c.Session.Query(query, time.Now(), conversationID, createdAt, messageID).Exec()
	if err != nil {
		return fmt.Errorf("failed to delete conversation message: %w", err)
	}

	return nil
}

// CreateConversation creates a new conversation
func (c *CassandraClient) CreateConversation(ctx context.Context, conversation *Conversation) error {
	// Use IF NOT EXISTS for idempotency
	query := `
		INSERT INTO conversations (conversation_id, participant1_id, participant2_id,
								  last_message_id, last_message_at, created_at)
		VALUES (?, ?, ?, ?, ?, ?)
		IF NOT EXISTS`

	err := c.Session.Query(query,
		conversation.ConversationID, conversation.Participant1ID, conversation.Participant2ID,
		conversation.LastMessageID, conversation.LastMessageAt, conversation.CreatedAt,
	).Exec()

	if err != nil {
		return fmt.Errorf("failed to create conversation: %w", err)
	}

	return nil
}

// GetUserConversations retrieves conversations for a user
func (c *CassandraClient) GetUserConversations(ctx context.Context, userID gocql.UUID, limit int) ([]*Conversation, error) {
	query := `
		SELECT conversation_id, other_user_id, last_message_at, unread_count, is_archived, created_at
		FROM user_conversations
		WHERE user_id = ?
		ORDER BY last_message_at DESC
		LIMIT ?`

	iter := c.Session.Query(query, userID, limit).Iter()
	defer iter.Close()

	var conversations []*Conversation
	var conversationID, otherUserID gocql.UUID
	var lastMessageAt, createdAt time.Time
	var unreadCount int
	var isArchived bool

	for iter.Scan(&conversationID, &otherUserID, &lastMessageAt, &unreadCount, &isArchived, &createdAt) {
		conversation := &Conversation{
			ConversationID: conversationID,
			Participant1ID: userID,
			Participant2ID: otherUserID,
			LastMessageAt:  lastMessageAt,
			CreatedAt:      createdAt,
		}
		conversations = append(conversations, conversation)
	}

	if err := iter.Close(); err != nil {
		return nil, fmt.Errorf("failed to get user conversations: %w", err)
	}

	return conversations, nil
}

// MarkMessageAsRead marks a conversation message as read
// CRITICAL FIX: Now accepts full primary key components and decrements unread counter
func (c *CassandraClient) MarkMessageAsRead(ctx context.Context, conversationID gocql.UUID, createdAt time.Time, messageID gocql.UUID, userID gocql.UUID) error {
	query := `
		UPDATE conversation_messages
		SET is_read = true, updated_at = ?
		WHERE conversation_id = ? AND created_at = ? AND message_id = ?`

	// No preliminary SELECT needed! Direct update with full primary key
	err := c.Session.Query(query, time.Now(), conversationID, createdAt, messageID).Exec()
	if err != nil {
		return fmt.Errorf("failed to mark message as read: %w", err)
	}

	// CRITICAL FIX: Decrement unread counter
	decrementQuery := `UPDATE conversation_unread_counts SET unread_count = unread_count - 1 WHERE conversation_id = ? AND user_id = ?`
	err = c.Session.Query(decrementQuery, conversationID, userID).Exec()
	if err != nil {
		return fmt.Errorf("failed to decrement unread count: %w", err)
	}

	return nil
}

// GetMessageCount gets total message count for a bubble
// CRITICAL FIX: Now uses counter table for lightning-fast reads
func (c *CassandraClient) GetMessageCount(ctx context.Context, bubbleID gocql.UUID) (int64, error) {
	query := `SELECT total_messages FROM bubble_message_counts WHERE bubble_id = ?`

	var count int64
	err := c.Session.Query(query, bubbleID).Scan(&count)
	if err != nil {
		if err == gocql.ErrNotFound {
			return 0, nil // No messages yet
		}
		return 0, fmt.Errorf("failed to get message count: %w", err)
	}

	return count, nil
}

// GetUnreadMessageCount gets unread message count for a user in a conversation
// CRITICAL FIX: Now uses counter table for lightning-fast reads
func (c *CassandraClient) GetUnreadMessageCount(ctx context.Context, conversationID gocql.UUID, userID gocql.UUID) (int64, error) {
	query := `SELECT unread_count FROM conversation_unread_counts WHERE conversation_id = ? AND user_id = ?`

	var count int64
	err := c.Session.Query(query, conversationID, userID).Scan(&count)
	if err != nil {
		if err == gocql.ErrNotFound {
			return 0, nil // No unread messages
		}
		return 0, fmt.Errorf("failed to get unread message count: %w", err)
	}

	return count, nil
}

// GetConversationByID retrieves a conversation by its ID
func (c *CassandraClient) GetConversationByID(ctx context.Context, conversationID gocql.UUID) (*Conversation, error) {
	query := `
		SELECT conversation_id, participant1_id, participant2_id, last_message_id, last_message_at, created_at
		FROM conversations
		WHERE conversation_id = ?
		LIMIT 1`

	var conv Conversation
	var lastMessageID *gocql.UUID
	var lastMessageAt time.Time
	if err := c.Session.Query(query, conversationID).Scan(&conv.ConversationID, &conv.Participant1ID, &conv.Participant2ID, &lastMessageID, &lastMessageAt, &conv.CreatedAt); err != nil {
		if err == gocql.ErrNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}

	conv.LastMessageID = lastMessageID
	conv.LastMessageAt = lastMessageAt
	return &conv, nil
}

// UpdateConversationLastMessage updates the last message metadata for a conversation
func (c *CassandraClient) UpdateConversationLastMessage(ctx context.Context, conversationID gocql.UUID, lastMessageID gocql.UUID, lastMessageAt time.Time) error {
	query := `
		UPDATE conversations
		SET last_message_id = ?, last_message_at = ?
		WHERE conversation_id = ?`

	return c.Session.Query(query, lastMessageID, lastMessageAt, conversationID).Exec()
}

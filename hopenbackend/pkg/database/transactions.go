package database

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"
)

// TransactionFunc represents a function that executes within a transaction
type TransactionFunc func(tx pgx.Tx) error

// WithTransaction executes a function within a database transaction
// Automatically handles commit/rollback based on the function's return value
func (c *PostgreSQLClient) WithTransaction(ctx context.Context, fn TransactionFunc) error {
	tx, err := c.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	// Ensure rollback is called if commit is not reached
	defer func() {
		if err := tx.Rollback(ctx); err != nil && err != pgx.ErrTxClosed {
			c.logger.Error("Failed to rollback transaction", zap.Error(err))
		}
	}()

	// Execute the function within the transaction
	if err := fn(tx); err != nil {
		return err
	}

	// Commit the transaction
	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// CreateUserWithBubble atomically creates a user and adds them to a bubble
func (c *PostgreSQLClient) CreateUserWithBubble(ctx context.Context, user User, bubbleID string) error {
	return c.WithTransaction(ctx, func(tx pgx.Tx) error {
		// Create the user
		userQuery := `
			INSERT INTO users (
				username, email, first_name, last_name, display_name,
				date_of_birth, avatar_bucket_name, avatar_object_key,
				is_premium, is_private, notification_settings
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
			RETURNING id, created_at, updated_at
		`

		var userID string
		err := tx.QueryRow(ctx, userQuery,
			user.Username, user.Email, user.FirstName, user.LastName, user.DisplayName,
			user.DateOfBirth, user.AvatarBucketName, user.AvatarObjectKey,
			user.IsPremium, user.IsPrivate, user.NotificationSettings,
		).Scan(&user.ID, &user.CreatedAt, &user.UpdatedAt)
		if err != nil {
			return fmt.Errorf("failed to create user: %w", err)
		}

		userID = user.ID

		// Add user to bubble
		bubbleMemberQuery := `
			INSERT INTO bubble_members (bubble_id, user_id, status)
			VALUES ($1, $2, 'active')
		`

		_, err = tx.Exec(ctx, bubbleMemberQuery, bubbleID, userID)
		if err != nil {
			return fmt.Errorf("failed to add user to bubble: %w", err)
		}

		// Update bubble member count
		updateBubbleQuery := `
			UPDATE bubbles 
			SET member_count = member_count + 1,
			    updated_at = NOW()
			WHERE id = $1
		`

		_, err = tx.Exec(ctx, updateBubbleQuery, bubbleID)
		if err != nil {
			return fmt.Errorf("failed to update bubble member count: %w", err)
		}

		c.logger.Info("Created user and added to bubble",
			zap.String("user_id", userID),
			zap.String("bubble_id", bubbleID))

		return nil
	})
}

// CreateContactRelationship atomically creates a bidirectional contact relationship
func (c *PostgreSQLClient) CreateContactRelationship(ctx context.Context, userID1, userID2, createdBy string) error {
	return c.WithTransaction(ctx, func(tx pgx.Tx) error {
		// Create relationship from user1 to user2
		_, err := tx.Exec(ctx, `
			INSERT INTO user_relationships (
				from_user_id, to_user_id, relationship_type, 
				status, created_by, reason
			) VALUES ($1, $2, 'contact', 'active', $3, 'Contact request accepted')
		`, userID1, userID2, createdBy)
		if err != nil {
			return fmt.Errorf("failed to create relationship from %s to %s: %w", userID1, userID2, err)
		}

		// Create relationship from user2 to user1
		_, err = tx.Exec(ctx, `
			INSERT INTO user_relationships (
				from_user_id, to_user_id, relationship_type, 
				status, created_by, reason
			) VALUES ($1, $2, 'contact', 'active', $3, 'Contact request accepted')
		`, userID2, userID1, createdBy)
		if err != nil {
			return fmt.Errorf("failed to create relationship from %s to %s: %w", userID2, userID1, err)
		}

		c.logger.Info("Created bidirectional contact relationship",
			zap.String("user1_id", userID1),
			zap.String("user2_id", userID2),
			zap.String("created_by", createdBy))

		return nil
	})
}

// RemoveUserFromBubble atomically removes a user from a bubble and updates counts
func (c *PostgreSQLClient) RemoveUserFromBubble(ctx context.Context, userID, bubbleID string) error {
	return c.WithTransaction(ctx, func(tx pgx.Tx) error {
		// Update bubble member status to 'left'
		_, err := tx.Exec(ctx, `
			UPDATE bubble_members 
			SET status = 'left', 
			    left_at = NOW(),
			    updated_at = NOW()
			WHERE user_id = $1 AND bubble_id = $2 AND status = 'active'
		`, userID, bubbleID)
		if err != nil {
			return fmt.Errorf("failed to update bubble member status: %w", err)
		}

		// Update bubble member count
		_, err = tx.Exec(ctx, `
			UPDATE bubbles 
			SET member_count = member_count - 1,
			    updated_at = NOW()
			WHERE id = $1 AND member_count > 0
		`, bubbleID)
		if err != nil {
			return fmt.Errorf("failed to update bubble member count: %w", err)
		}

		c.logger.Info("Removed user from bubble",
			zap.String("user_id", userID),
			zap.String("bubble_id", bubbleID))

		return nil
	})
}

// BlockUser atomically blocks a user and removes any existing relationships
func (c *PostgreSQLClient) BlockUser(ctx context.Context, blockerID, blockedID string) error {
	return c.WithTransaction(ctx, func(tx pgx.Tx) error {
		// Deactivate any existing relationships
		_, err := tx.Exec(ctx, `
			UPDATE user_relationships 
			SET status = 'inactive',
			    updated_at = NOW()
			WHERE ((from_user_id = $1 AND to_user_id = $2) 
			       OR (from_user_id = $2 AND to_user_id = $1))
			AND status = 'active'
		`, blockerID, blockedID)
		if err != nil {
			return fmt.Errorf("failed to deactivate existing relationships: %w", err)
		}

		// Create block relationship
		_, err = tx.Exec(ctx, `
			INSERT INTO user_relationships (
				from_user_id, to_user_id, relationship_type, 
				status, created_by, reason
			) VALUES ($1, $2, 'block', 'active', $1, 'User blocked')
		`, blockerID, blockedID)
		if err != nil {
			return fmt.Errorf("failed to create block relationship: %w", err)
		}

		c.logger.Info("Blocked user",
			zap.String("blocker_id", blockerID),
			zap.String("blocked_id", blockedID))

		return nil
	})
}

// PromoteToFriend atomically promotes a contact relationship to friend
func (c *PostgreSQLClient) PromoteToFriend(ctx context.Context, userID1, userID2 string) error {
	return c.WithTransaction(ctx, func(tx pgx.Tx) error {
		// Update existing contact relationships to friend
		_, err := tx.Exec(ctx, `
			UPDATE user_relationships 
			SET relationship_type = 'friend',
			    updated_at = NOW()
			WHERE ((from_user_id = $1 AND to_user_id = $2) 
			       OR (from_user_id = $2 AND to_user_id = $1))
			AND relationship_type = 'contact'
			AND status = 'active'
		`, userID1, userID2)
		if err != nil {
			return fmt.Errorf("failed to promote contact to friend: %w", err)
		}

		c.logger.Info("Promoted contact to friend",
			zap.String("user1_id", userID1),
			zap.String("user2_id", userID2))

		return nil
	})
}

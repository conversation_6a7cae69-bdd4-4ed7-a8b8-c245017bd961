package contact

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"

	"hopenbackend/pkg/database"
)

// PostgreSQLRepository implements contact operations using PostgreSQL
type PostgreSQLRepository struct {
	db     *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLRepository creates a new PostgreSQL repository for contacts
func NewPostgreSQLRepository(db *pgxpool.Pool, logger *zap.Logger) *PostgreSQLRepository {
	return &PostgreSQLRepository{
		db:     db,
		logger: logger,
	}
}

// Contact represents a contact relationship in PostgreSQL
type Contact struct {
	ID          string     `json:"id"`
	RequesterID string     `json:"requester_id"`
	RecipientID string     `json:"recipient_id"`
	Status      string     `json:"status"` // pending, accepted, declined
	Message     *string    `json:"message,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	AcceptedAt  *time.Time `json:"accepted_at,omitempty"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// CreateContact creates a new contact request
func (r *PostgreSQLRepository) CreateContact(ctx context.Context, contact *Contact) error {
	contact.ID = uuid.New().String()
	contact.CreatedAt = time.Now()
	contact.UpdatedAt = time.Now()

	query := `
		INSERT INTO contacts (id, requester_id, recipient_id, status, message, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)`

	_, err := r.db.Exec(ctx, query,
		contact.ID,
		contact.RequesterID,
		contact.RecipientID,
		contact.Status,
		contact.Message,
		contact.CreatedAt,
		contact.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("Failed to create contact", zap.Error(err))
		return database.HandlePgxError(err, "contacts")
	}

	return nil
}

// GetContactByID retrieves a contact by ID
func (r *PostgreSQLRepository) GetContactByID(ctx context.Context, contactID string) (*Contact, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, message, created_at, accepted_at, updated_at
		FROM contacts
		WHERE id = $1`

	var contact Contact
	var acceptedAt sql.NullTime

	err := r.db.QueryRow(ctx, query, contactID).Scan(
		&contact.ID,
		&contact.RequesterID,
		&contact.RecipientID,
		&contact.Status,
		&contact.Message,
		&contact.CreatedAt,
		&acceptedAt,
		&contact.UpdatedAt,
	)

	if err != nil {
		return nil, database.HandlePgxError(err, "contacts")
	}

	if acceptedAt.Valid {
		contact.AcceptedAt = &acceptedAt.Time
	}

	return &contact, nil
}

// UpdateContactStatus updates the status of a contact request
func (r *PostgreSQLRepository) UpdateContactStatus(ctx context.Context, contactID, status string) error {
	now := time.Now()
	var acceptedAt *time.Time
	if status == "accepted" {
		acceptedAt = &now
	}

	query := `
		UPDATE contacts 
		SET status = $1, accepted_at = $2, updated_at = $3
		WHERE id = $4`

	result, err := r.db.Exec(ctx, query, status, acceptedAt, now, contactID)
	if err != nil {
		return fmt.Errorf("failed to update contact status: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("contact not found")
	}

	return nil
}

// ContactRelationshipExists checks if a contact relationship already exists between two users
func (r *PostgreSQLRepository) ContactRelationshipExists(ctx context.Context, userID1, userID2 string) (bool, error) {
	query := `
		SELECT EXISTS(
			SELECT 1 FROM contacts
			WHERE ((requester_id = $1 AND recipient_id = $2) OR 
			       (requester_id = $2 AND recipient_id = $1))
			AND status IN ('pending', 'accepted')
		)`

	var exists bool
	err := r.db.QueryRow(ctx, query, userID1, userID2).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check contact relationship existence: %w", err)
	}

	return exists, nil
}

// GetContactsByUser retrieves all contact relationships for a user
func (r *PostgreSQLRepository) GetContactsByUser(ctx context.Context, userID string) ([]*Contact, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, message, created_at, accepted_at, updated_at
		FROM contacts
		WHERE (requester_id = $1 OR recipient_id = $1)
		AND status IN ('accepted', 'pending')
		ORDER BY created_at DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query contacts: %w", err)
	}
	defer rows.Close()

	var contacts []*Contact
	for rows.Next() {
		var contact Contact
		var acceptedAt sql.NullTime

		err := rows.Scan(
			&contact.ID,
			&contact.RequesterID,
			&contact.RecipientID,
			&contact.Status,
			&contact.Message,
			&contact.CreatedAt,
			&acceptedAt,
			&contact.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan contact row", zap.Error(err))
			continue
		}

		if acceptedAt.Valid {
			contact.AcceptedAt = &acceptedAt.Time
		}

		contacts = append(contacts, &contact)
	}

	return contacts, nil
}

// GetSentContactRequests retrieves pending contact requests sent by a user
func (r *PostgreSQLRepository) GetSentContactRequests(ctx context.Context, userID string) ([]*Contact, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, message, created_at, accepted_at, updated_at
		FROM contacts
		WHERE requester_id = $1 AND status = 'pending'
		ORDER BY created_at DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query sent contact requests: %w", err)
	}
	defer rows.Close()

	var contacts []*Contact
	for rows.Next() {
		var contact Contact
		var acceptedAt sql.NullTime

		err := rows.Scan(
			&contact.ID,
			&contact.RequesterID,
			&contact.RecipientID,
			&contact.Status,
			&contact.Message,
			&contact.CreatedAt,
			&acceptedAt,
			&contact.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan contact row", zap.Error(err))
			continue
		}

		if acceptedAt.Valid {
			contact.AcceptedAt = &acceptedAt.Time
		}

		contacts = append(contacts, &contact)
	}

	return contacts, nil
}

// GetReceivedContactRequests retrieves contact requests received by a user
func (r *PostgreSQLRepository) GetReceivedContactRequests(ctx context.Context, userID string) ([]*Contact, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, message, created_at, accepted_at, updated_at
		FROM contacts
		WHERE recipient_id = $1 AND status = 'pending'
		ORDER BY created_at DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query received contact requests: %w", err)
	}
	defer rows.Close()

	var contacts []*Contact
	for rows.Next() {
		var contact Contact
		var acceptedAt sql.NullTime

		err := rows.Scan(
			&contact.ID,
			&contact.RequesterID,
			&contact.RecipientID,
			&contact.Status,
			&contact.Message,
			&contact.CreatedAt,
			&acceptedAt,
			&contact.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan contact row", zap.Error(err))
			continue
		}

		if acceptedAt.Valid {
			contact.AcceptedAt = &acceptedAt.Time
		}

		contacts = append(contacts, &contact)
	}

	return contacts, nil
}

// GetMutualContacts retrieves mutual contacts between two users
func (r *PostgreSQLRepository) GetMutualContacts(ctx context.Context, userID1, userID2 string) ([]string, error) {
	query := `
		WITH user1_contacts AS (
			SELECT CASE
				WHEN requester_id = $1 THEN recipient_id
				ELSE requester_id
			END AS contact_id
			FROM contacts
			WHERE (requester_id = $1 OR recipient_id = $1) AND status = 'accepted'
		),
		user2_contacts AS (
			SELECT CASE
				WHEN requester_id = $2 THEN recipient_id
				ELSE requester_id
			END AS contact_id
			FROM contacts
			WHERE (requester_id = $2 OR recipient_id = $2) AND status = 'accepted'
		)
		SELECT u1.contact_id
		FROM user1_contacts u1
		INNER JOIN user2_contacts u2 ON u1.contact_id = u2.contact_id`

	rows, err := r.db.Query(ctx, query, userID1, userID2)
	if err != nil {
		return nil, fmt.Errorf("failed to query mutual contacts: %w", err)
	}
	defer rows.Close()

	var contactIDs []string
	for rows.Next() {
		var contactID string
		if err := rows.Scan(&contactID); err != nil {
			r.logger.Error("Failed to scan mutual contact row", zap.Error(err))
			continue
		}
		contactIDs = append(contactIDs, contactID)
	}

	return contactIDs, nil
}

// DeleteContact deletes a contact request (for cancellation)
func (r *PostgreSQLRepository) DeleteContact(ctx context.Context, contactID string) error {
	query := `DELETE FROM contacts WHERE id = $1`

	result, err := r.db.Exec(ctx, query, contactID)
	if err != nil {
		return fmt.Errorf("failed to delete contact: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("contact not found")
	}

	return nil
}

// SearchContacts performs optimized contact search using user_relationships table
func (r *PostgreSQLRepository) SearchContacts(ctx context.Context, filter ContactSearchFilter) ([]ContactSearchResult, error) {
	// Import the optimized search logic from database layer
	// This should be moved here from contact_search.go

	// Build the optimized query with proper indexes
	query := `
		WITH user_bubble_status AS (
			SELECT
				u.id,
				CASE
					WHEN bm.user_id IS NULL THEN 'no_bubble'
					WHEN b.member_count >= b.capacity THEN 'full_bubble'
					ELSE 'not_full_bubble'
				END as bubble_status
			FROM users u
			LEFT JOIN bubble_members bm ON u.id = bm.user_id
				AND bm.status = 'active'
			LEFT JOIN bubbles b ON bm.bubble_id = b.id
				AND b.status = 'active'
			WHERE u.is_active = true AND u.is_banned = false
		),
		user_relationships_view AS (
			SELECT
				u.id,
				COALESCE(
					(SELECT relationship_type
					 FROM user_relationships ur
					 WHERE ((ur.from_user_id = $1 AND ur.to_user_id = u.id)
					        OR (ur.from_user_id = u.id AND ur.to_user_id = $1))
					 AND ur.status = 'active'
					 ORDER BY ur.created_at DESC
					 LIMIT 1),
					'none'
				) as relationship_type
			FROM users u
			WHERE u.is_active = true AND u.is_banned = false
		)
		SELECT
			u.id,
			u.username,
			u.first_name,
			u.last_name,
			u.display_name,
			u.avatar_bucket_name,
			u.avatar_object_key,
			u.avatar_url,
			u.is_present,
			u.last_active_at,
			ubs.bubble_status,
			urv.relationship_type,
			CASE
				WHEN u.is_present = true
				AND u.last_active_at > NOW() - INTERVAL '5 minutes'
				THEN true
				ELSE false
			END as is_online
		FROM users u
		INNER JOIN user_bubble_status ubs ON u.id = ubs.id
		INNER JOIN user_relationships_view urv ON u.id = urv.id
		WHERE u.is_active = true
		AND u.is_banned = false
		AND u.id != $1  -- Exclude the requester
	`

	args := []interface{}{filter.RequesterUserID}
	argIndex := 2

	// Add text search filter if query provided
	if filter.Query != "" {
		query += fmt.Sprintf(` AND (
			u.first_name ILIKE $%d OR
			u.last_name ILIKE $%d OR
			u.username ILIKE $%d OR
			u.display_name ILIKE $%d OR
			(u.first_name || ' ' || u.last_name) ILIKE $%d
		)`, argIndex, argIndex, argIndex, argIndex, argIndex)
		searchTerm := "%" + filter.Query + "%"
		args = append(args, searchTerm)
		argIndex++
	}

	// Add bubble status filter
	if len(filter.BubbleStatuses) > 0 {
		placeholders := make([]string, len(filter.BubbleStatuses))
		for i, status := range filter.BubbleStatuses {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, status)
			argIndex++
		}
		query += fmt.Sprintf(" AND ubs.bubble_status IN (%s)", strings.Join(placeholders, ","))
	}

	// Add relationship type filter
	if len(filter.RelationshipTypes) > 0 {
		placeholders := make([]string, len(filter.RelationshipTypes))
		for i, relType := range filter.RelationshipTypes {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, relType)
			argIndex++
		}
		query += fmt.Sprintf(" AND urv.relationship_type IN (%s)", strings.Join(placeholders, ","))
	}

	// Exclude blocked users unless explicitly requested
	if !filter.IncludeBlocked {
		query += " AND urv.relationship_type != 'block'"
	}

	// Add ordering for optimal user experience
	query += `
		ORDER BY
			CASE WHEN u.is_present = true THEN 0 ELSE 1 END,
			u.last_active_at DESC,
			u.created_at DESC
	`

	// Add pagination
	if filter.Limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, filter.Limit)
		argIndex++
	}

	if filter.Offset > 0 {
		query += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, filter.Offset)
	}

	r.logger.Debug("Executing contact search query",
		zap.String("query", query),
		zap.Any("args", args))

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute contact search query: %w", err)
	}
	defer rows.Close()

	var results []ContactSearchResult
	for rows.Next() {
		var result ContactSearchResult
		err := rows.Scan(
			&result.ID,
			&result.Username,
			&result.FirstName,
			&result.LastName,
			&result.DisplayName,
			&result.AvatarBucketName,
			&result.AvatarObjectKey,
			&result.AvatarURL,
			&result.IsPresent,
			&result.LastActiveAt,
			&result.BubbleStatus,
			&result.RelationshipType,
			&result.IsOnline,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan contact search result: %w", err)
		}
		results = append(results, result)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating contact search results: %w", err)
	}

	r.logger.Info("Contact search completed",
		zap.Int("results_count", len(results)),
		zap.String("query", filter.Query))

	return results, nil
}

// ExpireOldRequests marks old pending contact requests as expired
func (r *PostgreSQLRepository) ExpireOldRequests(ctx context.Context, olderThan time.Time) (int, error) {
	query := `
		UPDATE contacts
		SET status = 'declined', updated_at = NOW()
		WHERE status = 'pending' AND created_at < $1`

	result, err := r.db.Exec(ctx, query, olderThan)
	if err != nil {
		return 0, fmt.Errorf("failed to expire old requests: %w", err)
	}

	return int(result.RowsAffected()), nil
}

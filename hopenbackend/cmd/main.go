package main

import (
	"context"
	"crypto/tls"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/jackc/pgx/v5/pgxpool"
	_ "github.com/lib/pq"
	"github.com/quic-go/quic-go/http3"
	"golang.org/x/crypto/acme/autocert"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"hopenbackend/internal/enterprise/gateway"
	"hopenbackend/internal/enterprise/monitoring"
	"hopenbackend/internal/enterprise/resilience"
	"hopenbackend/microservices/auth"
	"hopenbackend/microservices/bubble"
	"hopenbackend/microservices/call"
	"hopenbackend/microservices/contact"
	"hopenbackend/microservices/friendship"
	"hopenbackend/microservices/media"
	"hopenbackend/microservices/notification"
	"hopenbackend/microservices/presence"
	"hopenbackend/microservices/realtime"
	"hopenbackend/microservices/social_analytics"
	"hopenbackend/microservices/sync"
	"hopenbackend/microservices/user"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/middleware"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
)

// Application holds all the dependencies
type Application struct {
	config *config.Config
	logger *zap.Logger

	// Database clients
	postgresql *database.PostgreSQLClient
	cassandra  *database.CassandraClient

	// External services
	rateLimiter *ratelimit.RateLimiter
	natsConn    *nats.Conn
	mqttClient  mqtt.Client
	redisClient *redis.Client
	oryClient   *ory.Client

	// Enterprise modules
	securityMiddleware *middleware.SecurityMiddleware
	circuitBreakers    *resilience.CircuitBreakerManager
	socialMetrics      *monitoring.SocialMetrics
	apiGateway         *gateway.APIGateway

	// Microservices
	authService            *auth.Service
	userService            *user.Service
	bubbleService          *bubble.Service
	contactService         *contact.Service
	friendshipService      *friendship.Service
	socialAnalyticsService *social_analytics.Service
	callService            *call.Service
	notificationService    *notification.Service
	presenceService        *presence.Service
	realtimeService        *realtime.Service
	mediaService           *media.Service
	syncService            *sync.Service

	router      *gin.Engine
	server      *http.Server
	http3Server *http3.Server
}

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("Failed to load config: %v", err))
	}

	// Initialize logger
	logger, err := initLogger(cfg)
	if err != nil {
		panic(fmt.Sprintf("Failed to initialize logger: %v", err))
	}
	defer logger.Sync()

	logger.Info("Starting Hopen Backend",
		zap.String("version", cfg.App.Version),
		zap.String("environment", cfg.App.Environment),
		zap.Int("port", cfg.App.Port),
	)

	// Create application instance
	app := &Application{
		config: cfg,
		logger: logger,
	}

	// Initialize application
	if err := app.initialize(); err != nil {
		logger.Fatal("Failed to initialize application", zap.Error(err))
	}

	// Start server
	if err := app.start(); err != nil {
		logger.Fatal("Failed to start server", zap.Error(err))
	}

	// Wait for shutdown signal
	app.waitForShutdown()
}

// initialize sets up all application dependencies
func (app *Application) initialize() error {
	ctx := context.Background()

	// Initialize database connections
	if err := app.initializeDatabases(ctx); err != nil {
		return fmt.Errorf("failed to initialize databases: %w", err)
	}

	// Initialize external services
	if err := app.initializeExternalServices(); err != nil {
		return fmt.Errorf("failed to initialize external services: %w", err)
	}

	// Initialize enterprise modules
	if err := app.initializeEnterpriseModules(); err != nil {
		return fmt.Errorf("failed to initialize enterprise modules: %w", err)
	}

	// Initialize microservices
	if err := app.initializeMicroservices(); err != nil {
		return fmt.Errorf("failed to initialize microservices: %w", err)
	}

	// Setup router
	app.setupRouter()

	return nil
}

// initializeDatabases sets up database connections
func (app *Application) initializeDatabases(ctx context.Context) error {
	// PostgreSQL
	postgresql, err := database.NewPostgreSQLClient(&app.config.Databases.PostgreSQL, app.logger)
	if err != nil {
		return fmt.Errorf("failed to create PostgreSQL client: %w", err)
	}
	app.postgresql = postgresql

	// Run database migrations
	app.logger.Info("Running database migrations...")
	if err := runMigrations(postgresql.Pool, app.logger); err != nil {
		return fmt.Errorf("failed to run database migrations: %w", err)
	}

	// ArangoDB - REMOVED (migrated to PostgreSQL)

	// Cassandra
	cassandra, err := database.NewCassandraClient(&app.config.Databases.Cassandra, app.logger)
	if err != nil {
		return fmt.Errorf("failed to create Cassandra client: %w", err)
	}
	app.cassandra = cassandra

	// Initialize Cassandra keyspace
	if err := app.cassandra.InitializeKeyspace(ctx); err != nil {
		return fmt.Errorf("failed to initialize Cassandra keyspace: %w", err)
	}

	return nil
}

// initializeExternalServices sets up external service connections
func (app *Application) initializeExternalServices() error {
	app.logger.Info("🚀🚀🚀 EXTERNAL SERVICES INIT STARTED 🚀🚀🚀")
	// Redis client for caching and idempotency
	redisOptions := &redis.Options{
		Addr:     fmt.Sprintf("%s:%d", app.config.Valkey.Host, app.config.Valkey.Port),
		Password: app.config.Valkey.Password,
		DB:       app.config.Valkey.Database,
	}
	app.redisClient = redis.NewClient(redisOptions)

	// Test Redis connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := app.redisClient.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	// Rate limiter
	rateLimiter, err := ratelimit.NewRateLimiter(&app.config.Valkey, app.logger)
	if err != nil {
		return fmt.Errorf("failed to create rate limiter: %w", err)
	}
	app.rateLimiter = rateLimiter

	// NATS connection for event-driven architecture
	app.logger.Info("Connecting to NATS...")
	natsURL := fmt.Sprintf("nats://%s:%d", app.config.NATS.Host, app.config.NATS.Port)
	natsConn, err := nats.Connect(natsURL,
		nats.UserInfo(app.config.NATS.Username, app.config.NATS.Password),
		nats.ReconnectWait(time.Second*2),
		nats.MaxReconnects(10),
		nats.DisconnectErrHandler(func(nc *nats.Conn, err error) {
			app.logger.Warn("NATS disconnected", zap.Error(err))
		}),
		nats.ReconnectHandler(func(nc *nats.Conn) {
			app.logger.Info("NATS reconnected", zap.String("url", nc.ConnectedUrl()))
		}),
	)
	if err != nil {
		return fmt.Errorf("failed to connect to NATS: %w", err)
	}
	app.natsConn = natsConn

	// MQTT client for real-time messaging
	app.logger.Info("Initializing MQTT client...")
	if err := app.initializeMQTT(); err != nil {
		app.logger.Warn("Failed to initialize MQTT client", zap.Error(err))
		// Don't fail startup if MQTT is not available
	}

	// Ory client for authentication and authorization
	app.logger.Info("Creating Ory client...")
	oryDeps := &ory.Dependencies{
		Logger: app.logger,
		Config: app.config,
	}
	app.oryClient = ory.New(oryDeps)
	app.logger.Info("Ory client created successfully")

	// Log the configuration to debug
	app.logger.Info("Ory configuration check",
		zap.String("kratos_public_url", app.config.Ory.KratosPublicURL),
		zap.String("kratos_admin_url", app.config.Ory.KratosAdminURL))

	app.logger.Info("External services initialized successfully")
	return nil
}

// initializeMQTT initializes the MQTT client for real-time messaging
func (app *Application) initializeMQTT() error {
	// Check if MQTT is configured
	if app.config.MQTT.Broker == "" {
		app.logger.Warn("MQTT broker not configured, real-time messaging disabled")
		return nil
	}

	opts := mqtt.NewClientOptions()
	opts.AddBroker(app.config.MQTT.Broker)
	opts.SetClientID(fmt.Sprintf("hopen-backend-%d", time.Now().Unix()))
	opts.SetUsername(app.config.MQTT.Username)
	opts.SetPassword(app.config.MQTT.Password)
	opts.SetKeepAlive(time.Duration(app.config.MQTT.KeepAlive) * time.Second)
	opts.SetCleanSession(true)

	opts.SetOnConnectHandler(func(client mqtt.Client) {
		app.logger.Info("Backend connected to MQTT broker")
	})

	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		app.logger.Error("Backend lost connection to MQTT broker", zap.Error(err))
	})

	app.mqttClient = mqtt.NewClient(opts)
	if token := app.mqttClient.Connect(); token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to connect to MQTT broker: %w", token.Error())
	}

	app.logger.Info("MQTT client initialized successfully")
	return nil
}

// initializeEnterpriseModules sets up enterprise modules
func (app *Application) initializeEnterpriseModules() error {
	// Security middleware
	securityConfig := &middleware.SecurityConfig{
		CSPPolicy:         app.config.Enterprise.Security.CSPPolicy,
		HSTSMaxAge:        app.config.Enterprise.Security.HSTSMaxAge,
		MaxRequestSize:    app.config.Enterprise.Security.MaxRequestSize,
		EnableContentScan: app.config.Enterprise.Security.EnableContentScan,
	}
	app.securityMiddleware = middleware.NewSecurityMiddleware(securityConfig)

	// Circuit breakers
	app.circuitBreakers = resilience.NewCircuitBreakerManager(app.config, app.logger)

	// Social metrics
	app.socialMetrics = monitoring.NewSocialMetrics()

	// API Gateway
	app.apiGateway = gateway.New(app.config, app.logger)

	// Database pool manager and idempotency manager removed for now

	return nil
}

// initializeMicroservices sets up all microservices
func (app *Application) initializeMicroservices() error {
	// Auth service
	authDeps := &auth.Dependencies{
		Logger:    app.logger,
		Config:    app.config,
		DB:        app.postgresql,
		OryClient: app.oryClient,
	}
	app.authService = auth.New(authDeps)

	// User service
	userDeps := &user.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
	}
	app.userService = user.New(userDeps)

	// Notification service (needed by other services)
	notificationDeps := &notification.Dependencies{
		Logger:    app.logger,
		DB:        app.postgresql,
		Config:    app.config,
		OryClient: app.oryClient,
	}
	app.notificationService = notification.New(notificationDeps)

	// Presence service
	app.logger.Info("🚀🚀🚀 ABOUT TO INITIALIZE PRESENCE SERVICE 🚀🚀🚀")
	app.presenceService = presence.NewService(
		app.redisClient,
		app.config,
		app.logger,
	)
	if err := app.presenceService.Initialize(); err != nil {
		app.logger.Error("Failed to initialize presence service", zap.Error(err))
		return fmt.Errorf("failed to initialize presence service: %w", err)
	}

	// Bubble service (now handles ALL membership operations)
	app.bubbleService = bubble.NewService(
		app.postgresql,
		app.oryClient,
		app.natsConn,
		app.logger,
		app.notificationService,
	)

	// Contact service
	contactDeps := &contact.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
		NATSConn:    app.natsConn,
		MQTTClient:  app.mqttClient,
	}
	app.contactService = contact.New(contactDeps)

	// Bubble analytics service - REMOVED (no longer needed without ArangoDB)

	// Friendship service
	friendshipDeps := &friendship.Dependencies{
		Logger:              app.logger,
		DB:                  app.postgresql,
		Config:              app.config,
		RateLimiter:         app.rateLimiter,
		NATS:                app.natsConn,
		MQTTClient:          app.mqttClient,
		OryClient:           app.oryClient,
		NotificationService: app.notificationService,
	}
	app.friendshipService = friendship.New(friendshipDeps)

	// Social analytics service
	socialAnalyticsDeps := &social_analytics.Dependencies{
		Logger:     app.logger,
		PostgreSQL: app.postgresql,
		Config:     app.config,
		OryClient:  app.oryClient,
	}
	app.socialAnalyticsService = social_analytics.New(socialAnalyticsDeps)

	// Call service
	callDeps := &call.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
	}
	app.callService = call.New(callDeps)

	// Realtime service
	realtimeDeps := &realtime.Dependencies{
		Logger:      app.logger,
		Cassandra:   app.cassandra,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
	}
	app.realtimeService = realtime.New(realtimeDeps)

	// Media service
	mediaDeps := &media.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
	}
	app.mediaService = media.New(mediaDeps)

	// Sync service (HTTP/3-first bulk data synchronization)
	app.syncService = sync.NewService(
		app.logger,
		app.postgresql,
		app.config,
		app.oryClient,
	)

	return nil
}

// setupRouter configures the Gin router with middleware and routes
func (app *Application) setupRouter() {
	// Set Gin mode
	if app.config.App.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	router := gin.New()

	// Global middleware
	router.Use(
		gin.Recovery(),
		app.corsMiddleware(),
		app.securityMiddleware.SecurityHandler(),
		app.requestIDMiddleware(),
		app.loggingMiddleware(),
		app.rateLimitMiddleware(),
		app.socialMetrics.MetricsMiddleware(),
	)

	// Health check endpoints
	router.GET("/health", app.healthCheck)
	router.HEAD("/health", app.healthCheck) // Add HEAD support for HTTP/3 detection
	router.GET("/ready", app.readinessCheck)
	router.HEAD("/ready", app.readinessCheck) // Add HEAD support
	router.GET("/metrics", app.metricsHandler)

	// API routes
	v1 := router.Group("/api/v1")
	{
		// Auth routes
		auth := v1.Group("/auth")
		app.authService.RegisterRoutes(auth)

		// User routes
		users := v1.Group("/users")
		app.userService.RegisterRoutes(users)

		// Bubble routes
		bubbles := v1.Group("/bubbles")
		app.bubbleService.RegisterRoutes(bubbles)

		// Contact routes
		contact := v1.Group("/contact")
		app.contactService.RegisterRoutes(contact)

		// Bubble membership routes are now handled by bubble service

		// Friendship routes
		friendship := v1.Group("/friendship")
		app.friendshipService.RegisterRoutes(friendship)

		// Social analytics routes
		social := v1.Group("/social")
		app.socialAnalyticsService.RegisterRoutes(social)

		// Call routes
		calls := v1.Group("/calls")
		app.callService.RegisterRoutes(calls)

		// Notification routes
		notifications := v1.Group("/notifications")
		app.notificationService.RegisterRoutes(notifications)

		// Presence routes
		presence := v1.Group("/presence")
		app.presenceService.RegisterRoutes(presence)

		// Realtime routes
		realtime := v1.Group("/realtime")
		app.realtimeService.RegisterRoutes(realtime)

		// Media routes
		media := v1.Group("/media")
		app.mediaService.RegisterRoutes(media)

		// Sync routes (HTTP/3-first bulk data synchronization)
		syncGroup := v1.Group("/sync")
		app.syncService.RegisterRoutes(syncGroup)
	}

	app.router = router
}

// start starts the HTTP server with HTTP/3 support
func (app *Application) start() error {
	addr := fmt.Sprintf(":%d", app.config.App.Port)

	// Configure TLS if enabled
	var tlsConfig *tls.Config
	if app.config.App.TLS.Enabled {
		if app.config.App.TLS.AutoCert {
			// Use Let's Encrypt autocert
			certManager := autocert.Manager{
				Prompt:     autocert.AcceptTOS,
				HostPolicy: autocert.HostWhitelist("localhost"), // Configure your domains
				Cache:      autocert.DirCache(app.config.App.TLS.AutoCertDir),
			}
			tlsConfig = &tls.Config{
				GetCertificate: certManager.GetCertificate,
				// Only allow HTTP/3 and HTTP/2
				NextProtos: []string{"h3", "h2"},
			}
		} else {
			// Use provided certificates
			cert, err := tls.LoadX509KeyPair(app.config.App.TLS.CertFile, app.config.App.TLS.KeyFile)
			if err != nil {
				return fmt.Errorf("failed to load TLS certificates: %w", err)
			}
			tlsConfig = &tls.Config{
				Certificates: []tls.Certificate{cert},
				// Only allow HTTP/3 and HTTP/2
				NextProtos: []string{"h3", "h2"},
			}
		}
	}

	// Create HTTP/1.1 and HTTP/2 server
	app.server = &http.Server{
		Addr:         addr,
		Handler:      app.router,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
		TLSConfig:    tlsConfig,
	}

	// Start HTTP/3 server if enabled and TLS is configured
	if app.config.App.TLS.Enabled && app.config.App.TLS.HTTP3 && tlsConfig != nil {
		app.http3Server = &http3.Server{
			Addr:      addr,
			Handler:   app.router,
			TLSConfig: tlsConfig,
		}

		app.logger.Info("Starting HTTP/3 server", zap.Int("port", app.config.App.Port))
		go func() {
			if err := app.http3Server.ListenAndServeTLS(app.config.App.TLS.CertFile, app.config.App.TLS.KeyFile); err != nil {
				app.logger.Error("HTTP/3 server failed", zap.Error(err))
			}
		}()
	}

	// Start server based on TLS configuration
	if app.config.App.TLS.Enabled {
		app.logger.Info("Starting HTTPS server with HTTP/2 support", zap.Int("port", app.config.App.Port))
		http2Handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Alt-Svc", "h3=\":8443\"; ma=86400")
			app.router.ServeHTTP(w, r)
		})
		app.server.Handler = http2Handler
		go func() {
			app.logger.Info("HTTPS server listening", zap.String("address", addr))
			if err := app.server.ListenAndServeTLS(app.config.App.TLS.CertFile, app.config.App.TLS.KeyFile); err != nil && err != http.ErrServerClosed {
				app.logger.Fatal("Failed to start HTTPS server", zap.Error(err))
			}
		}()
	} else {
		// Fallback to HTTP server
		app.logger.Info("Starting HTTP server", zap.Int("port", app.config.App.Port))
		go func() {
			app.logger.Info("HTTP server listening", zap.String("address", addr))
			if err := app.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				app.logger.Fatal("Failed to start HTTP server", zap.Error(err))
			}
		}()
	}

	// Give the server a moment to start
	time.Sleep(100 * time.Millisecond)
	app.logger.Info("Server started successfully",
		zap.Int("port", app.config.App.Port),
		zap.Bool("tls_enabled", app.config.App.TLS.Enabled),
		zap.Bool("http3_enabled", app.config.App.TLS.HTTP3),
	)

	// Start periodic bubble dissolution check
	go app.startBubbleDissolutionChecker()

	return nil
}

// waitForShutdown waits for shutdown signal and gracefully shuts down
func (app *Application) waitForShutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	app.logger.Info("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP/1.1 and HTTP/2 server
	if err := app.server.Shutdown(ctx); err != nil {
		app.logger.Error("HTTP server forced to shutdown", zap.Error(err))
	}

	// Shutdown HTTP/3 server if running
	if app.http3Server != nil {
		if err := app.http3Server.Close(); err != nil {
			app.logger.Error("HTTP/3 server forced to shutdown", zap.Error(err))
		}
	}

	// Close database connections
	app.postgresql.Close()
	app.cassandra.Close()

	// Close external service connections
	if app.natsConn != nil {
		app.natsConn.Close()
		app.logger.Info("NATS connection closed")
	}

	if app.mqttClient != nil && app.mqttClient.IsConnected() {
		app.mqttClient.Disconnect(250)
		app.logger.Info("MQTT connection closed")
	}

	if app.redisClient != nil {
		app.redisClient.Close()
		app.logger.Info("Redis connection closed")
	}

	// Bubble analytics service - REMOVED

	// Database pool manager removed for now

	app.logger.Info("Server exited")
}

// Middleware functions

func (app *Application) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		c.Header("Access-Control-Expose-Headers", "X-Total-Count")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

func (app *Application) requestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

func (app *Application) loggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format(time.RFC1123),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	})
}

func (app *Application) rateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get client IP for rate limiting
		clientIP := c.ClientIP()

		// Check rate limit
		allowed, err := app.rateLimiter.Allow(c.Request.Context(), clientIP)
		if err != nil {
			app.logger.Error("Rate limit check failed", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			c.Abort()
			return
		}

		if !allowed {
			c.JSON(http.StatusTooManyRequests, gin.H{"error": "Rate limit exceeded"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// Health check handlers

func (app *Application) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"version":   app.config.App.Version,
	})
}

func (app *Application) readinessCheck(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	checks := map[string]string{
		"postgresql": "healthy",
		"cassandra":  "healthy",
	}

	// Check PostgreSQL
	if err := app.postgresql.Health(ctx); err != nil {
		checks["postgresql"] = "unhealthy"
	}

	// ArangoDB health check - REMOVED

	// Check Cassandra
	if err := app.cassandra.Health(ctx); err != nil {
		checks["cassandra"] = "unhealthy"
	}

	// Determine overall status
	status := "ready"
	for _, check := range checks {
		if check == "unhealthy" {
			status = "not ready"
			break
		}
	}

	statusCode := http.StatusOK
	if status == "not ready" {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, gin.H{
		"status":    status,
		"checks":    checks,
		"timestamp": time.Now().UTC(),
	})
}

func (app *Application) metricsHandler(c *gin.Context) {
	// This would typically serve Prometheus metrics
	// For now, return basic metrics
	c.JSON(http.StatusOK, gin.H{
		"metrics": gin.H{
			"postgresql_connections": app.postgresql.Stats().TotalConns(),
			"uptime_seconds":         time.Since(time.Now()).Seconds(),
		},
	})
}

// initLogger initializes the logger based on configuration
func initLogger(cfg *config.Config) (*zap.Logger, error) {
	var logger *zap.Logger
	var err error

	if cfg.App.Environment == "production" {
		logger, err = zap.NewProduction()
	} else {
		logger, err = zap.NewDevelopment()
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create logger: %w", err)
	}

	return logger, nil
}

// runMigrations executes database migrations using golang-migrate
func runMigrations(dbPool *pgxpool.Pool, logger *zap.Logger) error {
	// Create a separate SQL connection for migrations using lib/pq driver
	// We need to construct the connection string from the pgx pool config
	config := dbPool.Config()
	connConfig := config.ConnConfig

	// Build connection string for lib/pq
	connStr := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		connConfig.User,
		connConfig.Password,
		connConfig.Host,
		connConfig.Port,
		connConfig.Database,
	)

	// Open SQL connection using lib/pq driver
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return fmt.Errorf("failed to open database connection for migrations: %w", err)
	}
	defer db.Close()

	// Create migration driver using the postgres driver
	driver, err := postgres.WithInstance(db, &postgres.Config{})
	if err != nil {
		return fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Point to the migration files
	// The path should be relative to where you run your Go application from
	m, err := migrate.NewWithDatabaseInstance(
		"file://migrations", // source URL
		"postgres",          // database name
		driver,              // database instance
	)
	if err != nil {
		return fmt.Errorf("failed to create migrate instance: %w", err)
	}

	// Run the migrations
	if err := m.Up(); err != nil {
		// ErrNoChange is ok, it means the database is already up-to-date
		if !errors.Is(err, migrate.ErrNoChange) {
			return fmt.Errorf("failed to run migrations: %w", err)
		} else {
			logger.Info("Database schema is up-to-date")
		}
	} else {
		logger.Info("Database migrations applied successfully")
	}

	return nil
}

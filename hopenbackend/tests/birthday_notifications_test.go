package database

import (
	"context"
	"testing"
	"time"

	"hopenbackend/microservices/notification"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

func TestBirthdayNotifications(t *testing.T) {
	// Skip if not in integration test mode
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Load test configuration
	cfg, err := config.Load()
	require.NoError(t, err)

	// Initialize logger
	logger, err := zap.NewDevelopment()
	require.NoError(t, err)
	defer logger.Sync()

	// Initialize database connection
	db, err := database.NewPostgreSQLClient(&cfg.Databases.PostgreSQL, logger)
	require.NoError(t, err)
	defer db.Close()

	// Initialize notification service
	notificationService := notification.New(&notification.Dependencies{
		Logger: logger,
		DB:     db,
		Config: cfg,
	})

	ctx := context.Background()

	t.Run("GetUpcomingBirthdays", func(t *testing.T) {
		// Create test users with birthdays
		user1 := &database.User{
			ID:          "test-user-1",
			Email:       "<EMAIL>",
			DisplayName: stringPtr("Test User 1"),
			DateOfBirth: timePtr(time.Date(1990, 5, 15, 0, 0, 0, 0, time.UTC)),
			IsActive:    true,
		}

		user2 := &database.User{
			ID:          "test-user-2",
			Email:       "<EMAIL>",
			DisplayName: stringPtr("Test User 2"),
			DateOfBirth: timePtr(time.Date(1995, 12, 25, 0, 0, 0, 0, time.UTC)),
			IsActive:    true,
		}

		// Create users in database
		err := db.CreateUser(ctx, user1)
		require.NoError(t, err)
		err = db.CreateUser(ctx, user2)
		require.NoError(t, err)

		// Test getting upcoming birthdays
		birthdays, err := notificationService.GetUpcomingBirthdays(ctx, "test-user-1", 365)
		require.NoError(t, err)

		// Should find user2's birthday
		assert.Len(t, birthdays, 1)
		assert.Equal(t, "test-user-2", birthdays[0]["user_id"])
		assert.Equal(t, "Test User 2", birthdays[0]["display_name"])
		assert.Equal(t, "1995-12-25", birthdays[0]["date_of_birth"])
		assert.False(t, birthdays[0]["is_today"].(bool))
	})

	t.Run("SendBirthdayNotification", func(t *testing.T) {
		// Create a test user to receive the notification
		recipient := &database.User{
			ID:          "test-recipient",
			Email:       "<EMAIL>",
			DisplayName: stringPtr("Test Recipient"),
			DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
			IsActive:    true,
		}

		err := db.CreateUser(ctx, recipient)
		require.NoError(t, err)

		// Send birthday notification
		err = notificationService.SendBirthdayNotification(ctx, "test-user-1", "Test User 1")
		require.NoError(t, err)

		// Verify notification was created
		// Note: In a real test, you would query the notifications table
		// to verify the notification was created with correct data
	})

	t.Run("ProcessBirthdayNotifications", func(t *testing.T) {
		// Test processing birthday notifications
		err := notificationService.ProcessBirthdayNotifications(ctx)
		require.NoError(t, err)
		// Note: In a real test, you would verify that notifications
		// were sent for users whose birthday is today
	})
}

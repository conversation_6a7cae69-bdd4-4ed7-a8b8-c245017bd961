package database

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNotificationsSchemaImprovements tests the improved notifications schema
func TestNotificationsSchemaImprovements(t *testing.T) {
	// This test requires a running PostgreSQL instance
	// Skip if not in integration test environment
	if testing.Short() {
		t.Skip("Skipping integration test")
	}

	// Setup test database connection
	// Note: You'll need to configure this with your test database credentials
	cfg := &config.PostgreSQLConfig{
		Host:     "localhost",
		Port:     5432,
		Database: "hopen_test",
		Username: "test_user",
		Password: "test_password",
		// Add other required config fields
	}

	logger := zap.NewNop() // Use no-op logger for tests
	client, err := NewPostgreSQLClient(cfg, logger)
	require.NoError(t, err)
	defer client.Close()

	ctx := context.Background()

	// Initialize schema
	err = client.InitializeSchema(ctx)
	require.NoError(t, err)

	// Test 1: Create a test user first (required for foreign key)
	testUserID := uuid.New()
	testUser := &User{
		ID:        testUserID.String(),
		Username:  stringPtr("testuser"),
		Email:     "<EMAIL>",
		FirstName: stringPtr("Test"),
		LastName:  stringPtr("User"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		NotificationSettings: map[string]interface{}{
			"email": true,
		},
	}

	err = client.CreateUser(ctx, testUser)
	require.NoError(t, err)

	// Test 2: Create a notification with proper UUID user_id and grouping key
	notificationID := uuid.New()
	query := `
		INSERT INTO notifications (id, user_id, type, title, message, data, is_read, grouping_key)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`

	_, err = client.Pool.Exec(ctx, query,
		notificationID,
		testUserID, // This should work now with UUID type
		"contactRequestDeclined", // This should work with the ENUM type (non-dialog type)
		"Test Notification",
		"This is a test notification",
		map[string]interface{}{"test": "data"},
		false,
		"test_group:123", // Test grouping key
	)
	require.NoError(t, err, "Should be able to insert notification with UUID user_id, ENUM type, and grouping key")

	// Test additional notification types from the comprehensive ENUM (non-dialog types only)
	testNotificationTypes := []string{
		"friendChatMessageReceived",
		"bubblePopReminder7Days",
		"statusUpdates",
		"securityAlerts",
		"bubbleJoinRequestRejected",
		"bubbleInviteRequestRejected",
		"bubble_expiring_soon", // Legacy type for backward compatibility
	}

	for i, notificationType := range testNotificationTypes {
		_, err = client.Pool.Exec(ctx, query,
			uuid.New(),
			testUserID,
			notificationType,
			fmt.Sprintf("Test Notification %d", i+2),
			fmt.Sprintf("This is test notification %d", i+2),
			map[string]interface{}{"test": fmt.Sprintf("data%d", i+2)},
			false,
		)
		require.NoError(t, err, "Should be able to insert notification with type: %s", notificationType)
	}

	// Test 3: Verify the critical performance index works
	// Query that should use the optimized index: user_id, is_read, created_at DESC
	var count int
	err = client.Pool.QueryRow(ctx, `
		SELECT COUNT(*)
		FROM notifications
		WHERE user_id = $1 AND is_read = false
		ORDER BY created_at DESC`,
		testUserID,
	).Scan(&count)
	require.NoError(t, err)
	expectedCount := 1 + len(testNotificationTypes) // Original notification + additional test types
	assert.Equal(t, expectedCount, count, "Should find all test notifications")

	// Test 4: Test foreign key constraint (CASCADE DELETE)
	// Delete the user and verify notification is also deleted
	_, err = client.Pool.Exec(ctx, "DELETE FROM users WHERE id = $1", testUserID)
	require.NoError(t, err)

	// Verify notification was cascade deleted
	err = client.Pool.QueryRow(ctx, `
		SELECT COUNT(*) 
		FROM notifications 
		WHERE user_id = $1`,
		testUserID,
	).Scan(&count)
	require.NoError(t, err)
	assert.Equal(t, 0, count, "Notification should be cascade deleted when user is deleted")

	// Test 5: Test ENUM constraint
	// Try to insert invalid notification type (should fail)
	testUserID2 := uuid.New()
	testUser2 := &User{
		ID:        testUserID2.String(),
		Username:  stringPtr("testuser2"),
		Email:     "<EMAIL>",
		FirstName: stringPtr("Test2"),
		LastName:  stringPtr("User2"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		NotificationSettings: map[string]interface{}{
			"email": true,
		},
	}

	err = client.CreateUser(ctx, testUser2)
	require.NoError(t, err)

	_, err = client.Pool.Exec(ctx, query,
		uuid.New(),
		testUserID2,
		"invalid_notification_type", // This should fail with ENUM constraint
		"Test Notification",
		"This should fail",
		map[string]interface{}{"test": "data"},
		false,
	)
	assert.Error(t, err, "Should fail when inserting invalid notification type")

	// Test that dialog-based notification types are rejected (should fail)
	dialogBasedTypes := []string{
		"contactRequestReceived",    // Has ContactRequestDialog
		"contactRequestAccepted",    // Will have ContactshipEstablishedDialog
		"bubbleInvitationReceived",  // Has BubbleInviteRequestDialog
		"bubbleJoinRequestReceived", // Has BubbleJoinRequestDialog
		"bubbleJoinRequestAccepted", // Shows BubbleMemberJoinedDialog
		"bubbleMemberJoined",        // Has BubbleMemberJoinedDialog
		"bubbleVotekickInitiated",   // Has BubbleKickoutRequestDialog
		"friendshipEstablished",     // Has FriendshipEstablishedDialog
	}

	for _, dialogType := range dialogBasedTypes {
		_, err = client.Pool.Exec(ctx, query,
			uuid.New(),
			testUserID2,
			dialogType,
			"Dialog-based notification",
			"This should fail because it has a dedicated dialog",
			map[string]interface{}{"test": "data"},
			false,
		)
		assert.Error(t, err, "Should fail when inserting dialog-based notification type: %s", dialogType)
	}

	// Test 6: Test unique constraint for grouped notifications
	// Try to insert another unread notification with the same grouping key (should fail)
	_, err = client.Pool.Exec(ctx, query,
		uuid.New(),
		testUserID2,
		"bubbleJoinRequestRejected",
		"Another Test Notification",
		"This should fail due to unique constraint",
		map[string]interface{}{"test": "data"},
		false,
		"test_group:123", // Same grouping key as before
	)
	assert.Error(t, err, "Should fail when inserting duplicate unread grouped notification")

	// Test 7: Test that the constraint allows read notifications with same grouping key
	_, err = client.Pool.Exec(ctx, query,
		uuid.New(),
		testUserID2,
		"bubbleJoinRequestRejected",
		"Read Notification",
		"This should work because it's marked as read",
		map[string]interface{}{"test": "data"},
		true, // This is read, so constraint shouldn't apply
		"test_group:123", // Same grouping key
	)
	assert.NoError(t, err, "Should allow read notifications with same grouping key")

	// Cleanup
	_, _ = client.Pool.Exec(ctx, "DELETE FROM users WHERE id = $1", testUserID2)
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func timePtr(t time.Time) *time.Time {
	return &t
}

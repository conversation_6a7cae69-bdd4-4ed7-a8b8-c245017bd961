package main

import (
	"context"
	"flag"
	"log"
	"os"
	"time"

	"hopenbackend/microservices/notification"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"

	"go.uber.org/zap"
)

func main() {
	// Parse command line flags
	flag.Parse()

	// Initialize logger
	logger, err := zap.NewProduction()
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Sync()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.Fatal("Failed to load configuration", zap.Error(err))
	}

	// Initialize database connection
	db, err := database.NewPostgreSQLClient(&cfg.Databases.PostgreSQL, logger)
	if err != nil {
		logger.Fatal("Failed to connect to database", zap.Error(err))
	}
	defer db.Close()

	// Initialize notification service
	notificationService := notification.New(&notification.Dependencies{
		Logger: logger,
		DB:     db,
		Config: cfg,
	})

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	logger.Info("Starting birthday notification processing")

	// Process birthday notifications
	err = notificationService.ProcessBirthdayNotifications(ctx)
	if err != nil {
		logger.Error("Failed to process birthday notifications", zap.Error(err))
		os.Exit(1)
	}

	logger.Info("Birthday notification processing completed successfully")
}
